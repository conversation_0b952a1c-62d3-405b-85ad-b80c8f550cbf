<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.huafagroup</groupId>
        <artifactId>base-transaction</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>base-transaction-web</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>




    <dependencies>
        <!--<dependency>-->
            <!--<groupId>org.springframework.boot</groupId>-->
            <!--<artifactId>spring-boot-starter-tomcat</artifactId>-->
            <!--<scope>provided</scope>-->
        <!--</dependency>-->


        <dependency>
            <groupId>com.huafagroup</groupId>
            <artifactId>base-transaction-core</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--<dependency>-->
            <!--<groupId>mysql</groupId>-->
            <!--<artifactId>mysql-connector-java</artifactId>-->
            <!--<version>5.1.38</version>-->
        <!--</dependency>-->

        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-devtools</artifactId>-->
        <!--<optional>true</optional>-->
        <!--<scope>true</scope>-->
        <!--</dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <includeEmptyDirs>true</includeEmptyDirs>
                    <resources>
                        <resource>
                            <directory>src/main/resources</directory>
                            <filtering>true</filtering>
                            <excludes>
                                <exclude>**/*.woff</exclude>
                                <exclude>**/*.woff2</exclude>
                                <exclude>**/*.ttf</exclude>
                            </excludes>
                        </resource>
                        <resource>
                            <directory>src/main/resources</directory>
                            <filtering>false</filtering>
                            <includes>
                                <include>**/*.woff</include>
                                <include>**/*.woff2</include>
                                <include>**/*.ttf</include>
                            </includes>
                        </resource>
                    </resources>
                </configuration>
            </plugin>

        </plugins>
    </build>
    
</project>