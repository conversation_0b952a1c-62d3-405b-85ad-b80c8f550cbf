<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <link rel="stylesheet" href="/root/layui_ext/dtree/dtree.css">
    <link rel="stylesheet" href="/root/layui_ext/dtree/font/dtreefont.css">
    <link rel="stylesheet" href="/root/layui/css/layui.css">

    <script type="text/javascript" src="/root/layui/layui.js"></script>
    <script type="text/javascript" src="/root/layui_ext/dtree/dtree.js"></script>
    <script type="text/javascript" src="/root/treeSelect/treeSelect.js"></script>
    <title>TableSelect</title>
</head>

<body>
<form class="layui-form" action="" style="padding:20px;">
    <div class="layui-form-item">
        <label class="layui-form-label">多选</label>
        <div class="layui-input-inline">
            <input type="text" name="" placeholder="请输入" autocomplete="off" class="layui-input" id="demo"
                   value="刘晓军,张恒"/>
            <input type="hidden" name="" autocomplete="off" class="layui-input" id="demo_id" value="1,2"/>
            <input type="hidden" name="" autocomplete="off" class="layui-input" id="demo_data" value=""/>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">单选</label>
        <div class="layui-input-inline">
            <input type="text" name="" placeholder="请输入" autocomplete="off" class="layui-input" id="demo2">
            <input type="text" name="" placeholder="请输入" autocomplete="off" class="layui-input" id="demo2">
        </div>
    </div>
</form>
</body>
<script type="text/javascript">
    layui.config({
        base: '/root/treeSelect/' //你存放新模块的目录，注意，不是layui的模块目录
    }).use(['form', 'element', 'treeSelect'], function (args) {
        var form = layui.form;
        form.render();
        var treeSelect = layui.treeSelect;

        treeSelect.render({
            elem: '#demo',
            width: 300,
            height: 280,
            tree: {
                url: "/rbac/role/rolemenu",
                icon: "2",
                async: true,
                initLevel: -1,
                skin: "layui",
                checkbar: true,
                checkbarType: "all",
                checkbarFun: {
                    chooseBefore: function ($i, node) {

                        return true;
                    },
                    chooseDone: function (nodes) {

                    }
                }

            }
        });


    });


</script>

</html>