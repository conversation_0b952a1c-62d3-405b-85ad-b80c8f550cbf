<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>权限修改页面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/root/layui_origin/css/layui.css">
    <link rel="stylesheet" href="/root/layui_ext/dtree/dtree.css" media="all"/>
    <link rel="stylesheet" href="/root/layui_ext/dtree/font/dtreefont.css" media="all"/>
    <style type="text/css">
        .layui-table-cell {
            height: auto;
            overflow: visible;
            text-overflow: inherit;
            white-space: normal;
        }
    </style>
</head>

<body>

<form class="layui-form layui-form-pane" style="margin-left: 15px">

    <div class="layui-tab">
        <ul class="layui-tab-title">
            <li class="layui-this">角色管理</li>
            <li>组织权限</li>
        </ul>
        <div class="layui-tab-content">
            <div class="layui-tab-item layui-show">
                <div style="height: 500px;overflow: auto;">
                    高度
                </div>
            </div>
            <div class="layui-tab-item">
                <div style="height: 500px;overflow: auto;">
                    <ul id="adminTree" class="dtree" data-id="0"></ul>
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-filter="add" lay-submit="">确定</button>
            <button class="layui-btn layui-btn-primary" type="reset">重置</button>
        </div>
    </div>
</form>
<!--</div>-->
<script src="/root/layui_origin/layui.js" charset="utf-8"></script>
<script src="/root/js/server-api.js" charset="utf-8"></script>
<script>
    layui.extend({
        dtree: '/root/layui_ext/dtree/dtree'
    }).use(['table', 'layer', 'form', 'util', 'dtree', 'element'], function () {
        var $ = layui.jquery;
        var table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        var dtree = layui.dtree;
        var element = layui.element;

        dtree.render({
            elem: "#adminTree",
            url: "getTreeByUser?userid=${userid}",
            dataFormat: "list",
            checkbar: true,
            checkbarType: "self",
            skin: "layui"
        });


        //监听提交
        form.on('submit(add)', function (data) {
            var msgIndex = layer.msg('系统处理中，请等待...', {shade: [0.8, '#393D49'], time: 1500});
            var index = parent.layer.getFrameIndex(window.name); //
            var f = data.field;
            var nodeIds = [];
            if (dtree.changeCheckbarNodes("adminTree")) {
                var checkNodes = dtree.getCheckbarNodesParam("adminTree");
                for (var i = 0; i < checkNodes.length; i++) {
                    nodeIds.push(checkNodes[i].nodeId);
                }
            }
            console.log(JSON.stringify(nodeIds));
            var param = {
                birthday: f.birthday
            };
            var url;
            url = '/base';
            api(param, 'POST', url, Callback, '');
            return false;
        });
    });

</script>

</body>

</html>