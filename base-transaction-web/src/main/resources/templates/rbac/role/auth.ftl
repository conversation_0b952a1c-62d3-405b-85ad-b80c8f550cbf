<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>角色权限分配</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link rel="stylesheet" href="/root/assets/libs/layui/css/layui.css"/>
    <link rel="stylesheet" href="/root/assets/module/admin.css"/>
    <!-- zTree -->
    <link rel="stylesheet" href="/root/assets/libs/zTree/css/zTreeStyle/zTreeStyle.css"/>
</head>

<body>

<div style="height: 350px;overflow-y: auto;overflow-x: hidden;">
    <input id="roleId" type="hidden" value="3">
    <ul id="treeAuth" class="ztree" style="padding: 20px 0px 20px 40px;"></ul>
</div>

<div class="text-right" style="padding-top: 10px;padding-right: 25px;margin-bottom: 15px;">
    <button class="layui-btn layui-btn-primary" ew-event="closeDialog">取消</button>
    <button id="btnSave" class="layui-btn">保存</button>
</div>

<!-- js部分 -->
<script type="text/javascript" src="/root/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/root/assets/libs/layui/layui.js"></script>
<!-- zTree的库请在iframe后台集成版中获取 -->
<script type="text/javascript" src="/root/assets/libs/zTree/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript" src="/root/assets/js/common.js"></script>
<script>
    layui.use(['layer', 'admin'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;
        var admin = layui.admin;

        var roleId = $('#roleId').val();
        layer.load(2);
        var setting = {
            check: {enable: true},
            data: {
                simpleData: {enable: true}
            }
        };
        $.get('../../json/authTree.json', {
            roleId: roleId
        }, function (data) {
            $.fn.zTree.init($('#treeAuth'), setting, data);
            layer.closeAll('loading');
        }, 'json');

        // 保存按钮点击事件
        $('#btnSave').click(function () {
            layer.load(2);
            var treeObj = $.fn.zTree.getZTreeObj('treeAuth');
            var nodes = treeObj.getCheckedNodes(true);
            var ids = new Array();
            for (var i = 0; i < nodes.length; i++) {
                ids[i] = nodes[i].id;
            }
            $.get('../../json/ok.json', {
                roleId: roleId,
                authIds: JSON.stringify(ids)
            }, function (data) {
                layer.closeAll('loading');
                if (200 == data.code) {
                    top.layer.msg(data.msg, {icon: 1});
                    // 关闭当前iframe弹出层
                    admin.closeThisDialog();
                } else {
                    top.layer.msg(data.msg, {icon: 2});
                }
            }, 'json');
        });

        admin.iframeAuto();  // 让当前iframe弹层高度适应
    });
</script>

</body>
</html>