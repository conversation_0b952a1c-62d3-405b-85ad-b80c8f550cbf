<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>新增页面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/root/layui/css/layui.css">

</head>

<body>
<!--<div class="weadmin-body">-->
<form class="layui-form">

    </div>
    <div class="layui-form-item">
        <label for="sex" class="layui-form-label">
            性别
        </label>
        <div class="layui-input-inline">
            <select name="sex" id="sex">
                <option value="">请选择</option>
                <@user_dict type="sex">
                    <#list dictList as dict>
                        <option value="${dict.value}"> ${dict.text}</option>
                    </#list>
                </@user_dict>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label for="idCard" class="layui-form-label">
            身份证
        </label>
        <div class="layui-input-inline">
            <input type="text" id="idCard" name="idCard" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm" lay-verify="idCardverify">
        </div>
        <h1 style="color:red">*</h1>

    </div>
    <div class="layui-form-item">
        <label for="password" class="layui-form-label">
            密码
        </label>
        <div class="layui-input-inline">
            <input type="text" id="password" name="password" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm" lay-verify="passwordverify">
        </div>
        <h1 style="color:red">*</h1>

    </div>
    <div class="layui-form-item">
        <label for="name" class="layui-form-label">
            姓名
        </label>
        <div class="layui-input-inline">
            <input type="text" id="name" name="name" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm" lay-verify="nameverify">
        </div>
        <h1 style="color:red">*</h1>

    </div>
    <div class="layui-form-item">
        <label for="tel" class="layui-form-label">
            手机号码
        </label>
        <div class="layui-input-inline">
            <input type="text" id="tel" name="tel" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm" lay-verify="telverify">
        </div>
        <h1 style="color:red">*</h1>

    </div>


    <div class="layui-form-item">
        <label for="account" class="layui-form-label">
            账号
        </label>
        <div class="layui-input-inline">
            <input type="text" id="account" name="account" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm" lay-verify="accountverify">
        </div>
        <h1 style="color:red">*</h1>

    </div>
    <div class="layui-form-item">
        <label for="email" class="layui-form-label">
            邮箱
        </label>
        <div class="layui-input-inline">
            <input type="text" id="email" name="email" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm" lay-verify="emailverify">
        </div>
        <h1 style="color:red">*</h1>

    </div>
    <div class="layui-form-item">
        <label for="age" class="layui-form-label">
            年龄
        </label>
        <div class="layui-input-inline">
            <input type="text" id="age" name="age" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm" lay-verify="ageverify">
        </div>


    </div>
    <div class="layui-form-item">
        <label for="status" class="layui-form-label">
            用户状态
        </label>
        <div class="layui-input-inline">
            <input type="checkbox" name="status" lay-skin="switch" lay-text="ON|OFF">

        </div>


    </div>


    <div class="layui-form-item layui-hide">
        <label for="createDate" class="layui-form-label">
            创建时间
            <!--  <span class="we-red">*</span>session-->
        </label>
        <div class="layui-input-inline">
            <input type="text" id="createDate" name="createDate" autocomplete="off" class="layui-input"
                   placeholder="yyyy-MM-dd HH:mm:ss" lay-verify="createDateverify">
        </div>
        <!--	<div class="layui-form-mid layui-word-aux">
                请设置至少5个字符，将会成为您唯一的登录名
            </div>-->
    </div>

    <div class="layui-form-item layui-hide">
        <label for="createBy" class="layui-form-label">
            创建者
            <!--  <span class="we-red">*</span>session-->
        </label>
        <div class="layui-input-inline">
            <input type="text" id="createBy" name="createBy" autocomplete="off" class="layui-input"
                   lay-verify="createByverify">
        </div>
        <!--	<div class="layui-form-mid layui-word-aux">
                请设置至少5个字符，将会成为您唯一的登录名
            </div>-->
    </div>

    <div class="layui-form-item layui-hide">
        <label for="updateDate" class="layui-form-label">
            修改时间
        </label>
        <div class="layui-input-inline">
            <input type="text" id="updateDate" name="updateDate" autocomplete="off" class="layui-input"
                   placeholder="yyyy-MM-dd HH:mm:ss" lay-verify="updateDateverify">
        </div>
    </div>

    <div class="layui-form-item layui-hide">
        <label for="updateBy" class="layui-form-label">
            修改者
            <!--  <span class="we-red">*</span>session-->
        </label>
        <div class="layui-input-inline">
            <input type="text" id="updateBy" name="updateBy" autocomplete="off" class="layui-input"
                   lay-verify="updateByverify">
        </div>
        <!--	<div class="layui-form-mid layui-word-aux">
                请设置至少5个字符，将会成为您唯一的登录名
            </div>-->
    </div>

    <div class="layui-form-item">
        <!--	<label for="L_repass" class="layui-form-label">
      </label>-->
        <div class="layui-input-block">
            <button class="layui-btn" lay-filter="add" lay-submit="">确定</button>
            <button class="layui-btn layui-btn-primary" type="reset">重置</button>
        </div>
    </div>
</form>
<!--</div>-->
<script src="/root/layui/layui.js" charset="utf-8"></script>
<script src="/root/js/server-api.js" charset="utf-8"></script>
<script>

    layui.use(['form', 'jquery', 'util', 'layer', 'table', 'laydate'], function () {
        var form = layui.form,
            $ = layui.jquery,
            util = layui.util,
            layer = layui.layer;
        table = layui.table;
        laydate = layui.laydate;
        laydate.render({
            elem: '#createDate',
            type: 'datetime'
        });
        laydate.render({
            elem: '#updateDate',
            type: 'datetime'
        });

        laydate.render({
            elem: '#updateDate',
            type: 'datetime'
        });
        laydate.render({
            elem: '#createDate',
            type: 'datetime'
        });
        Callback = function (result, obj) {
            if (result.code == 200) {
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;"> 保存成功！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        window.parent.layui.table.reload('userbasic_table');
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }
                });
            } else {
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">' + result.message + '插入失败！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        /*  window.parent.layui.table.reload('userbasic_table');
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);*/
                    }
                });
            }

        };
        //添加前端验证
        form.verify({
            verify: [, '']
            , saltverify: [/^.{0,40}$/, '  长度不能超过40']
            , sexverify: [/^.{0,1}$/, '性别 1: 男  2: 女  长度不能超过1']
            , idCardverify: [/^.{1,30}$/, '身份证 不能为空并且长度不能超过30']
            , createByverify: [/^.{0,10}$/, '  长度不能超过10']
            , passwordverify: [/^.{1,40}$/, '密码 不能为空并且长度不能超过40']
            , nameverify: [/^.{1,50}$/, '姓名 不能为空并且长度不能超过50']
            , telverify: [/^.{1,11}$/, '手机号码 不能为空并且长度不能超过11']
            , idverify: [/^.{0,32}$/, '  长度不能超过32']
            , updateByverify: [/^.{0,10}$/, '  长度不能超过10']
            , accountverify: [/^.{1,50}$/, '账号 不能为空并且长度不能超过50']
            , emailverify: [/^.{1,50}$/, '邮箱 不能为空并且长度不能超过50']
        });

        //监听提交
        form.on('submit(add)', function (data) {
            var msgIndex = layer.msg('系统处理中，请等待...', {shade: [0.8, '#393D49'], time: 1500});
            var index = parent.layer.getFrameIndex(window.name); //
            var f = data.field;
            var param = {

                salt: f.salt
                , sex: f.sex

                , idCard: f.idCard

                , updateDate: f.updateDate

                , createBy: f.createBy

                , password: f.password

                , name: f.name

                , tel: f.tel

                , id: f.id

                , createDate: f.createDate

                , updateBy: f.updateBy

                , account: f.account

                , email: f.email

                , age: f.age

                , status: f.status == undefined ? 0 : 1

                /* id: f.id, section: f.section, name: f.name, owner: f.owner, description: f.description,
createDate: f.createDate, createBy: f.createBy, updateDate: f.updateDate, updateBy: f.updateBy*/
            };
            var url;
            url = '/uc/userbasic/create';
            api(param, 'POST', url, Callback, '');
            return false;
        });
    });

</script>

</body>

</html>