<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>新增页面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/root/assets/libs/layui/css/layui.css">
    <!--<link rel="stylesheet" href="../static/css/weadmin.css">-->
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>
<form class="layui-form" style="margin: 20px; padding-top: 10px">
    <div class="layui-form-item">
        <label for="code" class="layui-form-label">
            字段名
        </label>
        <div class="layui-input-inline">
            <input type="text" id="code" name="code" autocomplete="off" lay-verify="required"
                   class="layui-input layui-input-primary layui-input-sm">
        </div>

    </div>
    <div class="layui-form-item">
        <label for="text" class="layui-form-label">
            字典中文名
        </label>
        <div class="layui-input-inline">
            <input type="text" id="text" name="text" autocomplete="off" lay-verify="required"
                   class="layui-input layui-input-primary layui-input-sm">
        </div>
    </div>
    <div class="layui-form-item">
        <label for="status" class="layui-form-label">
            状态
        </label>
        <div class="layui-input-inline">
            <input type="checkbox" name="status" lay-skin="switch" lay-text="ON|OFF">
        </div>

    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-filter="add" lay-submit="">确定</button>
            <button class="layui-btn layui-btn-primary" type="reset">重置</button>
        </div>
    </div>
</form>
<script src="/root/assets/libs/layui/layui.js" charset="utf-8"></script>
<script src="/root/js/server-api.js" charset="utf-8"></script>
<script>

    layui.use(['form', 'jquery', 'util', 'layer', 'table', 'laydate'], function () {
        var form = layui.form,
            $ = layui.jquery,
            util = layui.util,
            layer = layui.layer,
            table = layui.table,
            laydate = layui.laydate;

        laydate.render({
            elem: '#createDate',
            type: 'datetime'
        });
        laydate.render({
            elem: '#updateDate',
            type: 'datetime'
        });

        //表格验证.
        form.verify({
            verify: [, '']
            , codeVerify: [/^.{1,255}$/, '字段名称 不能为空']
            , textVerify: [/^.{1,255}$/, '任务名称 不能为空']
        });

        //监听提交
        form.on('submit(add)', function (data) {
            var index = parent.layer.getFrameIndex(window.name); //
            var f = data.field;
            var param = {
                createBy: f.createBy
                , code: f.code
                , status: f.status == undefined ? 0 : 1
                , text: f.text
            };
            var url = '/system/dictionary/dictionarytype/create';
            api(param, 'POST', url, Callback, '');
            return false;
        });

        // 回调方法
        Callback = function (result, obj) {
            if (result.code == 200) {
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">保存成功！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        window.parent.layui.table.reload('dictionarytype_table');
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }
                });
            } else {
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">' + result.message + '保存失败！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();

                    }
                });
            }

        };
    });

</script>

</body>

</html>