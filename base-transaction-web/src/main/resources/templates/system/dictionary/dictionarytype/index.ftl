<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/root/assets/libs/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="/root/assets/libs/layui/css/layuipage.css" media="all">
    <link rel="stylesheet" href="/root/assets/common.css" media="all">
    <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<div class="layui-fluid">
    <div class="layui-card">
        <div class="layui-card-body layui-form">
            <table class="layui-hide" id="dictionarytype_table" lay-filter="dictionarytype_table"></table>
            <hr>
            <table class="layui-hide" id="dictionaryitem_table" lay-filter="dictionaryitem_table"></table>
        </div>
    </div>
</div>
<script type="text/html" id="statusTpl">
    <input type="checkbox" disabled="disabled" value="{{d.status}}" name="status" lay-skin="switch" lay-text="ON|OFF" {{ d.status== 1 ? 'checked' : '' }}>
</script>

<script id="toolbarDemo" type="text/html">
    <div>
        <button class="layui-btn" onclick="addShow('新增页面','./add',450,300)">
            <i class="layui-icon">&#xe654;</i>新增
        </button>
        <span style="padding-left: 30px;"></span>
        字段名：
        <div class="layui-inline">
            <input name="code" class="layui-input" id="code" autocomplete="off">
        </div>
        <span style="padding-left: 15px;"></span>
        <button class="layui-btn" data-type="reload" onclick="dictionaryTypereloadTable()">
            <iclass="layui-icon layui-icon-search"></i>搜索
        </button>
    </div>
</script>
<script id="barDemo" type="text/html">
    <a class="layui-btn layui-btn-primary layui-btn-xs" lay-event="addDictionaryItem">添加字典项</a>
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<script id="dictionaryitemBarDemo" type="text/html">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>

<script src="/root/assets/libs/layui/layui.js" charset="utf-8"></script>
<!-- 注意：如果你直接复制所有代码到本地，上述js路径需要改成你本地的 -->
<script src="/root/js/server-api.js" charset="utf-8"></script>
<script charset="utf-8">
    layui.use(['table', 'layer', 'form', 'util', 'laydate'], function () {
        var $ = layui.jquery;
        var table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        var util = layui.util;
        var laydate = layui.laydate;
        var getListUrl = '/system/dictionary/dictionaryitem/getListbyDictionaryId';
        var deleteUrl = '/system/dictionary/dictionaryitem/delete';

        var getdictionarytypeListUrl = '/system/dictionary/dictionarytype/getlist';
        var dictionarytypedeleteUrl = '/system/dictionary/dictionarytype/delete';

        laydate.render({
            elem: '#dateRangePick'
            , type: 'date'
            , range: true //或 range: '~' 来自定义分割字符
        });
        table.render({
            elem: '#dictionarytype_table',
            url: getdictionarytypeListUrl,
            toolbar: '#toolbarDemo',
            title: '信息',
            // height: 'full-200',
            cellMinWidth: 90,
            cols: [[
                // {type: 'checkbox', fixed: 'left'}
                {field: 'id', title: '主键', width: 80, align: 'center'}
                , {field: 'code', title: '字段名', align: 'center'}
                , {field: 'text', title: '字典中文名', align: 'center'}
                , {field: 'status', templet: '#statusTpl', title: '状态', align: 'center'}
                , {field: 'createBy', title: '创建者', align: 'center'}
                , {field: 'createDate', title: '创建时间', sort: true, align: 'center'}
                , {field: 'updateBy', title: '修改者', align: 'center'}
                , {field: 'updateDate', title: '修改时间', align: 'center'}
                , {fixed: 'right', title: '操作', toolbar: '#barDemo', width: 300, align: 'center'}
            ]]
            , response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            }
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.data //解析数据列表
                };
            }
            , page: true
        });

        //监听行单击事件（单击事件为：rowDouble）
        table.on('row(dictionarytype_table)', function (obj) {
            var data = obj.data;

            table.reload('dictionaryitem_table', {
                page: {
                    curr: 1 //重新从第 1 页开始
                }
                , where: {

                    dictid: data.id

                }
            });
            //标注选中样式
            obj.tr.addClass('layui-table-click').siblings().removeClass('layui-table-click');
        });

        //头工具栏事件
        table.on('toolbar(dictionarytype_table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            switch (obj.event) {
                case 'ApplicationAdd':
                    addShow('添加', './add', 450, 300);
                    var data = checkStatus.data;
                    layer.alert(JSON.stringify(data));
                    break;
            }
            ;
        });
        //监听行工具事件
        table.on('tool(dictionarytype_table)', function (obj) {
            var f = obj.data;

            var param = {
                createBy: f.createBy
                , code: f.code
                , status: f.status
                , text: f.text
                , id: f.id
                , createDate: f.createDate
                , updateBy: f.updateBy
                , updateDate: f.updateDate
            };
            if (obj.event === 'del') {
                layer.confirm('确定删除此记录吗?', function (index) {
                    api(param, 'POST', dictionarytypedeleteUrl, Callback, obj);
                    // obj.del();
                    layer.close(index);
                });
            } else if (obj.event === 'edit') {
                layer.open({
                    type: 2,
                    area: [450 + 'px', 300 + 'px'],
                    fix: false, //不固定
                    maxmin: true,
                    shadeClose: true,
                    shade: 0.4,
                    title: '编辑',
                    content: './edit?id=' + f.id
                });
            } else if (obj.event === 'addDictionaryItem') {
                //执行重载
                layer.open({
                    type: 2,
                    area: [450 + 'px', 300 + 'px'],
                    fix: false, //不固定
                    maxmin: true,
                    shadeClose: true,
                    shade: 0.4,
                    title: '编辑',
                    content: '../dictionaryitem/add?typeid=' + f.id
                });

                table.reload('dictionaryitem_table', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    }
                    , where: {

                        dictid: f.id

                    }
                });
            }
        });

        table.render({
            elem: '#dictionaryitem_table',
            url: getListUrl,
            toolbar: '#dictionaryitemToolbarDemo',
            title: '信息',
            // height: 'full-50',
            cellMinWidth: 90,
            cols: [[
                // {type: 'checkbox', fixed: 'left'}
                {field: 'typeId', title: '类型ID', width: 80, align: 'center'}
                , {field: 'sort', title: '序号', hide:true, align: 'center'}
                , {field: 'text', title: '字典内容', align: 'center'}
                , {field: 'id', title: '主键', hide:true, align: 'center'}
                , {field: 'value', title: '值', align: 'center'}
                , {field: 'createBy', title: '创建者', align: 'center'}
                , {field: 'createDate', title: '创建时间', align: 'center'}
                , {field: 'updateBy', title: '修改者', align: 'center'}
                , {field: 'updateDate', title: '修改时间', align: 'center'}
                , {fixed: 'right', title: '操作', toolbar: '#dictionaryitemBarDemo', width: 300, align: 'center'}
            ]]
            , response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            }
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.data //解析数据列表
                };
            }
            , page: true
        });

        //头工具栏事件
        table.on('toolbar(dictionaryitem_table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            switch (obj.event) {
                case 'ApplicationAdd':
                    addShow('添加', './add', 450, 300);
                    var data = checkStatus.data;
                    layer.alert(JSON.stringify(data));
                    break;
            }
        });
        //监听行工具事件
        table.on('tool(dictionaryitem_table)', function (obj) {
            var f = obj.data;
            var param = {
                value: f.value
                , typeId: f.typeId
                , sort: f.sort
                , text: f.text
                , id: f.id
            };
            if (obj.event === 'del') {
                layer.confirm('确定删除此记录吗?', function (index) {
                    api(param, 'POST', deleteUrl, Callback, obj);
                    // obj.del();
                    layer.close(index);
                });
            } else if (obj.event === 'edit') {
                layer.open({
                    type: 2,
                    area: [450 + 'px', 300 + 'px'],
                    fix: false, //不固定
                    maxmin: true,
                    shadeClose: true,
                    shade: 0.4,
                    title: '编辑',
                    content: '../dictionaryitem/edit?id=' + f.id
                });
            }
        });

        Callback = function (result, obj) {
            if (result.code == 200) {
                //
                obj.del();
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">删除成功！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        //   window.parent.location.reload();
                    }
                });
            } else {
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">' + result.message + '删除失败！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        //  window.parent.location.reload();
                    }
                });
            }
        }

        dictionaryTypereloadTable = function () {
            var code = $('#code').val();
            //  var daterange = $('#dateRangePick').val();
            table.reload('dictionarytype_table', {
                page: {
                    curr: 1 //重新从第 1 页开始
                }
                , where: {
                    code: code
                }
            });
            laydate.render({
                elem: '#dateRangePick'
                , type: 'date'
                , range: true //或 range: '~' 来自定义分割字符
            });
        }

        window.addShow = function (title, url, w, h) {
            if (title == null || title == '') {
                title = false;
            }
            if (url == null || url == '') {
                url = "404.html";
            }
            if (w == null || w == '') {
                w = ($(window).width() * 0.9);
            }
            if (h == null || h == '') {
                h = ($(window).height() - 50);
            }
            layer.open({
                type: 2,
                area: [w + 'px', h + 'px'],
                fix: false, //不固定
                maxmin: true,
                shadeClose: true,
                shade: 0.4,
                title: title,
                content: url
            });
        }

    });


</script>
</body>
</html>