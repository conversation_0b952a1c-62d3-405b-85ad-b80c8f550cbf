<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>layui</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/root/assets/libs/layui/css/layui.css" media="all">
    <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>

<div style="margin: 15px;">
    <div style="border-radius: 2px; background-color: #fff; box-shadow: ">
        <div style="position: relative; padding: 10px 15px; line-height: 24px;">
            <table class="layui-hide" id="dictionaryitem_table" lay-filter="dictionaryitem_table"></table>
        </div>
    </div>
</div>

<#--<script id="dictionaryitemToolbarDemo" type="text/html">-->
    <#--<div class="">-->
        <#--<button class="layui-btn" onclick="addShow('新增页面','./add',600,400)"><i class="layui-icon">&#xe654;</i>新增-->
        <#--</button>-->
    <#--</div>-->
<#--</script>-->
<script id="dictionaryitemBarDemo" type="text/html">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<script src="/root/assets/libs/layui/layui.js" charset="utf-8"></script>
<!-- 注意：如果你直接复制所有代码到本地，上述js路径需要改成你本地的 -->
<script src="/root/js/server-api.js" charset="utf-8"></script>
<script charset="utf-8">
    var getListUrl = '/system/dictionary/dictionaryitem/getlist';
    var deleteUrl = '/system/dictionary/dictionaryitem/delete';

    layui.use(['table', 'layer', 'form', 'util', 'laydate'], function () {
        var $ = layui.jquery;
        var table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        var util = layui.util;
        var laydate = layui.laydate;
        laydate.render({
            elem: '#dateRangePick'
            , type: 'date'
            , range: true //或 range: '~' 来自定义分割字符
        });
        table.render({
            elem: '#dictionaryitem_table',
            url: getListUrl,
            toolbar: '#dictionaryitemToolbarDemo',
            title: '信息',
            // height: 'full-200',
            cellMinWidth: 80,
            cols: [[
                // {type: 'checkbox', fixed: 'left'}
                {field: 'id', title: '主键', width: 80, hide:true}
                , {field: 'typeId', title: '类型ID', width: 80}
                , {field: 'sort', title: '序号', width: 80, hide:true}
                , {field: 'value', title: '值', width: 80}
                , {field: 'text', title: '字典内容', width: 80}
                , {field: 'createBy', title: '创建者', width: 80}
                , {field: 'createDate', title: '创建时间', width: 80}
                , {field: 'updateBy', title: '修改者', width: 80}
                , {field: 'updateDate', title: '修改时间', width: 80}
                , {fixed: 'right', title: '操作', toolbar: '#dictionaryitemBarDemo', width: 200}
            ]]
            , response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            }
            , parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.count, //解析数据长度
                    "data": res.data //解析数据列表
                };
            }
            , page: true
        });
        Callback = function (result, obj) {
            if (result.code == 200) {
                obj.del();
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">删除成功！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        //   window.parent.location.reload();
                    }
                });
            } else {
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">' + result.message + '删除失败！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        //  window.parent.location.reload();
                    }
                });
            }
        }
        //头工具栏事件
        table.on('toolbar(dictionaryitem_table)', function (obj) {
            var checkStatus = table.checkStatus(obj.config.id);
            switch (obj.event) {
                case 'ApplicationAdd':
                    addShow('添加', './add', 600, 400);
                    var data = checkStatus.data;
                    layer.alert(JSON.stringify(data));
                    break;
            }
            ;
        });
        //监听行工具事件
        table.on('tool(dictionaryitem_table)', function (obj) {
            var f = obj.data;

            var param = {
                createBy: f.createBy
                , typeId: f.typeId
                , sort: f.sort
                , text: f.text
                , id: f.id
                , value: f.value
                , updateBy: f.updateBy
                , createDate: f.createDate
                , updateDate: f.updateDate

            };
            if (obj.event === 'del') {
                layer.confirm('确定删除此记录吗?', function (index) {
                    api(param, 'POST', deleteUrl, Callback, obj);
                    // obj.del();
                    layer.close(index);
                });
            } else if (obj.event === 'edit') {

                layer.open({
                    type: 2,
                    area: [600 + 'px', 400 + 'px'],
                    fix: false, //不固定
                    maxmin: true,
                    shadeClose: true,
                    shade: 0.4,
                    title: '编辑',
                    content: './edit?id=' + f.id,
                });
            }
        });

        reloadTable = function () {
            var appm = $('#appModule').val();
            var daterange = $('#dateRangePick').val();
            table.reload('dictionaryitem_table', {
                page: {
                    curr: 1 //重新从第 1 页开始
                }
                , where: {
                    appname: appm,
                    begindate: daterange.substr(0, 10),
                    enddate: daterange.substr(13, 10)
                }
            });
            laydate.render({
                elem: '#dateRangePick'
                , type: 'date'
                , range: true //或 range: '~' 来自定义分割字符
            });
        }

        window.addShow = function (title, url, w, h) {
            if (title == null || title == '') {
                title = false;
            }
            if (url == null || url == '') {
                url = "404.html";
            }
            if (w == null || w == '') {
                w = ($(window).width() * 0.9);
            }
            if (h == null || h == '') {
                h = ($(window).height() - 50);
            }
            layer.open({
                type: 2,
                area: [w + 'px', h + 'px'],
                fix: false, //不固定
                maxmin: true,
                shadeClose: true,
                shade: 0.4,
                title: title,
                content: url
            });
        }

    });


</script>
</body>
</html>