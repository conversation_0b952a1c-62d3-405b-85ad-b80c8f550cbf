<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>添加应用</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="/root/assets/libs/layui/css/layui.css">
    <!--<link rel="stylesheet" href="../static/css/weadmin.css">-->
    <!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>

<body>
<form class="layui-form" style="margin: 20px; padding-top: 10px">
    <div class="layui-form-item">
        <label for="sort" class="layui-form-label">
            序号
        </label>
        <div class="layui-input-inline">
            <input type="text" value="${entity.sort!""}" name="sort" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm">
        </div>
    </div>
    <div class="layui-form-item">
        <label for="text" class="layui-form-label">
            字典内容
        </label>
        <div class="layui-input-inline">
            <input type="text" value="${entity.text!""}" name="text" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <label for="id" class="layui-form-label ">
            主键
        </label>
        <div class="layui-input-inline">
            <input type="text" value="${entity.id}" name="id" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm">
        </div>
    </div>
    <div class="layui-form-item">
        <label for="value" class="layui-form-label">
            值
        </label>
        <div class="layui-input-inline">
            <input type="text" value="${entity.value!""}" name="value" autocomplete="off"
                   class="layui-input layui-input-primary layui-input-sm">
        </div>
    </div>
    <div class="layui-form-item layui-hide">
        <label for="updateBy" class="layui-form-label">隐藏域</label>
        <div class="layui-input-inline">
            <div class="layui-input-inline">
                <input type="text" value="${entity.typeId}" name="typeId" autocomplete="off"
                       class="layui-input layui-input-primary layui-input-sm">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-input-block">
            <button class="layui-btn" lay-filter="add" lay-submit="">确定</button>
            <button class="layui-btn layui-btn-primary" type="reset">重置</button>
        </div>
    </div>
</form>

<script src="/root/assets/libs/layui/layui.js" charset="utf-8"></script>
<script src="/root/js/server-api.js" charset="utf-8"></script>
<script charset="utf-8">
    layui.use(['form', 'jquery', 'util', 'layer', 'table', 'laydate'], function () {
        var form = layui.form,
            $ = layui.jquery,
            util = layui.util,
            layer = layui.layer;
        table = layui.table;
        laydate = layui.laydate;
        laydate.render({
            elem: '#createDate',
            type: 'datetime'
        });
        laydate.render({
            elem: '#updateDate',
            type: 'datetime'
        });

        Callback = function (result, obj) {
            if (result.code == 200) {
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">保存成功！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        window.parent.layui.table.reload('dictionaryitem_table');
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                    }
                });
            } else {
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">' + result.message + '保存失败！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        window.parent.location.reload();
                    }
                });
            }
        };


        //监听提交
        form.on('submit(add)', function (data) {
            var index = parent.layer.getFrameIndex(window.name); //zhixian
            var f = data.field;
            var param = {
                createBy: f.createBy
                , typeId: f.typeId
                , sort: f.sort
                , text: f.text
                , value: f.value
             , id: f.id

                /*      , updateBy: f.updateBy
                   , createDate: f.createDate
                   , updateDate: f.updateDate*/
            };
            var updateUrl;
            updateUrl = '/system/dictionary/dictionaryitem/update';
            api(param, 'POST', updateUrl, Callback, '');

            return false;

        });

    });

    function UrlSearch() {
        var name, value;
        var str = location.href; //取得整个地址栏
        var rep = new RegExp("%20", "g");
        str = str.replace(rep, ' ');
        var num = str.indexOf("?")
        str = str.substr(num + 1); //取得所有参数   stringvar.substr(start [, length ]
        var arr = str.split("&"); //各个参数放到数组里
        for (var i = 0; i < arr.length; i++) {
            num = arr[i].indexOf("=");
            if (num > 0) {
                name = arr[i].substring(0, num);
                value = arr[i].substr(num + 1);
                this[name] = value;
            }
        }
    }

</script>

</body>

</html>