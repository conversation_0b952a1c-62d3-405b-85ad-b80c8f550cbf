<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>珠海华发集团有限公司</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">

    <link rel="icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="/root/layui/css/layui.css" media="all" />
    <link rel="stylesheet" href="/root/layui_origin/font/wyfont.css" media="all"/>
    <link rel="stylesheet" href="/root/css/main.css" media="all" />
</head>
<body>
<div class="layui-layout layui-layout-admin">
    <!-- 顶部 -->
    <div class="layui-header header">
        <div class="layui-main">
            <a href="#" class="logo"><img src="./root/images/hf.png" alt="" style="height: 30px; width: 30px; margin-right: 10px;" />华发慧云</a>
            <!-- 显示/隐藏菜单 -->
            <a href="javascript:" class="iconfont hideMenu icon-menu1"></a>
            <#--<!-- 搜索 &ndash;&gt;-->
            <#--<div class="layui-form component">-->
                <#--<select name="modules" lay-verify="required" lay-search="">-->
                    <#--<option value="">直接选择或搜索选择</option>-->
                    <#--<#if (userMenu?size>0)>-->
                        <#--<#list userMenu as items>-->
                        <#--<option value="${items.href}">${items.name}</option>-->
                        <#--</#list>-->
                    <#--</#if>-->
                <#--</select>-->
                <#--<i class="layui-icon">&#xe615;</i>-->
            <#--</div>-->
            <!-- 顶部右侧菜单 -->
            <ul class="layui-nav top_menu">
                <#--<li class="layui-nav-item showNotice" id="showNotice" pc>-->
                    <#--<a href="javascript:"><i class="iconfont icon-gonggao"></i><cite>系统公告</cite></a>-->
                <#--</li>-->
                <li class="layui-nav-item" mobile>
                    <a href="javascript:" class="mobileAddTab" data-url="page/user/changePwd.html"><i class="iconfont icon-shezhi1" data-icon="icon-shezhi1"></i><cite>设置</cite></a>
                </li>
                <li class="layui-nav-item" mobile>
                    <a href="/systemLogout" class="signOut"><i class="iconfont icon-loginout"></i> 退出</a>
                </li>
                <li class="layui-nav-item" pc>
                    <a href="javascript:">
                        <#--<img src="<#if (currentUser.icon??)>${currentUser.icon}<#else>/images/face.jpg</#if>" class="layui-circle" width="35" height="35">-->
                        <cite>用户名todo</cite>
                    </a>
                    <dl class="layui-nav-child">
                        <#--<dd><a href="javascript:" data-url="/rbac/user/userinfo"><i class="iconfont icon-zhanghu" data-icon="icon-zhanghu"></i><cite>个人资料</cite></a></dd>-->
                        <#--<dd><a href="javascript:" id="btnUP"><i class="iconfont icon-shezhi1" data-icon="icon-shezhi1"></i><cite>修改密码</cite></a></dd>-->
                        <#--<dd><a href="javascript:" class="changeSkin"><i class="iconfont icon-huanfu"></i><cite>更换皮肤</cite></a></dd>-->
                        <dd><a href="/systemLogout" class="signOut"><i class="iconfont icon-loginout"></i><cite>退出</cite></a></dd>
                    </dl>
                </li>
            </ul>
        </div>
    </div>
    <!-- 左侧导航 -->
    <div class="layui-side layui-bg-black">
        <#--<div class="user-photo">-->
            <#--<a class="img" title="我的头像" ><img src="<#if (currentUser.icon??)>${currentUser.icon}<#else>/images/face.jpg</#if>"></a>-->
            <#--<p>你好！<span class="userName"><#if currentUser.name!=''>${currentUser.name}<#else>${currentUser.loginName}</#if></span>, 欢迎登录</p>-->
        <#--</div>-->
        <div class="navBar layui-side-scroll"></div>
    </div>
    <!-- 右侧内容 -->
    <div class="layui-body layui-form">
        <div class="layui-tab marg0" lay-filter="bodyTab" id="top_tabs_box">
            <ul class="layui-tab-title top_tab" id="top_tabs">
                <li class="layui-this" lay-id=""><i class="iconfont icon-computer"></i> <cite>后台首页</cite></li>
            </ul>

            <ul class="layui-nav closeBox">
                <li class="layui-nav-item">
                    <a href="javascript:"><i class="iconfont icon-caozuo"></i> 页面操作</a>
                    <dl class="layui-nav-child">
                        <dd><a href="javascript:" class="refresh refreshThis"><i class="layui-icon">&#x1002;</i> 刷新当前</a></dd>
                        <dd><a href="javascript:" class="closePageOther"><i class="iconfont icon-prohibit"></i> 关闭其他</a></dd>
                        <dd><a href="javascript:" class="closePageAll"><i class="iconfont icon-guanbi"></i> 关闭全部</a></dd>
                    </dl>
                </li>
            </ul>

            <div class="layui-tab-content clildFrame">
                <div class="layui-tab-item layui-show">ss
                </div>
            </div>
        </div>
    </div>
    <!-- 底部 -->
    <#--<div class="layui-footer footer">-->
        <#--<p>預留描述</p>-->
    <#--</div>-->
</div>
<script>
    var baseUrl = "";
</script>
<!-- 移动导航 -->
<div class="site-tree-mobile layui-hide"><i class="layui-icon">&#xe602;</i></div>
<div class="site-mobile-shade"></div>


<!-- 修改密码弹窗 -->
<script type="text/html" id="upModel">
    <form class="layui-form model-form" id="formPsw" style="margin-right: 30px;">
        <div class="layui-form-item" style="margin-top: 30px;">
            <label class="layui-form-label">原始密码:</label>
            <div class="layui-input-block" style="margin-right: 10px;">
                <input type="password" name="oldPsw" placeholder="请输入原始密码" class="layui-input"
                       lay-verType="tips" lay-verify="required" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">新密码:</label>
            <div class="layui-input-block" style="margin-right: 10px;">
                <input type="password" name="newPsw" placeholder="请输入新密码" class="layui-input"
                       lay-verType="tips" lay-verify="required|psw" required/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">确认密码:</label>
            <div class="layui-input-block" style="margin-right: 10px;">
                <input type="password" name="rePsw" placeholder="请再次输入新密码" class="layui-input"
                       lay-verType="tips" lay-verify="required|repsw" required/>
            </div>
        </div>
        <div class="layui-form-item" style="text-align: right; margin-right: 10px;">
                <button class="layui-btn layui-btn-primary" type="button" ew-event="closeDialog">取消</button>
                <button class="layui-btn" lay-filter="submitPsw" lay-submit>保存</button>
        </div>
    </form>
</script>
<script type="text/javascript" src="/root/layui/layui.js"></script>
<script type="text/javascript" src="/root/js/leftNav.js?v=2.0"></script>
<script type="text/javascript" src="/root/js/index.js?t=3.0"></script>

</body>
</html>
