<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>华发慧云运营管理平台</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="icon" href="/favicon.ico" type="image/x-icon" />
    <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="/root/layui/css/layui.css">
    <link rel="stylesheet" href="/root/css/login.css" media="all" />
</head>


<section class="top-center-bottom">
    <style type="text/css">
        .top-center-bottom{
            width: 100%;
            height:100%;
            display: flex;
            flex-direction: column;
        }
        .top{
            width: 100%;
            height: 80px;
            /*background: yellow;*/
        }
        .bottom{
            width: 100%;
            flex: 1;
            height: 100%;
        }
    </style>
    <div class="top">
        <img src="/root/images/logo.png" style="margin-left: 10%;margin-top: 20px;float: left">
        <p style="margin-top: 20px"><font size="6">&nbsp&nbsp&nbsp&nbsp业务管理系统</font></p>
    </div>
    <div class="center">
        <form class="layui-form" action="/login/main" method="post">
            <input name="username" placeholder="请输入账号" class="name" required />
            <input name="password" placeholder="请输入密码" class="password" type="password" required />
            <button class="layui-btn login_btn" lay-submit="" lay-filter="login">登录</button>
        </form>
    </div>
    <div class="bottom">
        <div style="margin-left:40%;margin-top: 50px">© 2019 珠海华发集团有限公司 版权所有</div>
    </div>
</section>

</body>
<script type="text/javascript" src="/root/layui/layui.js"></script>
<script type="text/javascript" src="/root/js/jquery.min.js"></script>
<script>
    if (window != top){
        top.location.href = location.href;
    }
    layui.use(['layer', 'form'], function() {
        var layer = layui.layer,
            $ = layui.jquery,
            form = layui.form;

        $("#mycode").on('click',function(){
            var t = Math.random();
            $("#mycode")[0].src="/genCaptcha?t= "+t;
        });

        form.on('submit(login)', function(data) {
            var loadIndex = layer.load(2, {
                shade: [0.3, '#333']
            });
            // if($('form').find('input[type="checkbox"]')[0].checked){
            //     data.field.rememberMe = true;
            // }else{
            data.field.rememberMe = false;
            // }
            $.post(data.form.action, data.field, function(res) {
                layer.close(loadIndex);
                console.log(res)
                if(res.success){
                    location.href="/"+res.data.url;
                }else{
                    layer.msg(res.msg);
                    $("#mycode").click();
                }
            });
            return false;
        });
    });
</script>
</html>