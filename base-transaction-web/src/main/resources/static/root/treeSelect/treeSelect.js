layui.define(['dtree', 'jquery', 'form'], function (exports) {//
    "use strict";// 以javascript严格模式运行

    var MOD_NAME = 'treeSelect',
        $ = layui.jquery,
        dtree = layui.dtree,
        form = layui.form;
    var treeSelect = function () {
        this.v = '1.1.0';
    };

    treeSelect.prototype.render = function (opt) {
        var visibleInput = $(opt.elem);
        var width = (opt.width == null) ? 350 : opt.width;
        var height = (opt.height == null) ? 280 : opt.height;
        var visibleInputId = $(opt.elem + "_id");
        var visibleInputValue = $(opt.elem + "_data");
        var response = opt.tree.response;
        if (response == null || response.rootName == null) {
            response = {rootName: "data"};
        }
        opt.title = opt.title != null ? opt.title : '详情选择';

        var treeDone = opt.tree.done || function () {
        };

        visibleInput.off('click').on('click', function (e) {
            e.stopPropagation();// 阻止事件冒泡

            if ($('div.treeSelect').length >= 1) {
                // 点选id，#选class  如果已经显示，则不再重复加载
                return false;
            }

            var treeY = visibleInput.offset().top + visibleInput.outerHeight() + "px";
            var treeX = visibleInput.offset().left + "px";
            var treeid = "treeSelect_tree_" + new Date().getTime();
            var treeBarDiv = treeid + "barDiv";
            var treeBox = '<div class="treeSelect layui-anim layui-anim-upbit" style="left:' + treeX + ';top:' + treeY + ';border: 1px solid #d2d2d2;background-color: #fff;box-shadow: 0 2px 4px rgba(0,0,0,.12);position: absolute;z-index:66666666;border-radius: 2px;min-width:280px;">';

            treeBox += '<div class="treeSelectBar">';
            treeBox += '<span style="height:30px;line-height:30px;margin-left:20px;">' + opt.title + '</span>';
            treeBox += '</div>';
            treeBox += '<div style="height: ' + height + 'px;width:' + width + 'px;overflow: auto;" id="' + treeBarDiv + '">';
            treeBox += '<ul id="' + treeid + '" class="dtree" data-id="0"' + '"></ul>';
            treeBox += '</div>';
            treeBox += '<div>';
            treeBox += '<button style="float: left;margin-left: 20px;margin-bottom: 10px;" class="layui-btn layui-btn-sm treeSelect_btn_select">确定</button>';
            treeBox += '<button style="float: left;margin-left: 20px;margin-bottom: 10px;" class="layui-btn layui-btn-sm treeSelect_btn_exit">取消</button>';
            treeBox += '</div>';
            treeBox += '</div>';
            treeBox = $(treeBox);
            $('body').append(treeBox);

            // 渲染tree
            opt.tree.elem = "#" + treeid;
            opt.tree.toolbar = true;
            opt.tree.toolbarScroll = '#' + treeBarDiv;
            opt.tree.success = function (data, obj) {
                //初始化已经选中的值
                var selectId = visibleInputId.attr("value");
                if (selectId == null || selectId == "") {
                    return;
                }
                var selectIds = selectId.split(",");
                if (data instanceof String) {
                    data = JSON.parse(data);
                }

                var dataList = data[response.rootName];
                setCheckStatus(dataList, selectIds);

            };
            var treeSelect_tree = dtree.render(opt.tree);

            //按钮“选中”绑定点击事件
            treeBox.find('.treeSelect_btn_select').on('click', function () {
                var params = dtree.getCheckbarNodesParam(treeid);
                setCheckedValue(params, visibleInput, visibleInputId, visibleInputValue);
                treeBox.remove();
            });
            //按钮“选中”绑定点击事件
            treeBox.find('.treeSelect_btn_exit').on('click', function () {
                treeBox.remove();
            })
            //点击其他区域关闭
            $(document).mouseup(function (e) {
                var userSet_con = $('' + opt.elem + ',.treeSelect');
                if (!userSet_con.is(e.target) && userSet_con.has(e.target).length === 0) {
                    treeBox.remove();
                }
            });
        });

    }

    function setCheckedValue(params, visibleInput, visibleInputId, visibleInputValue) {
        if (params == null) {
            return;
        }
        var fValue = "";
        var fId = "";
        var fData = "";
        for (var i = 0; i < params.length; i++) {
            fId += params[i].nodeId + ",";
            fValue += params[i].context + ",";
            fData += params[i].basicData + ",";

        }
        fValue = fValue.substring(0, fValue.length - 1);
        fId = fId.substring(0, fId.length - 1);
        fData = fData.substring(0, fData.length - 1);

        visibleInput.attr("value", fValue);
        visibleInputId.attr("value", fId);
        if (visibleInputValue != null) {
            visibleInputValue.attr("value", fData);
        }
    }

    function setCheckStatus(dataList, selectIds) {
        for (var i = 0; i < dataList.length; i++) {
            var id = dataList[i].id;
            for (let j = 0; j < selectIds.length; j++) {
                if (selectIds[j] == id) {
                    dataList[i].checkArr = "1";
                }
            }
            if (dataList[i].children != null || dataList[i].children.length > 0) {
                setCheckStatus(dataList[i].children, selectIds);
            }
        }
    };

    /**
     * 隐藏选择器
     */
    treeSelect.prototype.hide = function (opt) {
        $('.treeSelect').remove();
    }

    //自动完成渲染
    var treeSelect = new treeSelect();

    //FIX 滚动时错位
    if (window.top == window.self) {
        $(window).scroll(function () {
            treeSelect.hide();
        });
    }

    exports(MOD_NAME, treeSelect);

})