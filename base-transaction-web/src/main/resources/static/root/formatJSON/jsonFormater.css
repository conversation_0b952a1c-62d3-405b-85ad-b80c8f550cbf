@charset "utf-8";
/* CSS Document */
.jf-ObjectBrace {
    color: #00AA00;
    font-weight: bold;
}
.jf-ArrayBrace {
    color: #0033FF;
    font-weight: bold;
}
.jf-PropertyName {
    color: #CC0000;
    font-weight: bold;
}
.jf-String {
    color: #007777;
}
.jf-Number {
    color: #AA00AA;
}
.jf-Boolean {
    color: #0000FF;
}
.jf-Null {
    color: #0000FF;
}
.jf-Comma {
    color: #000000;
    font-weight: bold;
}
pre.jf-CodeContainer {
    margin-top: 0;
    margin-bottom: 0;
    text-align: left;
}
