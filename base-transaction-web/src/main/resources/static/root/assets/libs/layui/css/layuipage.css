.layui-form-bottom-confirm {
    text-align: right;
    pointer-events: auto;
    padding: 0px 15px 12px;
}

.layui-form-simple-table {
    margin: 0 15px;
}

.layui-label-required {
    color: #7a1409;
    font-size: larger;
}

.text-right {
    text-align: right;
    padding-top: 20px;
}

/* 树表格样式 ----begin */
.treeTable-empty {
    width: 20px;
    display: inline-block;
}

.treeTable-icon {
    cursor: pointer;
}

.treeTable-icon .layui-icon-triangle-d:before {
    content: "\e623";
}

.treeTable-icon.open .layui-icon-triangle-d:before {
    content: "\e625";
    background-color: transparent;
}

/* 树表格样式 ----end */

/* 树表格样式 ----begin */
.my-skin .layui-layer-btn .layui-layer-btn0 {
    border-color: #009f95;
    background-color: #009f95;
    color: #fff
}

/* 树表格样式 ----end */
.layui-form .indexSearchDiv {
    display: inline-block;
    margin-right: 10px;
}


