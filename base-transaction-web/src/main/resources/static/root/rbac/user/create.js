layui.use(['form','jquery','layer'],function(){
    var form = layui.form,
        $    = layui.jquery,
        layer = layui.layer,
        delFlage = false;    //默认启用用户

    form.on("submit(addUser)",function(data){
        var loadIndex = layer.load(2, {
            shade: [0.3, '#333']
        });
        //给角色赋值
        delete data.field["sysRoleSet"];
        var selectRole = [];
        $('input[name="sysRoleSet"]:checked').each(function(){
            selectRole.push({"id":$(this).val()});
        });
        data.field.sysRoleSet = selectRole;
        //判断用户是否启用
        if(undefined !== data.field.delFlag && null != data.field.delFlag){
            data.field.delFlag = -1;
        }else{
            data.field.delFlag = 0;
        }
        $.ajax({
            type:"POST",
            url:"/rbac/user/create",
            dataType:"json",
            contentType:"application/json",
            data:JSON.stringify(data.field),
            success:function(res){
                layer.close(loadIndex);
                if(res.code==200){
                    parent.layer.msg(res.msg,{time:1500},function(){
                        //刷新父页面
                        parent.location.reload();
                    });
                }else{
                    layer.msg(res.message);
                }
            }
        });
        return false;
    });

});