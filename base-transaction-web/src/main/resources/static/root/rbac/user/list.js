layui.use(['layer','form','table'], function() {
    var layer = layui.layer,
        $ = layui.jquery,
        form = layui.form,
        table = layui.table,
        userListTableOptions;                  //表格数据变量

    userListTableOptions = {
        elem: '#userListTable',
        url:'/rbac/user/list',
        method:'post',
        cellMinWidth: 80,//全局定义常规单元格的最小宽度
        page: { //支持传入 laypage 组件的所有参数（某些参数除外，如：jump/elem） - 详见文档
            layout: ['limit', 'count', 'prev', 'page', 'next', 'skip'], //自定义分页布局
            //,curr: 5 //设定初始在第 5 页
            groups: 2, //只显示 1 个连续页码
            first: "首页", //显示首页
            last: "尾页", //显示尾页
            limits:[3,10, 20, 30]
        },
        width: $(parent.window).width()-223,
        request: {
            pageName: 'pageNum', //页码的参数名称，默认：page
            limitName: 'pageSize' //每页数据量的参数名，默认：limit
        },
        response: {
            statusName: 'code' //规定数据状态的字段名称，默认：code
            ,statusCode: 200 //规定成功的状态码，默认：0
            ,msgName: 'msg' //规定状态信息的字段名称，默认：msg
            ,countName: 'count' //规定数据总数的字段名称，默认：count
            ,dataName: 'data' //规定数据列表的字段名称，默认：data
        },
        cols: [[
            {type:'checkbox'},
            {field:'loginName', title: '登录名称'},
            {field:'nickName',  title: '昵称',    width:'10%'},
            {field:'email',     title: '邮箱',    width:'16%' },
            {field:'tel',       title: '电话',    width:'12%'},
            {field:'locked',    title: '用户状态',width:'12%',templet:'#userStatus'},
            {field:'createDate',  title: '创建时间',width:'18%',templet:'<div>{{ layui.laytpl.toDateString(d.createDate) }}</div>',unresize: true}, //单元格内容水平居中
            {fixed: 'right',    width: '15%', align: 'center',toolbar: '#barDemo'}
        ]]
    };
    table.render(userListTableOptions);

    //监听工具条
    table.on('tool(userListFilter)', function(obj){
        var data = obj.data;
        if(obj.event === 'edit'){
            var editIndex = layer.open({
                title : "编辑用户",
                type : 2,
                content : "/rbac/user/update?id="+data.id,
                success : function(layero, index){
                    setTimeout(function(){
                        layer.tips('点击此处返回用户列表', '.layui-layer-setwin .layui-layer-close', {
                            tips: 3
                        });
                    },500);
                }
            });
            //改变窗口大小时，重置弹窗的高度，防止超出可视区域（如F12调出debug的操作）
            $(window).resize(function(){
                layer.full(editIndex);
            });
            layer.full(editIndex);
        }
        if(obj.event === "del"){
            layer.confirm("你确定要删除该用户么？",{btn:['是的,我确定','我再想想']},
                function(){
                    $.post({
                        type:"POST",
                        url:"/rbac/user/delete",
                        dataType:"json",
                        contentType:"application/json",
                        data:JSON.stringify({"id":data.id}),
                        success:function(res) {
                            if (res.code == 200) {
                                layer.msg(res.msg, {time: 1000}, function () {
                                    table.reload('userListTable', userListTableOptions);
                                });
                            } else {
                                layer.msg(res.msg);
                            }
                        }
                    })
                })
        }
    });

    //功能按钮
    var active={
        addUser : function(){
            var addIndex = layer.open({
                title : "添加用户",
                type : 2,
                content : "/rbac/user/create",
                success : function(layero, addIndex){
                    setTimeout(function(){
                        layer.tips('点击此处返回用户列表', '.layui-layer-setwin .layui-layer-close', {
                            tips: 3
                        });
                    },500);
                }
            });
            //改变窗口大小时，重置弹窗的高度，防止超出可视区域（如F12调出debug的操作）
            $(window).resize(function(){
                layer.full(addIndex);
            });
            layer.full(addIndex);
        },
        deleteSome : function(){                        //批量删除
            var checkStatus = table.checkStatus('userListTable'),
                data = checkStatus.data;
            if(data.length > 0){
                console.log(JSON.stringify(data));
                for(var i=0;i<data.length;i++){
                    var d = data[i];
                    if(d.id === 1){
                        layer.msg("不能删除超级管理员");
                        return false;
                    }
                }
                layer.confirm("你确定要删除这些用户么？",{btn:['是的,我确定','我再想想']},
                    function(){
                        var deleteindex = layer.msg('删除中，请稍候',{icon: 16,time:false,shade:0.8});
                        $.ajax({
                            type:"POST",
                            url:"/rbac/user/deleteSome",
                            dataType:"json",
                            contentType:"application/json",
                            data:JSON.stringify(data),
                            success:function(res){
                                layer.close(deleteindex);
                                if(res.success){
                                    layer.msg("删除成功",{time: 1000},function(){
                                        table.reload('userListTable', userListTableOptions);
                                    });
                                }else{
                                    layer.msg(res.message);
                                }
                            }
                        });
                    }
                )
            }else{
                layer.msg("请选择需要删除的用户",{time:1000});
            }
        }
    };

    $('.layui-inline .layui-btn').on('click', function(){
        var type = $(this).data('type');
        active[type] ? active[type].call(this) : '';
    });
    /**/
    //搜索
    form.on("submit(searchForm)",function(data){
        userListTableOptions.where = data.field;
        table.reload('userListTable', userListTableOptions);
        return false;
    });

});