layui.extend({dtree: '/root/layui_ext/dtree/dtree'})
    .use(['form', 'jquery', 'layer', 'element', 'dtree'], function () {
        var form = layui.form,
            $ = layui.jquery,
            dtree = layui.dtree,
            layer = layui.layer;
        var roleId = $("input[name='id']").val();
        dtree.render({
            elem: "#sysRoleUpdateTree",
            url: "rolemenu?id=" + roleId,
            icon: "2",
            initLevel: -1,
            async: true,
            initLevel: "1",
            skin: "layui",
            response: {statusCode: 200, rootName: "data"},
            checkbar: true,
            checkbarType: "all",
            checkbarFun: {
                chooseBefore: function ($i, node) {
                    console.log($i);
                    return true;
                },
                chooseDone: function (nodes) {

                }
            }

        });

        form.on("submit(editRole)", function (data) {
            if (data.field.id == null) {
                layer.msg("角色ID不存在");
                return false;
            }
            var menus = [];

            var params = dtree.getCheckbarNodesParam("sysRoleUpdateTree");
            for (j = 0, len = params.length; j < len; j++) {
                var m = {},
                    item = params[j];
                if (item.ischecked) {
                    m.id = item.nodeId;
                    menus.push(m);
                }
            }
            data.field.roleMenuSet = menus;
            var loadIndex = layer.load(2, {
                shade: [0.3, '#333']
            });
            $.ajax({
                type: "POST",
                url: "/rbac/role/update",
                dataType: "json",
                contentType: "application/json",
                data: JSON.stringify(data.field),
                success: function (res) {
                    layer.close(loadIndex);
                    if (res.code == 200) {
                        parent.layer.msg(res.msg, {time: 1000}, function () {
                            //刷新父页面
                            parent.location.reload();
                        });
                    } else {
                        layer.msg(res.msg, {time: 1000}, function () {
                            //刷新本页面
                            location.reload();
                        });

                    }
                }
            });
            return false;
        });

    });