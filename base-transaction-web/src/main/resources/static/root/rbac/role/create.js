layui.extend({
    dtree: '/root/layui_ext/dtree/dtree'
}).use(['form','layer','jquery','element','dtree'], function(){
    var form = layui.form,
        layer = layui.layer,
        dtree = layui.dtree,
        $ = layui.jquery;


    dtree.render({
        elem: "#sysRoleCreateTree",
        url: "rolemenu",
        icon: "2",
        async:true,
        initLevel:-1,
        skin:"layui",
        checkbar: true,
        checkbarType: "all",
        checkbarFun: {
            chooseBefore: function($i, node){
                console.log($i);
                return true;
            },
            chooseDone: function(nodes){

            }
        }

    });

    form.on('submit(addRole)',function(data){
        var menus = [];

        var params = dtree.getCheckbarNodesParam("sysRoleCreateTree");
        for(j = 0,len=params.length; j < len; j++) {
            var m = {},
                item = params[j];
            if(item.ischecked){
                m.id = item.nodeId;
                menus.push(m);
            }
        }
        data.field.roleMenuSet = menus;
        var loadIndex = layer.load(2, {
            shade: [0.3, '#333']
        });
        $.ajax({
            type:"POST",
            url:"/rbac/role/create",
            dataType:"json",
            contentType:"application/json",
            data:JSON.stringify(data.field),
            success:function(res){
                layer.close(loadIndex);
                if(res.code == 200){
                    parent.layer.msg(res.msg,{time:1000},function(){
                        //刷新父页面
                        parent.location.reload();
                    });
                }else{
                    layer.msg(res.msg);
                }
            }
        });
        return false;
    });
});