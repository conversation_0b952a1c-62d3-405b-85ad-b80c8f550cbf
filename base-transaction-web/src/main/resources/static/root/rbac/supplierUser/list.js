layui.use(['layer', 'form', 'table', 'formSelects', 'util', 'admin'], function () {
    var $ = layui.jquery,
        layer = layui.layer,
        form = layui.form,
        table = layui.table,
        util = layui.util,
        formSelects = layui.formSelects;
    var admin = layui.admin;

    // 定义 “供应商URL” “供应商ID”
    var supplieruserURL, supplieruserIdTemp, idTemp, isHideCol = false;

// =============================================================== 首页列表逻辑js处理 Start ===============================================================//
    //渲染表格
    table.render({
        elem: '#supplierUserTable',
        url: '/supplieruser/getlist',
        // toolbar: '#customtoolbar',
        method: 'post',
        page: true,
        cellMinWidth: 100,
        response: {
            statusName: 'code' //规定数据状态的字段名称，默认：code
            ,statusCode: 200 //规定成功的状态码，默认：0
            ,msgName: 'msg' //规定状态信息的字段名称，默认：msg
            ,countName: 'count' //规定数据总数的字段名称，默认：count
            ,dataName: 'data' //规定数据列表的字段名称，默认：data
        },
        cols: [[
            {type: 'numbers', title: '序号'},
            {field: 'loginName', title: '登录账号'},
            {field: 'name', title: '供应商名称'},
            {
                templet: function (res) {
                    if(res.type == 1){
                        return '培训';
                    }else if(res.type == 2){
                        return '咨询';
                    }else if(res.type == 3){
                        return '招聘';
                    }else{
                        return '';
                    }
                }, title: '供应商类型'
            },
            {
                templet: function (res) {
                    return util.toDateString(res.createDate);
                }, title: '创建时间'
            },
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 200}
        ]],
        // skin: 'line',
        // size: 'lg'
        done: function () {
            layer.closeAll('loading');
        }
    });
    // ==================================== 公共方法 Start ====================================

    // 初始化 供应商类型
    initListType('supplierTypeCond');

    function initListType(typeId){
        var type = [{
            id: '1',
            name: '培训'
        },{
            id: '2',
            name: '咨询'
        },{
            id: '3',
            name: '招聘'
        }];

        if(typeId == 'supplierTypeCond'){
            initSelect('supplierTypeCond', type, "供应商类型");
        }else if(typeId == 'type'){
            initSelect('type', type, "供应商类型");
        }

        form.render();
    }

    // ==================================== 公共方法 End ====================================


    // 搜索按钮点击事件
    $('#userBtnSearch').click(function () {
        var supplierName = $('#supplierName').val();
        table.reload('supplierUserTable', {where: {name: supplierName}});
    });

    // 工具条点击事件
    table.on('tool(supplierUserTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'del') { // 删除账号
            doDelete(obj);
        }else if (layEvent === 'edit') { // 修改
            showEditModel(data, 'edit');
        }else if (layEvent === 'add') { // 新增
            showEditModel(data, 'add');
        }
    });

    // 删除
    function doDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要注销该用户吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);
            /* var url = '/consulting/deleteByID';
             apiCurrentPageDel(id, 'POST', url, callbackCloseCurrentPage, 'consultingTable', 'delete');
             return false;*/
            $.post("/supplieruser/deleteByID", {id: id}, function (res){
                layer.closeAll('loading');
                if(res.code == 200){
                    layer.msg("删除成功", {icon: 1});
                    obj.del();
                    table.reload('supplierUserTable');
                }else{
                    layer.msg( res.msg, {icon: 1});
                }
            });
        });
    }
/*    // 添加新增咨询点击事件
    $('#consultingAdd').click(function () {
        showEditModel();
    });
    // 添加新增供应商点击事件
    $(document).on('click', '#addSupplierButton', function () {
        showEditSupplierSateModel();
    });*/
// =============================================================== 首页列表逻辑js处理 Start ===============================================================//


// =============================================================== “业务流程”逻辑处理 Start ===============================================================//
    // 弹出“业务流程”新增与编辑窗口
    function showEditModel(data, showType) {
        layer.open({
            type: 1,
            //area: ['60%', '100%'],
            area: ['360px'],
            offset: '10%',
            // offset: '65px',
            title: showType == 'edit'?  '修改登陆账号' : '新增登陆账号',
            content: $('#supplierUserForm').html(),
            success: function (layero, index) {
// ======================== 新增逻辑处理 Start ========================
                // 初始化 项目类型，业务类型， 服务类型
                initListType('type');
                idTemp = data.id;
                supplieruserIdTemp = data.supplierId;

// ======================== 编辑逻辑处理 Start ========================
                if (showType == 'edit'){

                    isHideCol = false;
                    if(showType) {
                        isHideCol = true;
                    }
                    supplieruserURL = '/supplieruser/update';

                    form.val('supplierUserForm', data);
                }else if(showType == 'add'){
                    supplieruserURL = '/supplieruser/create';
                    $("#name").val(data.name);
                }

                form.render();

/*                // 控制form表单是否可以编辑
                if(showType == ) {
                    $('form').find('input,select,textarea').attr('disabled', true);
                    $("#testList").css('display', 'none');
                    // $("#closeDialog").css('display', 'none');
                    $("#consultingFormSubmit").css('display', 'none');
                }
                form.render();*/

                // “业务流程”表单提交处理
                form.on('submit(supplierUserFormSubmit)', function (d){
                    var data_row = d.field;

                    // var reg =  data_row.expectedRevenue.replace(/,/g,'');
                    //  预计营收
                   // var expectedRevenueTemp = data_row.expectedRevenue.replace(/[\ |\~|\`|\!|\@|\#|\$|\%|\^|\&|\*|\(|\)|\-|\_|\+|\=|\||\\|\[|\]|\{|\}|\;|\:|\"|\'|\,|\<|\.|\>|\/|\?]/g,"");


                    var param = {
                        // id
                        id: idTemp,
                        // 供应商Id
                        supplierId: supplieruserIdTemp,
                        // 登录名称
                        loginName: data_row.loginName,
                        // 供应商名称
                        name: data_row.name,
                        // 供应商类型
                        type: data_row.type
                    }
                    $.ajax({
                        type: 'POST',
                        url: supplieruserURL,//发送请求
                        contentType: "application/json; charset=utf-8",
                        async: true,
                        data: JSON.stringify(param),
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll("loading");
                            if(res.code == 200){
                                layer.msg("添加供应商登录用户成功！", {icon: 1});
                                layer.closeAll('page');
                                table.reload('supplierUserTable');
                            }else{
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    return false;
                });
            }
        });
    }


// =============================================================== “业务流程”逻辑处理 End ===============================================================//

});