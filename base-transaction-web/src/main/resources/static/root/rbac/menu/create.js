layui.config({
    base: '/root/treeSelect/' //你存放新模块的目录，注意，不是layui的模块目录
}).use(['form', 'element', 'treeSelect'], function (args) {
    var form = layui.form,
        $ = layui.jquery;
    form.render();
    var treeSelect = layui.treeSelect;

    treeSelect.render({
        elem: '#parentName',
        width: 300,
        height: 280,
        tree: {
            url: "/rbac/role/rolemenu",
            icon: "2",
            async: true,
            initLevel: -1,
            skin: "layui",
            checkbar: true,
            checkbarType: "only",
            checkbarFun: {
                chooseBefore: function ($i, node) {

                    return true;
                },
                chooseDone: function (nodes) {

                }
            }

        }
    });

    form.on('submit(addNode)', function (data) {
        var json = JSON.stringify(data.field);
        json = JSON.parse(json);
        var parentNode = JSON.parse(json.parentNameData);
        var nodeLevel = parseInt(parentNode.level) + 1;
        sysMenuItem = {
            "name": data.field.context
            , "level": nodeLevel
            , "parentId": parentNode.id
            , "parentIds": parentNode.parentIds
            , "permission": data.field.permission
            , "href": data.field.href
        };

        $.ajax({
            type: "POST",
            url: "/rbac/menu/create",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(sysMenuItem),
            success: function (result) {
                layer.msg(result.msg);
                if (result.code == 200) {
                    layer.closeAll();
                    window.parent.layui.dtree.reload('sysMenuTree');
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                } else {
                    layer.closeAll();
                }
            },
            error: function () {
                layer.closeAll();
            }
        })
        return false;
    });

});