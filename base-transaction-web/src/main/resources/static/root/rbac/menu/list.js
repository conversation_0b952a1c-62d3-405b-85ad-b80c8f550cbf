layui.extend({
    dtree: '/root/layui_ext/dtree/dtree'
}).use(['form','layer','jquery','element','dtree'], function(){
    var form = layui.form,
        layer = layui.layer,
        dtree = layui.dtree,
        $ = layui.jquery;

    var dtreeOptions = {
        elem: "#sysMenuTree",
        url: "list",
        icon: "2",
        initLevel:"1",
        async:true,
        toolbar:true,
        toolbarScroll:"#toolbarDiv",
        toolbarShow:["add","edit","delete"],
        toolbarBtn:[
            [{"label":"请求地址","name":"url","type":"text"}
                ,{"label":"请求权限","name":"permission","type":"text"}],
            [{"label":"请求地址","name":"url","type":"text"}
                ,{"label":"请求权限","name":"permission","type":"text"}
            ], // 自定义编辑页的内容，第一个[]是新增页的，咱不用管它
            []
        ],
        toolbarFun: {
            editTreeLoad: function(treeNode){
                // 这里可以发送ajax请求，来获取参数值，最后将参数值以form能识别的键值对json的形式返回
                var json = JSON.parse(treeNode.basicData);
                var param = {url:json.href, level:treeNode.level, permission:json.permission};
                Dtree.changeTreeNodeDone(param);
            },
            addTreeNode: function(treeNode){
                var param;
                // 这里可以发送ajax请求，来获取参数值，最后将参数值必须符合树response中定义的参数规范
                if (treeNode.addNodeName.toString().endsWith("root")) {
                    treeNode.addNodeName = treeNode.addNodeName.substr(0, treeNode.addNodeName.indexOf("root"))
                    param = 0;
                } else {
                    param = dtree.getParentParam(Dtree, treeNode.nodeId);
                    param = JSON.parse(param.basicData).parentIds;
                }
                onAddTreeNode(treeNode, param);
            },
            editTreeNode: function(treeNode){
                var sysMenuItem = {"name":treeNode.addNodeName
                    ,"id":treeNode.nodeId
                    ,"permission":treeNode.permission
                    ,"href":treeNode.url};
                update(sysMenuItem);
            },
            delTreeNode: function(treeNode){
                if(treeNode.isLeaf == true){
                    ondelete({"id":treeNode.nodeId});
                }else{
                    layer.msg("节点下还有其他资源，禁止删除");
                }

            }
        }
    };
    var Dtree = dtree.render(dtreeOptions);
    // 绑定节点的单击事件
    dtree.on("node('sysMenuTree')" ,function(obj){
        console.log(obj.param); // 点击当前节点传递的参数
        var inputs = $(".layui-input");
        var parentParam;
        if(obj.param.level == "1"){
            //根节点是没有父节点的；
            parentParam = {context:"根节点"};
        }else{
            parentParam =  dtree.getParentParam(Dtree,obj.param.nodeId);
        }
        var json = JSON.parse(obj.param.basicData);
        for(var i=0;i<inputs.length;i++){
            if(inputs[i].name == "nodeId"){
                $(inputs[i]).attr("value",obj.param.nodeId);
            }else if(inputs[i].name == "basicData"){
                $(inputs[i]).attr("value",obj.param.basicData);
            }else if(inputs[i].name == "isLeaf"){
                $(inputs[i]).attr("value",obj.param.isLeaf);
            }else if(inputs[i].name == "parentId"){
                $(inputs[i]).attr("value",obj.param.parentId);
            }else if(inputs[i].name == "parentName"){
                $(inputs[i]).attr("value",parentParam.context);
            }else if(inputs[i].name == "context"){
                $(inputs[i]).attr("value",obj.param.context);
            }else if(inputs[i].name == "href"){
                $(inputs[i]).attr("value",json.href);
            }else if(inputs[i].name == "permission"){
                $(inputs[i]).attr("value",json.permission);
            }else if(inputs[i].name == "level"){
                $(inputs[i]).attr("value",obj.param.level);
            }
        }
    });
    form.on('submit(updateNode)',function(data){
        if(data.field == null){
            return false;
        }
        var sysMenuItem = {"name":data.field.context
            ,"id":data.field.nodeId
            ,"permission":data.field.permission
            ,"href":data.field.href};
        update(sysMenuItem);
        resetForm();
        return false;
    });
    form.on('submit(deleteNode)',function (data) {
        if(data.field == null){
            layer.msg("请先选择要删除的节点");
            return false;
        }
        if(data.field.isLeaf == 'true'){
            ondelete({"id":data.field.nodeId});
        }else {
            layer.msg("节点下还有其他资源，禁止删除");
        }
        return false;
    });

    function resetForm() {
        var inputs = $(".layui-input");
        for(var i=0;i<inputs.length;i++){
            $(inputs[i]).val("");
        }
    }
    function onAddTreeNode(treeNode,param){
        var sysMenuItem;
        if (param == 0) {
            sysMenuItem = {
                "name": treeNode.addNodeName
                , "parentId": ""
                , "level": 1
                , "permission": treeNode.permission
                , "href": treeNode.url
            };
        } else {
            sysMenuItem = {
                "name": treeNode.addNodeName
                , "level": treeNode.level
                , "parentId": treeNode.parentId
                , "parentIds": param + treeNode.parentId + ","
                , "permission": treeNode.permission
                , "href": treeNode.url
            };
        }
        $.ajax({
            type:"POST",
            url:"/rbac/menu/create",
            dataType:"json",
            contentType:"application/json",
            data:JSON.stringify(sysMenuItem),
            success: function(result){
                layer.msg(result.msg);
                if(result.code == 200){
                    Dtree.changeTreeNodeAdd(treeNode.nodeId);
                    dtree.reload(Dtree,dtreeOptions);
                }else{
                    Dtree.changeTreeNodeAdd(false);
                }
            },
            error: function(){
                Dtree.changeTreeNodeAdd(false); // 配套使用,失败使用
            }
        })
    };
    function update(sysMenuItem) {
        $.ajax({
            type:"POST",
            url:"/rbac/menu/update",
            dataType:"json",
            contentType:"application/json",
            data:JSON.stringify(sysMenuItem),
            success: function(result){
                layer.msg(result.msg);
                if(result.code == 200){
                    dtree.reload(Dtree,dtreeOptions);
                }else{
                    Dtree.changeTreeNodeEdit(false);
                }
            },
            error: function(result){
                layer.msg(result.msg);
                Dtree.changeTreeNodeEdit(false); // 配套使用,失败使用
            }
        })
    };
    function ondelete(sysMenuItem) {
        $.ajax({
            type:"POST",
            url:"/rbac/menu/delete",
            dataType:"json",
            contentType:"application/json",
            data:JSON.stringify(sysMenuItem),
            success: function(result){
                layer.msg(result.msg);
                if(result.code == 200){
                    dtree.reload(Dtree,dtreeOptions);
                    resetForm();
                    Dtree.changeTreeNodeDel(true);
                }else{
                    Dtree.changeTreeNodeDel(false);
                }
            },
            error: function(result){
                layer.msg(result.msg);
                Dtree.changeTreeNodeEdit(false); // 配套使用,失败使用
            }
        })
    }

    window.addShow = function (title, url, w, h) {
        if (title == null || title == '') {
            title = false;
        }
        ;
        if (url == null || url == '') {
            url = "404.html";
        }
        ;
        if (w == null || w == '') {
            w = ($(window).width() * 0.9);
        }
        ;
        if (h == null || h == '') {
            h = ($(window).height() - 50);
        }
        ;
        layer.open({
            type: 2,
            area: [w + 'px', h + 'px'],
            fix: false, //不固定
            maxmin: true,
            shadeClose: true,
            shade: 0.4,
            title: title,
            content: url
        });
    }
});