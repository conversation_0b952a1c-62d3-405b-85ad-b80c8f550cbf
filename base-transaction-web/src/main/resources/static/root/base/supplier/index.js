layui.extend({
    dtree: '../../layui_ext/dtree/dtree'
}).use(['layer', 'form', 'table', 'util', 'admin', 'dtree', 'laydate', 'formSelects'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;
    var dtree = layui.dtree;
    var laydate = layui.laydate;
    var formSelects = layui.formSelects;
    var categoryIditem = '';
    var subcategoryiditem = '';

    //渲染表格
    table.render({
        elem: '#supplierTable',
        url: '/base/supplier/getlist',
        // toolbar: '#customtoolbar',
        method: 'post',
        page: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers', title: '序号'},
            {field: 'name', title: '企业名称'},
            {
                templet: function (res) {
                    var str = '';
                    if(res.subcategoryList.length > 0 ) {
                        str = res.subcategoryList[0].categoryName;
                    }
                    return str;

                }, title: '一级分类'
            },
            {
                templet: function (res) {
                    var str = '';
                    if(res.subcategoryList.length > 0 ) {
                        for (var i = 0; i < res.subcategoryList.length; i++) {
                            str += ('<span class="layui-badge-rim">' + res.subcategoryList[i].name + '</span>');
                        }
                    }
                    return str;

                }, title: '二级分类'
            },
            {field: 'legalPerson', title: '法人代表'},
            {
                templet: function (res) {
                    var str = '';
                    if(res.linkManList.length > 0 ) {
                        for (var i = 0; i < res.linkManList.length; i++) {
                            str += ('<span class="layui-badge-rim">' + res.linkManList[i].name + '</span>');
                        }
                    }
                    return str;

                }, title: '联系人'
            },
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 200}
        ]],
        response: {
            statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
        },
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
            return {
                "code": res.code, //解析接口状态
                "msg": res.msg, //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.data //解析数据列表
            };
        },
        done: function () {
            layer.closeAll('loading');
        }
    });

    function init(){
        // 渲染角色下拉框
        var categorySelData = new Array();
        for (var i = 0; i < mCategory.length; i++) {
            categorySelData.push({name: mCategory[i].name, value: mCategory[i].id});
        }
        formSelects.data('selCategory', 'local', {arr: categorySelData});
    }

    formSelects.on('selCategory', function(id, vals, val, isAdd, isDisabled){
        if(isAdd){
            categoryIditem = val.value;
        }else{
            categoryIditem = '';
        }

        showSubcategory(val.value);

    });

    formSelects.on('selSubcategory', function(id, vals, val, isAdd, isDisabled){
        if(isAdd){
            subcategoryiditem = val.value;
        }else{
            subcategoryiditem = '';
        }

    });

    // 根据选中的一级分类Id获取二级分类
    function showSubcategory(data) {
        $.get("/subcategory/getlistByCategoryId", {id: data}, function (res) {
            layer.closeAll('loading');
            if (res.code == 200) {
                mSubcategory = res.data;
                subcategoryCall();
            } else {
                layer.msg('获取一级分类数据失败');
            }
        });
    }

    function subcategoryCall(){
        // 处理数据
        var subcategorySelData = new Array();
        for (var i = 0; i < mSubcategory.length; i++) {
            subcategorySelData.push({name: mSubcategory[i].name, value: mSubcategory[i].id});
        }
        formSelects.data('selSubcategory', 'local', {arr: subcategorySelData});
    }

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        showEditModel();
    });

    //弹出绑定关系前的窗口
    $(document).on('click', '#btnAddLinkman', function () {
        layer.open({
            type: 1,
            title: false,
            closeBtn: true,
            shadeClose: true,
            offset: '65px',
            area: [640 + 'px', 400 + 'px'],
            content: $('#customLinkmanForm')
        });
    });


    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        var supplierName = $('#name').val();
        var categoryId = categoryIditem
        var subcategoryId = subcategoryiditem;
        // var keyword = $('#edtSearch').val();
        //table.reload('customTable', {where: {keyword: keyword}});
        table.reload('supplierTable', {where: {customName: supplierName, categoryId: categoryId, subcategoryId: subcategoryId}});
    });

    // 工具条点击事件
    table.on('tool(supplierTable)', function (obj) {
        var data = obj.data;
       // var dataCategoryId = data.subcategoryList[0].categoryId;
        var dataCategoryId;
        if(data.subcategoryList.length > 0){
            dataCategoryId = data.subcategoryList[0].categoryId;
        }
        var param = {id: data.id};
        if (obj.event === 'edit') { //修改
           // addShow('编辑', './edit?id=' + data.id + '&categoryIdss=' + dataCategoryId);
            if(dataCategoryId){
                addShow('编辑', './edit?id=' + data.id + '&categoryIdss=' + dataCategoryId);
            }else{
                addShow('编辑', './edit?id=' + data.id);
            }
        } else if (obj.event === 'del') { // 删除
            doDelete(obj);
        } else if (obj.event === 'detail') {  // 查看
           // addShow('查看', './view?id=' + data.id + '&categoryIdss=' + dataCategoryId);
            if(dataCategoryId){
                addShow('查看', './view?id=' + data.id + '&categoryIdss=' + dataCategoryId);
            }else{
                addShow('查看', './view?id=' + data.id);
            }
        }
    });

    $('#import').click(function () {
        showExcelModule();
    });

    $('#download').click(function () {
        downloadExcel();
    });

    // 删除
    function doDelete(obj) {
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);
            $.post("/base/supplier/deleteByID", {id:obj.data.id}, function (res){
                layer.closeAll('loading');
                if(res.code == 200){
                    layer.msg("删除成功");
                    layer.closeAll('page');
                    table.reload('supplierTable');
                }else{
                    layer.msg( res.msg);
                }
            });
        });
    }

    function downloadExcel() {
        var exportUrl = '/base/supplier/export2excel';
        window.open(exportUrl);
    }

    function showExcelModule() {
        window.addShow('导入', './import');
    }

    // 获取一级分类数据
    layer.load(2);
    $.get("/category/getall", {}, function (res) {
        layer.closeAll('loading');
        if(res.code == 200) {
            mCategory = res.data;
            init();
        } else {
            layer.msg('获取一级分类数据失败');
        }
    });

    // 初始化数据 标签控件
    // 获取供应商和标签的数据，并初始化对应的选择控件
    layer.load(2);
    $.get("/base/custom/getinit", {}, function (res) {
        layer.closeAll('loading');
        if(res.code == 200) {
            tagsData = res.data.tags;
            initSelect("tagIdCond", tagsData, "标签");
            teachersData = res.data.tags;
            form.render();
        } else {
            layer.msg('获取初始化数据失败');
        }
    });
});