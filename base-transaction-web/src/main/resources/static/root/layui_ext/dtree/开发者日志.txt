------------------------v2.4.5_finally(2018/12/21)【修复】：------------------------
1.【修复】单页应用中，未开始toolbar导致body上的所有事件失效。
2.【优化】更新dist文件夹中的dtree压缩
3.【优化】toolbar模块优化:新增节点时，当开启了record参数时不会将参数新增到节点上的问题。


------------------------v2.4.5_finally(2018/12/21)【优化】：------------------------
1.【优化】menubar模块优化:点击收缩时，当前页的所有树都收缩的问题。
2.【优化】menubar模块优化:点击删除复选框时，展开和收缩节点会报错，现在改为删除的节点如果有子集的话，子集会一并删除，但是回调函数中只会提供你勾中的数据。
3.【优化】基础方法优化:dataInit方法回显数据时，当前选中的数据无法被获取到的问题。
4.【优化】基础方法优化:parseData方法的basicData()方法，basicData如果未指定，前端的该项属性无数据，而不是""
5.【优化】异步属性优化:使用data加载时，可以允许指定的data为[]。
6.【优化】异步属性优化:使用data加载时，现在也有success和done的回调
7.【新增】checkbar模块新增：checkbarType新增属性："only"，用于模拟单选，只能同时选择一个复选框。
8.【新增】异步回调方法新增:新增success回调，用于数据加载完毕后的回调，在done之前执行
9.【新增】异步属性新增:filterRequest属性，用于过滤树自动发起的请求中你不需要的参数。
10.【新增】基础属性新增：record属性，开启数据记录模式，用于记录用户提供的JSON数据串中，指定当前节点的JSON数据（排除basicData字段和children字段）
11.【新增】基础属性新增：load属性，是否开启加载遮罩。
12.【新增】内置函数新增：getFilterRequestParam，获取filterParam过滤后的requestParam

------------------------v2.4.5_finally_beta(2018/12/07)【大改】：------------------------
0.【新增】提供的内容中新增dist/dtree.js压缩js文件。
1.【大改】css：css样式均加上dtree前缀。
2.【大改】图标：iconfont更改为dtreefont。
3.【大改】事件监听：图标点击事件返回的参数改为一个JSON对象，具体查看基础文档的事件监听。
4.【大改】事件监听：节点点击事件返回的参数改为一个JSON对象，具体查看基础文档的事件监听。
5.【大改】事件监听：节点双击事件返回的参数改为一个JSON对象，具体查看基础文档的事件监听。
6.【大改】事件监听：复选框点击事件返回的参数改为一个JSON对象，具体查看基础文档的事件监听。
7.【大改】事件监听：iframe加载完毕事件返回的参数改为一个JSON对象，具体查看基础文档的事件监听。
8.【新增】基础方法新增：getChildParam,获取全部下级节点
9.【新增】menubar模块新增：menubarTips属性,用于指定menubar的依附方式。
10.【新增】toolbar模块新增：toolbarFun新增loadToolbarBefore函数，用于呈现右键菜单之前调用的函数。
11.【新增】内置函数新增：initTreePlus 用于初始化菜单栏和工具栏的div。
12.【新增】内置函数新增：openTreePlus 用于开启工具栏和菜单栏。
13.【新增】内置函数新增：getMenubarDom 用于获取菜单栏。
14.【新增】内置函数新增：getExtMenubarDom 用于获取扩展菜单栏。
15.【新增】内置函数新增：getMenubarToolDom 用于获取依附在工具栏的菜单栏。
16.【新增】内置函数新增：getExtMenubarToolDom 用于获取依附在工具栏的扩展菜单栏。
17.【新增】内置函数新增：menubarMethod 用于menubar内置调用方法。
17.【新增】内置函数新增：menubarListener 用于menubar内置监听。
18.【新增】内置函数新增：setToolbarDom 用于设置工具栏按钮。
19.【新增】内置函数新增：unbindBrowserEvent 用于解绑浏览器事件。
19.【新增】内置属性新增：response属性中新增spread。
20.【优化】优化了图标显示。
21.【优化】toolbar模块:单页应用中，右键菜单的显示位置问题。
22.【优化】menubar模块:在开启了toolbar模块时按钮点击失效的问题。
23.【优化】iframe模块:iframeuUrl指定了"?"时，实际发送的url会出现两个"?"的问题。
23.【优化】数据格式:默认数据格式中非必须指定isLast属性，移除level属性的作用。
24.【修复】数据格式:在开启了list风格参数时，修复必须指定isLast返回的问题和设置initLevel无效的问题。
25.【修复】toolbar模块:新增节点返回json格式后显示数据未定义的问题。
26.【移除】内置函数移除：refreshTree，现放置在menubarMethod方法中。
27.【移除】内置函数移除：openAllNode，现放置在menubarMethod方法中。
28.【移除】内置函数移除：closeAllNode，现放置在menubarMethod方法中。
28.【移除】内置函数移除：loadChildTree。
29.【移除】内置函数移除：openMenubar。
30.【移除】内置函数移除：openToolbar。
31.【移除】内置属性移除：level。
32.【移除】内置属性移除：response属性中移除level。


------------------------v2.4.5(2018/11/30)【修复】：------------------------
1.【修复】基础方法修复：dataInit方法的返回数据异常。
2.【修复】基础方法修复：render函数自动识别是否重载树，代码再次修复(针对单页应用)。
3.【新增】内置函数新增：refreshTree,用于刷新树。
4.【新增】内置函数新增：parseData 用于解析数据。
5.【优化】checkbar模块：checkArr属性支持传递字符串。 
6.【修复】toolbar模块:拼接新增节点内容的代码异常 。

------------------------v2.4.5(2018/11/25)【修复】：------------------------
1.【修复】render函数自动识别是否重载树，代码修复。
2.【修复】内部代码bug。
3.【优化】修改了右键菜单弹出的动画效果。

------------------------v2.4.5(2018/11/25)【新增】：------------------------
1.【优化】优化了图标显示。
2.【新增】内置图标新增。
3.【新增】ficon属性：用于用户自定义一级图标
4.【新增】firstIconArray属性：用于用户自定义扩展一级图标
5.【新增】异步加载模块添加headers属性。
6.【新增】toolbar模块:新增属性toolbarExt，用于自定义扩展工具栏右键菜单按钮
7.【新增】checkbar模块:新增checkbarFun中的回调方法：chooseBefore，用于复选框点击前回调
8.【新增】内置函数新增:changeCheck,用于改变复选框状态。
9.【优化】render函数自动识别是否重载树。
10.【优化】内部代码优化。
11.【更新】更新了帮助文档

------------------------v2.4.5(2018/11/23)【修复】：------------------------
1.【修复】toolbar模块点击删除按钮时，控制台报错。
2.【修复】配置了dot:false时，toolbar模块新增节点后一级图标不显示的问题。
3.【修复】内置函数优化：initNoAllCheck，修复了显示bug
4.【新增】内置函数新增：initAllCheck，复选框选中状态初始化设置
5.【新增】内置函数新增：checkStatus，设置复选框选中/未选中/半选
6.【优化】toolbar模块：点击删除时，当删除了某个节点下的最后一个子节点，那该节点也会改变样式变成叶子节点。
7.【优化】toolbar模块:addTreeNode方法优化，ajax请求不限于同步，方法无需返回
8.【新增】内置函数新增：changeTreeNodeAdd，新增节点后改变节点内容
9.【优化】toolbar模块:editTreeNode方法优化，ajax请求不限于同步，方法无需返回
10.【新增】内置函数新增：changeTreeNodeEdit，编辑节点后改变节点内容
11.【优化】toolbar模块:delTreeNode方法优化，ajax请求不限于同步，方法无需返回
12.【新增】内置函数新增：changeTreeNodeDel，删除节点后改变节点内容
13.【优化】toolbar模块:editTreeLoad方法优化，ajax请求不限于同步，方法无需返回
14.【新增】内置函数新增：changeTreeNodeDone，编辑页打开后显示编辑页内容
15.【更新】更新了帮助文档

------------------------v2.4.5(2018/11/22)【修复】：------------------------
1.【修复】树重载时，使用data属性会造成数据重复加载问题。
2.【修复】输出参数的字段，spared修改为spread（之前单词拼错了。。。。）。
3.【新增】toolbar模块中新增属性：toolbarStyle，用于自定义toolbar的显示文字，弹框大小。
4.【移除】基础属性中移除了addIndex属性。
5.【新增】skin属性：用于用户自定义主题。
6.【更新】更新了帮助文档

------------------------v2.4.5(2018/11/21)【修复】：------------------------
1.【修复】使用dataFormat属性时，直接使用data属性配置节点内容失效的问题。
2.【修复】parentId为null导致节点数据加载失效的问题。
3.【优化】反选节点时将选中哪一级的节点展开，单选和多选均生效。
4.【新增】dot属性，用于用户自定义一级图标中的小圆点是否显示。
5.【更新】更新了帮助文档

------------------------v2.4.5(2018/11/21)【新增】：------------------------
1.【新增】toolbar取消按钮的显示，开始绑定右键点击事件。
2.【新增】toolbar模块新增一个属性：toolbarScroll 用于绑定树的上级div容器，让树可以显示滚动条的div容器，右键菜单绑定必填项。
3.【新增】toolbar模块新增编辑页数据回显功能。
4.【新增】树加载完毕后的回调函数
5.【新增】dataStyle属性，用于用户配置layui通用的json数据风格
6.【新增】dataFormat属性，用于用户自定义data中的数据格式（即支持传入一个大的list）
7.【更新】更新了帮助文档

------------------------v2.4.5(2018/11/09)【优化】：------------------------
1.【优化】更新了图标库，精简了图标(60个)

------------------------v2.4.5(2018/11/09)【初始】：------------------------
1.【初始】基本树形展示，无限级，支持自定义修改树的展示图标
2.【初始】支持异步/同步数据加载，支持静态数据加载，支持数据缓存
3.【初始】支持自定义返回json格式，支持自定义异步/同步加载参数
4.【初始】支持复选框，1-N级，支持复选框的四种选中形式，支持记录复选框选中/更改数据的回调
5.【初始】支持工具栏，即可直接修改当前树节点，新增/ 编辑/删除等
6.【初始】支持菜单栏，即可直接对树进行全部节点展开/收缩、删除全部选中节点、刷新树、搜索树等
7.【初始】支持加载iframe，即点击树节点时，可以带上一个访问iframe的url，这个设计在左树右内容风格的页面极为有用
8.【初始】支持数据回调，单击节点回调、双击节点回调、复选框选中回调、iframe加载完毕回调等
9.【初始】支持数据获取，即获取当前选中节点数据，当前选中复选框节点数据等
10.【初始】支持数据反显，即加载树之后将需要反显的节点高亮显示或选中