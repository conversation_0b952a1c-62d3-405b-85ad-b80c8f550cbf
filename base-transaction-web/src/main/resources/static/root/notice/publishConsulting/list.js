layui.use(['layer', 'form', 'table', 'util', 'laytpl', 'admin'], function () {
    var $ = layui.jquery,
        layer = layui.layer,
        form = layui.form,
        table = layui.table,
        util = layui.util,
        admin = layui.admin,
        laytpl = layui.laytpl;

    // 发布后台咨询 -- URL，ID，标识
    var pConsultURL, pConsultIds, isHideCol = false;

// =============================================================== 首页列表逻辑js处理 Start ===============================================================//
    //渲染表格
    table.render({
        elem: '#publishConsultingTable',
        url: '/publishconsultingprogram/getlist',
        method: 'post',
        page: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers', title: '序号'},
            {field: 'name', title: '项目名称'},
            {field: 'principal', title: '项目负责人'},
            {
                templet: function (res) {
                    if(res.state == 1){
                        return util.toDateString(res.publishDate);
                    }else{
                        return '';
                    }

                }, title: '发布时间'
            },
            {
                templet: function (res) {
                    if(res.state == 0){
                        return '未发布';
                    }else if(res.state == 1){
                        return '已发布';
                    }
                }, title: '项目状态'
            },
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 200}
        ]],
        response: {
            statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
        },
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
            return {
                "code": res.code, //解析接口状态
                "msg": res.msg, //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.data //解析数据列表
            };
        },
        done: function () {
            layer.closeAll('loading');
        }
    });
    // ==================================== 公共方法 Start ====================================


    // ==================================== 公共方法 End ====================================


    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        var nameCond = $('#nameCond').val();
        table.reload('publishConsultingTable', {where: {name: nameCond}});
    });

    // 工具条点击事件
    table.on('tool(publishConsultingTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'del') { // 删除
            doDelete(obj);
        } else if (layEvent === 'detail') { // 查看
            showEditModel(data, 'detail');
        } else if (layEvent === 'edit') { // 修改
            showEditModel(data);
        }else if(layEvent === 'send'){ // 发布
            updateStateModel(data, 'send');
        }else if(layEvent === 'cancelUpdate'){ // 撤回
            updateStateModel(data, 'cancelUpdate');
        }
    });

    // 删除
    function doDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);
            $.post("/publishconsultingprogram/deleteByID", {id: id}, function (res){
                layer.closeAll('loading');
                if(res.code == 200){
                    layer.msg("删除成功", {icon: 1});
                    obj.del();
                    table.reload('publishConsultingTable');
                }else{
                    layer.msg( res.msg, {icon: 1});
                }
            });
        });
    }
    // 添加新增咨询点击事件
    $('#publishTrainingAdd').click(function () {
        showEditModel();
    });
// =============================================================== 首页列表逻辑js处理 Start ===============================================================//

    // =============================================================== “后台发布咨询”逻辑处理 Start ===============================================================//
    // 弹出“后台发布咨询”新增与编辑窗口
    function showEditModel(data, showType) {
        layer.open({
            type: 1,
            area: ['60%', '100%'],
            // offset: '65px',
            title: showType? '查看咨询公告' : data ? '修改咨询公告' : '添加咨询公告',
            content: $('#publishConsultingForm').html(),
            success: function (layero, index) {
                if (data){
                    cooperRecords(data.id);
                    // 更改处理标识
                    isHideCol = false;
                    if(showType) {
                        isHideCol = true;
                    }
                    // 编辑模式--获取id
                    pConsultIds = data.id;
                    // 编辑模式--更改数据存储URL为Update
                    pConsultURL = '/publishconsultingprogram/update';
                    // 编辑模式--对填写的表单回写
                    form.val('publishConsultingForm', data);
                }else{
                    pConsultIds = '';
                    pConsultURL = '/publishconsultingprogram/create'

                }
                form.render();

                // 控制form表单是否可以编辑
                if(showType == 'detail') {
                    $('form').find('input,select,textarea').attr('disabled', true);
                    $("#publishConsultingFormSubmit").css('display', 'none');
                    $("#closeDialog").css('display', 'none');
                }
                form.render();

                // “后台发布咨询”表单提交处理
                form.on('submit(publishConsultingFormSubmit)', function (d){
                    layer.load(2);
                    var data_row = d.field;

                    // var reg =  data_row.expectedRevenue.replace(/,/g,'');
                    //  预算
                    var budgetTemp = data_row.budget.replace(/[\ |\~|\`|\!|\@|\#|\$|\%|\^|\&|\*|\(|\)|\-|\_|\+|\=|\||\\|\[|\]|\{|\}|\;|\:|\"|\'|\,|\<|\.|\>|\/|\?]/g,"");

                    if(!data_row.state){
                        data_row.state = 0;
                    }

                    var param = {
                        // id
                        id: pConsultIds,
                        // 项目名称
                        name: data_row.name,
                        // 客户信息
                        customInfo: data_row.customInfo,
                        // 项目负责人
                        principal: data_row.principal,
                        // 电话
                        phone: data_row.phone,
                        // 微信
                        wechatNumber: data_row.wechatNumber,
                        // 邮箱
                        email: data_row.email,
                        // 预算
                        budget: budgetTemp,
                        // 发布状态
                        state: data_row.state,
                        // 服务内容
                        serverContent: data_row.serverContent,
                        // 项目要求
                        programRequired: data_row.programRequired
                    }
                    $.ajax({
                        type: 'POST',
                        url: pConsultURL,//发送请求
                        contentType: "application/json; charset=utf-8",
                        async: true,
                        data: JSON.stringify(param),
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll('loading');
                            if(res.code == 200){
                                layer.msg("添加成功！", {icon: 1});
                                layer.closeAll('page');
                                table.reload('publishConsultingTable');
                            }else{
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    return false;
                });
            }
        });
    }

    // 发布
    function updateStateModel(data, btnType){

        var noticeMsg = '';

        if(btnType == 'send'){
            noticeMsg = '您确定要发布新的公告吗？';
            data.state = 1;
            //  data.updateDate = myDate.format.datetime("YYYY-MM-DD HH:mm:ss");
           // data.updateDate = layui.util.toDateString(new Date('YYYY-MM-DD HH:mm:ss').getTime());
        }else if(btnType == 'cancelUpdate'){
            noticeMsg = '您确定要撤回已发布的公告吗？';
            data.state = 0;
        }

        layer.confirm(noticeMsg, {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);

            $.ajax({
                type: 'POST',
                url: "/publishconsultingprogram/updateState",//发送请求
                contentType: "application/json; charset=utf-8",
                async: true,
                data: JSON.stringify(data),
                dataType: "json",
                success: function (res) {
                    // layer.closeAll("loading");
                    if(res.code == 200){
                        layer.msg("发布成功！", {icon: 1});
                        table.reload('publishConsultingTable');
                    }else{
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });

        });
    }

    // 访问记录
    function cooperRecords(obj){

        table.render({
            elem: '#publishTrainingLoginRecordTable',
            url: '/publishconsultingprogram/getlistAccesslog?programId=' + obj,
            // toolbar: '#customtoolbar',
            //  data: loginData,
            method: 'get',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers', title: '序号'},
                {
                    templet: function (res) {
                        return res.supplierUserList[0].name;

                    }, title: '供应商名称'
                },
                {
                    templet: function (res) {
                        //return util.toDateString(res.updateDate, "yyyy-MM-dd");
                        return util.toDateString(res.accessDate);

                    }, title: '发布时间'
                }
            ]],
            response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            },
            parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.data //解析数据列表
                };
            },
            done: function () {
                layer.closeAll('loading');
            }
        });
    }

});