layui.use(['layer', 'form', 'table', 'util', 'laytpl', 'admin'], function () {
    var $ = layui.jquery,
        layer = layui.layer,
        form = layui.form,
        table = layui.table,
        util = layui.util,
        admin = layui.admin,
        laytpl = layui.laytpl;

    // 发布培训--URL， 发布培训--id， 隐藏标识
    var pTrainingURL, pTrainingIds, isHideCol = false;

// =============================================================== 首页列表逻辑js处理 Start ===============================================================//
    //渲染表格
    table.render({
        elem: '#publishTrainingTable',
        url: '/publishtrainingprogram/getlist',
        method: 'post',
        page: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers', title: '序号'},
            {field: 'name', title: '项目名称'},
            {field: 'principal', title: '项目负责人'},
            {
                templet: function (res) {
                    //return util.toDateString(res.updateDate, "yyyy-MM-dd");
                    if(res.state == 1){
                        return util.toDateString(res.publishDate);
                    }else{
                        return '';
                    }

                }, title: '发布时间'
            },
            {
                templet: function (res) {
                    if(res.state == 0){
                        return '未发布';
                    }else if(res.state == 1){
                        return '已发布';
                    }
                }, title: '项目状态'
            },
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 200}
        ]],
        response: {
            statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
        },
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
            return {
                "code": res.code, //解析接口状态
                "msg": res.msg, //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.data //解析数据列表
            };
        },
        done: function () {
            layer.closeAll('loading');
        }
    });

    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        var nameCond = $('#nameCond').val();
        table.reload('publishTrainingTable', {where: {name: nameCond}});
    });

    // 工具条点击事件
    table.on('tool(publishTrainingTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'del') { // 删除
            doDelete(obj);
        } else if (layEvent === 'detail') { // 查看
            showEditModel(data, 'detail');
        } else if (layEvent === 'edit') { // 修改
            showEditModel(data);
        }else if(layEvent === 'send'){
            updateStateModel(data, 'send');
        }else if(layEvent === 'cancelUpdate'){
            updateStateModel(data, 'cancelUpdate');
        }
    });

    // 删除
    function doDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);
            $.post("/publishtrainingprogram/deleteByID", {id: id}, function (res){
                layer.closeAll('loading');
                if(res.code == 200){
                    layer.msg("删除成功", {icon: 1});
                    obj.del();
                    table.reload('publishTrainingTable');
                }else{
                    layer.msg( res.msg, {icon: 1});
                }
            });
        });
    }

    // 添加新增咨询点击事件
    $('#publishTrainingAdd').click(function () {
        showEditModel();
    });
// =============================================================== 首页列表逻辑js处理 Start ===============================================================//

    // =============================================================== “后台发布培训” 逻辑处理 Start ===============================================================//
    // 弹出“后台发布培训”新增与编辑窗口
    function showEditModel(data, showType) {
        layer.open({
            type: 1,
            area: ['60%', '100%'],
            // offset: '65px',
            title: showType? '查看培训公告' : data ? '修改培训公告' : '添加培训公告',
            content: $('#publishTrainingForm').html(),
            success: function (layero, index) {
                if (data){
                    cooperRecords(data.id);
                    // 更改隐藏标识
                    isHideCol = false;
                    if(showType) {
                        isHideCol = true;
                    }
                    // 编辑模式--获取id
                    pTrainingIds = data.id;
                    // 编辑模式--更改数据存储URL为Update
                    pTrainingURL = '/publishtrainingprogram/update';
                    // 编辑模式--对填写的表单回写
                    form.val('publishTrainingForm', data);
                }else{
                    // 新增模式--初始化id null
                    pTrainingIds = '';
                    // 新增模式--更改数据存储URL为Create
                    pTrainingURL = '/publishtrainingprogram/create';
                }
                // 控制form表单是否可以编辑
                if(showType == 'detail') {
                    $('form').find('input,select,textarea').attr('disabled', true);
                    $("#publishTrainingFormSubmit").css('display', 'none');
                    $("#closeDialog").css('display', 'none');


                }
                form.render();

                // “后台发布培训”表单提交处理
                form.on('submit(publishTrainingFormSubmit)', function (d){
                    layer.load(2);
                    var data_row = d.field;

                    // var reg =  data_row.expectedRevenue.replace(/,/g,'');
                    //  预算
                    var budgetTemp = data_row.budget.replace(/[\ |\~|\`|\!|\@|\#|\$|\%|\^|\&|\*|\(|\)|\-|\_|\+|\=|\||\\|\[|\]|\{|\}|\;|\:|\"|\'|\,|\<|\.|\>|\/|\?]/g,"");

                    if(!data_row.state){
                        data_row.state = 0;
                    }

                    var param = {
                        // id
                        id: pTrainingIds,
                        // 项目名称
                        name: data_row.name,
                        // 客户信息
                        customInfo: data_row.customInfo,
                        // 项目负责人
                        principal: data_row.principal,
                        // 电话
                        phone: data_row.phone,
                        // 微信
                        wechatNumber: data_row.wechatNumber,
                        // 邮箱
                        email: data_row.email,
                        // 培训人数
                        personNumber: data_row.personNumber,
                        // 培训范围
                        trainingScope: data_row.trainingScope,
                        // 时长
                        duration: data_row.duration,
                        // 预算
                        budget: budgetTemp,
                        // 发布状态
                        state: data_row.state,
                        // 课程需求
                        courseRequirements: data_row.courseRequirements,
                        // 讲师需求
                        teacherRequirements: data_row.teacherRequirements
                    }
                    $.ajax({
                        type: 'POST',
                        url: pTrainingURL,//发送请求
                        contentType: "application/json; charset=utf-8",
                        async: true,
                        data: JSON.stringify(param),
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll('loading');
                            if(res.code == 200){
                                layer.msg("添加成功！", {icon: 1});
                                layer.closeAll('page');
                                table.reload('publishTrainingTable');
                            }else{
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    return false;
                });
            }
        });
    }

    // 发布
    function updateStateModel(data, btnType){

        var noticeMsg = '';

        if(btnType == 'send'){
            noticeMsg = '您确定要发布新的公告吗？';
            data.state = 1;
            //  data.updateDate = myDate.format.datetime("YYYY-MM-DD HH:mm:ss");
           // data.publishDate = layui.util.toDateString(new Date('YYYY-MM-DD HH:mm:ss').getTime());
        }else if(btnType == 'cancelUpdate'){
            noticeMsg = '您确定要撤回已发布的公告吗？';
            data.state = 0;
        }

        layer.confirm(noticeMsg, {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);

            $.ajax({
                type: 'POST',
                url: "/publishtrainingprogram/updateState",//发送请求
                contentType: "application/json; charset=utf-8",
                async: true,
                data: JSON.stringify(data),
                dataType: "json",
                success: function (res) {
                    // layer.closeAll("loading");
                    if(res.code == 200){
                        layer.msg("发布成功！", {icon: 1});
                        table.reload('publishTrainingTable');
                    }else{
                        layer.msg(res.msg, {icon: 2});
                    }
                }
            });

        });


    }

    // 访问记录
    function cooperRecords(obj){
        table.render({
            elem: '#publishTrainingLoginRecordTable',
            url: '/publishtrainingprogram/getlistAccesslog?programId=' + obj,
            // toolbar: '#customtoolbar',
            //  data: loginData,
            method: 'get',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers', title: '序号'},
                {
                    templet: function (res) {
                        return res.supplierUserList[0].name;

                    }, title: '供应商名称'
                },
                {
                    templet: function (res) {
                        //return util.toDateString(res.updateDate, "yyyy-MM-dd");
                        return util.toDateString(res.accessDate);

                    }, title: '发布时间'
                }
            ]],
            response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            },
            parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.data //解析数据列表
                };
            },
            done: function () {
                layer.closeAll('loading');
            }
        });
    }

    // =============================================================== “后台发布培训” 逻辑处理 End ===============================================================//

});