layui.define(['jquery', 'table'], function (exports) {
    var $ = layui.jquery;
    var table = layui.table;
    httpRequestApi = function (param, type, url, Callback, tableId) {
        $.ajax({
            type: type,
            url: url,//发送请求
            contentType:  "application/x-www-form-urlencoded; charset=UTF-8", //"application/json; charset=utf-8",
            async: true,
            data: param,
            dataType: "json",
            success: function (result) {
                var htmlresult = result;//返回的结果页面
                Callback(htmlresult, tableId);
            }
        });
    }

    httpRequestApiCreate = function (param, type, url, Callback, tableId) {
        $.ajax({
            type: type,
            url: url,//发送请求
            contentType: "application/json; charset=utf-8",
            async: true,
            data: JSON.stringify(param),
            dataType: "json",
            success: function (result) {
                var htmlresult = result;//返回的结果页面
                Callback(htmlresult, tableId);
            }
        });
    }

    callbackClosePage = function (result, tableId) {
        if (result.code === 200) {
            layer.msg("删除成功");
            layer.closeAll('page');
            table.reload(tableId);
        }else{
            layer.msg(result.msg);
        }
    };
});