var testMode = false;

layui.define(['jquery', 'table'], function (exports) {
    var $ = layui.jquery;
    var table = layui.table;
    api = function (param, type, url, Callback, obj, msgType) {
        $.ajax({
            type: type,
            url: url,//发送请求
            contentType: "application/json; charset=utf-8",
            async: true,
            data: JSON.stringify(param),
            dataType: "json",
            success: function (result) {
                var htmlresult = result;//返回的结果页面
                Callback(htmlresult, obj, msgType);
            }
        });
    }

    apiSynchronized = function (param, type, url, Callback, obj) {
        $.ajax({
            type: type,
            url: url,//发送请求
            contentType: "application/json; charset=utf-8",
            async: false,
            data: JSON.stringify(param),
            dataType: "json",
            success: function (result) {
                var htmlresult = result;//返回的结果页面
                Callback(htmlresult, obj);
            }
        });
    }

    apiCurrentPageDel = function (param, type, url, Callback, obj, msgType) {
        $.ajax({
            type: type,
            url: url,//发送请求
            contentType:  "application/x-www-form-urlencoded; charset=UTF-8", //"application/json; charset=utf-8",
            async: true,
            data: param,
            dataType: "json",
            success: function (result) {
                var htmlresult = result;//返回的结果页面
                Callback(htmlresult, obj, msgType);
            }
        });
    }

    callbackCloseCurrentPage = function (result, obj, msgType) {
        var msgTemp;
        if(msgType == 'create'){
            msgTemp = '新增信息成功！';
        }else if(msgType == 'update'){
            msgTemp = '修改信息成功！';
        }else if(msgType == 'delete'){
            msgTemp = '删除信息成功！';
        }
        if (result.code === 200) {
            parent.layer.msg(msgTemp, {icon: 1});
            layer.closeAll('page');
            table.reload(obj);
        }else{
            layer.msg(result.msg);
        }
    };

    /**
     * 用于弹出页操作后的回调，自动刷新父页面table
     * @param result
     * @param obj 要刷新的父页面表格
     */
    callbackCloseChildPage = function (result, obj, msgType) {
        testModeAlert('进来callbackCloseChildPage：' + obj);
        var msgTemp;
        if(msgType == 'create'){
            msgTemp = '新增信息成功！';
        }else if(msgType == 'update'){
            msgTemp = '修改信息成功！';
        }else if(msgType == 'delete'){
            msgTemp = '删除信息成功！';
        }

        if (result.code === 200) {
            parent.layer.msg(msgTemp, {icon: 1});
           // layer.closeAll();
            window.parent.layui.table.reload(obj);
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
/*            layer.open({
                type: 1
                , content: '<div style="padding: 20px 100px;"> 操作成功！</div>'
                , btn: '关闭'
                , btnAlign: 'c' //按钮居中
                , shade: 0 //不显示遮罩
                , yes: function () {
                    layer.closeAll();
                    window.parent.layui.table.reload(obj);
                    var index = parent.layer.getFrameIndex(window.name);
                    parent.layer.close(index);
                }
            });*/
        } else {
            layer.open({
                type: 1
                , content: '<div style="padding: 20px 100px;">' + result.msg + '操作失败！</div>'
                , btn: '关闭'
                , btnAlign: 'c' //按钮居中
                , shade: 0 //不显示遮罩
                , yes: function () {
                    layer.closeAll();
                }
            });
        }
    };

    /**
     * 用于异步回调处理
     * @param result
     * @param obj 预留扩展的参数
     */
    callbackCurrentPage = function (result, obj) {
        testModeAlert('进来callbackCurrentPage：' + obj);

        if (result.code === 200) {
            layer.open({
                type: 1
                , content: '<div style="padding: 20px 100px;"> 操作成功！</div>'
                , btn: '关闭'
                , btnAlign: 'c' //按钮居中
                , shade: 0 //不显示遮罩
                , yes: function () {
                    layer.closeAll();
                    layui.table.reload(obj);
                }
            });
        } else {
            layer.open({
                type: 1
                , content: '<div style="padding: 20px 100px;">' + result.msg + '操作失败！</div>'
                , btn: '关闭'
                , btnAlign: 'c' //按钮居中
                , shade: 0 //不显示遮罩
                , yes: function () {
                    layer.closeAll();
                }
            });
        }
    };

    window.addShow = function (title, url) {
        testModeAlert('进来window.addShow：' + title + "::" + url);

        if (title == null || title === '') {
            title = false;
        }
        if (url == null || url === '') {
            url = "404.html";
        }
        var index = layer.open({
            type: 2,
            title: title,
            area: ['1024px', '100%'],
            content: url
        });
        // $(window).resize(function () {
        //     layer.full(index);
        // });
        // layer.full(index);
    };

    setParamValue = function (obj) {
        return obj === "" ? null : obj;
    }

});

function testModeAlert(obj) {
    if (testMode) {
        alert(obj);
    }
}