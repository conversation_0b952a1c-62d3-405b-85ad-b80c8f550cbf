/**
 * 请求地址为baseController的路径，暂时用这个写法
 * @type {string}
 */
layui.define(['table', 'layer', 'form', 'util', 'laydate', 'dtree', 'jquery'], function (exports) {
    var dtree = layui.dtree;
    var laydate = layui.laydate;

    laydate.render({
        elem: '#dateRangePick'
        , type: 'date'
        , range: true //或 range: '~' 来自定义分割字符
    });

    var obj = {
        //查询并渲染数据
        initTree: function () {
            dtree.render({
                elem: "#leftTree",
                url: "/property/building/listDtree",
                icon: "2",
                async: true,
                skin: "layui",
                width: 100,
                response: {statusCode: 200, rootName: "data"},
                checkbar: true,
                checkbarType: "all",
                checkbarFun: {
                    chooseBefore: function ($i, node) {
                        console.log($i);
                        return true;
                    },
                    chooseDone: function (nodes) {

                    }
                }
            })
        },

        /**
         * 闭包资源树
         * @param erpTreeRender 基于人力资源树的联动查询列表函数
         */
        showTreeNode: function (erpTreeRender) {
            dtree.on("node('leftTree')", function (obj) {
                var data = obj.param;
                //目前只传部门对应的物业项目id进行关联
                var itemId_ = data.basicData;
                var itemId = eval(itemId_);
                if (itemId != null) {
                    erpTreeRender(itemId);
                }
                var storage=window.localStorage;
                storage["itemId"]=itemId;
            })
        }
    };
    //输出接口
    exports('erpLeftTree', obj);

});