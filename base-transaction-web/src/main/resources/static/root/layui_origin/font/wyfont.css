
@font-face {font-family: "iconfont";
    src: url('font_tnyc012u2rlwstt9.eot'); /* IE9*/
    src: url('font_tnyc012u2rlwstt9.eot') format('embedded-opentype'), /* IE6-IE8 */
    url('font_tnyc012u2rlwstt9.woff') format('woff'), /* chrome, firefox */
    url('font_tnyc012u2rlwstt9.ttf') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
    url('font_tnyc012u2rlwstt9.svg') format('svg'); /* iOS 4.1- */
}

.iconfont {
    font-family:"iconfont" !important;
    font-size:16px;
    font-style:normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-zhanghu:before { content: "\e605"; }

.icon-lock1:before { content: "\e622"; }

.icon-erweima:before { content: "\e62d"; }

.icon-xinlangweibo:before { content: "\e63d"; }

.icon-qq:before { content: "\e63e"; }

.icon-icon:before { content: "\e609"; }

.icon-edit:before { content: "\e602"; }

.icon-computer:before { content: "\e645"; }

.icon-text:before { content: "\e64d"; }

.icon-guanbi:before { content: "\e62f"; }

.icon-loginout:before { content: "\e608"; }

.icon-shuaxin1:before { content: "\e648"; }

.icon-shezhi1:before { content: "\e64a"; }

.icon-gonggao:before { content: "\e614"; }

.icon-menu1:before { content: "\e62a"; }

.icon-wenben:before { content: "\e600"; }

.icon-dengji3:before { content: "\e61e"; }

.icon-dengji1:before { content: "\e628"; }

.icon-dengji2:before { content: "\e629"; }

.icon-dengji4:before { content: "\e62b"; }

.icon-dengji5:before { content: "\e62c"; }

.icon-dengji6:before { content: "\e62e"; }

.icon-new1:before { content: "\e610"; }

.icon-huanfu:before { content: "\e623"; }

.icon-link:before { content: "\e657"; }

.icon-lingsheng:before { content: "\e601"; }

.icon-star:before { content: "\e783"; }

.icon-dongtaifensishu:before { content: "\e603"; }

.icon-prohibit:before { content: "\e73a"; }

.icon-caozuo:before { content: "\e64e"; }

.icon-weather:before { content: "\e89e"; }

