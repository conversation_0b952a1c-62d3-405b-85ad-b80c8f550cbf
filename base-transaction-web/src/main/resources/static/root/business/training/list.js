layui.use(['layer', 'form', 'table', 'formSelects', 'laydate', 'admin', 'upload', 'element'], function () {
    var $ = layui.jquery;
    var element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;
    var formSelects = layui.formSelects;
    var admin = layui.admin;
    var util = layui.util;
    var upload = layui.upload;

// ==========================================初始化变量 start===============================================
    // 定义一些需要初始化的数据
    var tagsData, customsData, memberTabData = [], userData, removeUserData=[],schemeTabData = [],schemeData, removeSchemeData = [],
        finalSchemeData = [],courseTabData = [], fileIds, programIdVal, isHideCol = false, supplierTabData = [], supplierData, removeSupplierData = [],
        isCanChangeTag = false, internalProIdTemp, internalURL;
    var methodObj = {"1": "公开招标", "2": "邀请招标", "3": "网络竞价", "4": "竞争性谈判（公开）", "5": "单一来源", "6": "询价",
        "7": "内部邀请招标", "8": "竞争性谈判（公开）", "9": "市场询价", "10": "商务谈判", "11": "其他"};
    var stateObj = {"1": "未开始", "2": "进行中", "3": "已结束", "4": "延期", "5": "中止", "6": "其他"};
// ==========================================初始化变量 end=================================================

// ==========================================渲染主页 start===============================================
    //渲染表格
    table.render({
        elem: '#trainingTable',
        url: '/trainingprogram/getlist',
        method: 'post',
        page: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers', title: '序号'},
            {field: 'name', title: '项目名称'},
            {
                templet: function (res) {
                    if(res.type == 1){
                        return '年度项目';
                    }else if(res.type == 2){
                        return '单个项目';
                    }else if(res.type == 3){
                        return '系列项目';
                    }
                    return "";

                }, title: '项目类型'
            },
            {
                templet: function (res) {
                    if(res.belong == 1){
                        return '集团内业务';
                    }else if(res.belong == 2){
                        return '集团外业务';
                    }
                    return "";

                }, title: '业务类型'
            },
            {
                templet: function (d) {
                    var str = '';
                    if(d.projectMemberVos != undefined) {
                        for (var i = 0; i < d.projectMemberVos.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.projectMemberVos[i].name + '</span>');
                        }
                    }
                    return str;
                }, title: '项目成员'
            },
            {
                templet: function (res) {
                    if(res.state == 1) {
                        return '未开始';
                    }else if(res.state == 2){
                        return '正常开展';
                    }else if(res.state == 3){
                        return '已完成';
                    }else if(res.state == 4){
                        return '延期';
                    }else if(res.state == 5){
                        return '中止';
                    }else if(res.state == 6){
                        return '其他';
                    }
                    return "";
                }, title: '项目状态'
            },
            {
                templet: function (d) {
                    return util.toDateString(d.createDate, "yyyy-MM-dd");
                }, title: '创建时间'
            },
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 200}
        ]],
        response: {
            statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
        },
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
            return {
                "code": res.code, //解析接口状态
                "msg": res.msg, //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.data //解析数据列表
            };
        },
        done: function () {
            layer.closeAll('loading');
        }
    });

    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        var trainingName = $('#trainingName').val();
        var projectType = $('#projectType').val();
        var serviceType = $('#serviceType').val();
        table.reload('trainingTable', {where: {trainingName: trainingName, projectType: projectType, serviceType: serviceType}});
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        programIdVal = '';
        showEditModel();
    });

    // 供应商状态新增按钮事件
    $(document).on('click', '#addSupplierButton', function () {
        showEditSupplierSateModel();
    });

    // 工具条点击事件
    table.on('tool(trainingTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;

        if (layEvent === 'del') { // 删除
            doDelete(obj);
        } else if (layEvent === 'detail') { // 查看
            showEditModel(data, 'detail');
        } else if (layEvent === 'edit') { // 修改
            showEditModel(data);
        }
    });

    // 删除
    function doDelete(obj) {
        layer.confirm('确定要删除吗？', {
            offset: '65px',
        }, function (i) {
            layer.close(i);
            layer.load(2);
            $.post("/trainingprogram/deleteByID", {id: obj.data.id}, function (res){
                layer.closeAll('loading');
                if(res.code == 200){
                    layer.msg("删除成功");
                    obj.del();
                }else{
                    layer.msg(res.msg);
                }
            });
        });
    }
// ==========================================渲染主页 end===============================================

// ==========================================培训项目弹框 start===============================================
    // 显示表单弹窗
    function showEditModel(data, showType) {
        fileIds = new Array();
        isCanChangeTag = false;
        layer.open({
            type: 1,
            area: ['61%', '100%'],
            // offset: '65px',
            title: showType? '查看培训业务' : data ? '修改培训业务' : '添加培训业务',
            content: $('#trainingForm').html(),
            success: function (layero, index) {
                // tab切换标签触发函数
                element.on('tab(docDemoTabBrief)', function(d){
                    if(d.index == 0){  // 表示为业务标签

                    }else{ // 表示为内部流程标签
                        changeInternalShow();
                        getSupplierData();
                        // 内部流程处理
                        internalProgramHandle();
                    }

                });

                // 处理当前tab标签是否可以切换
                if(data) {
                    isCanChangeTag = true;
                } else {
                    isCanChangeTag = false;
                }
                changeInternalShow();

                // 培训业务处理
                trainingBusinessHandle(data, showType);
            }
        });
    }

    // 培训业务处理
    function trainingBusinessHandle(data, showType) {
        // 初始化时间控件
        laydate.render({
            elem: '#starttime' //指定元素
            ,done: function(value, date, endDate){
                var eDate = $("#finishtime").val();
                if(eDate!="" && compareDate(value, eDate)) {
                    $("#finishtime").val(value)
                }
            }
        });

        laydate.render({
            elem: '#finishtime' //指定元素
            ,done: function(value, date, endDate){
                var sDate = $("#starttime").val();
                if(endDate!="" && compareDate(sDate, value)) {
                    $("#starttime").val(value)
                }
            }
        });

        // 渲染标签下拉框
        var tagSelData = new Array();
        for (var i = 0; i < tagsData.length; i++) {
            tagSelData.push({name: tagsData[i].name, value: tagsData[i].id});
        }
        formSelects.data('selTag', 'local', {arr: tagSelData});

        // 渲染客户
        initSelect("customId", customsData, "客户");

        // 渲染下载模板（只有编辑和查看需要显示下载按钮）
        showModuleTab();

        // 查询当前业务的项目成员
        getUserData();

        // 查询方案数据
        getSchemesData();

        if(!data) {
            $('#moduleList').find('.data-download1').addClass('layui-hide');
            // 渲染项目成员
            showMemberTab();
            // 渲染项目方案
            showSchemeTab();
            // 渲染项目排期
            showCourseTab();
        } else {
            programIdVal = data.id;
            // 处理下时间数据
            data.starttime = data.starttime == undefined ? '' : data.starttime.split(" ")[0];
            data.finishtime = data.finishtime == undefined ? '' : data.finishtime.split(" ")[0];
            form.val('trainingForm', data);
            $.get("/tag/getTag", {correlatorId: data.id}, function (res) {
                if(res.code == 200) {
                    var rds = new Array();
                    for (var i = 0; i < res.data.length; i++) {
                        rds.push(res.data[i].id);
                    }
                    formSelects.value('selTag', rds);  // 回显多选框
                }
            });
            // 渲染客户表格
            var text = $("#customId option:selected").text();
            $.get("/linkman/getLinkMans", {customId: data.customId}, function (res) {
                if(res.code == 200) {
                    showCustomTab(res.data, text)
                }
            });

            isHideCol = false;
            if(showType) {
                isHideCol = true;
            }
            // 渲染项目成员
            showMemberTab(data.id);
            // 渲染项目方案
            showSchemeTab(data.id);
            // 渲染项目排期
            showCourseTab(data.id);
            // 渲染文件
            initFileTab();
        }
        // 渲染文件上传控件信息
        loadFileUpload();

        // 控制form表单是否可以编辑
        if(showType) {
            $('form').find('input,select,textarea').attr('disabled', true);
            $("#testList").css('display', 'none');
            $("#cancleBtn").css('display', 'none');
            $("#programFormSubmit").css('display', 'none');
            $('#demoList').find('.data-delete').addClass('layui-hide');
            formSelects.disabled();

            $("#addMemberButton").css('display', 'none');
            $("#addSchemeButton").css('display', 'none');
            $("#addCourseButton").css('display', 'none');
        }
        form.render();
        // 表单提交事件
        form.on('submit(programFormSubmit)', function (d) {

            var trainingName = $('#trainingName').val();
            var projectType = $('#projectType').val();
            var serviceType = $('#serviceType').val();

            // 判断当前的项目成员是否为空,
            if(memberTabData.length == 0) {
                layer.msg("项目成员数据需要维护！", {icon: 2});
                return false;
            }
            layer.load(2);
            // 组装数据
            // 处理下标签
            var tempTags = [];
            if(d.field.tagId != '') {
                var tag = d.field.tagId.split(",");
                for(var i=0; i<tag.length; i++) {
                    tempTags.push({id: tag[i]});
                }
            }
            var param = {
                id: d.field.id || programIdVal
                ,name: d.field.name
                ,type: d.field.type
                ,belong: d.field.belong
                ,expectedRevenue: d.field.expectedRevenue
                ,projectedCost: d.field.projectedCost
                ,starttime: d.field.starttime
                ,finishtime: d.field.finishtime
                ,personNumber: d.field.personNumber
                ,trainingObject: d.field.trainingObject
                ,serverPeople: d.field.serverPeople
                ,tags: tempTags
                ,customId: d.field.customId
                ,courseRequirements: d.field.courseRequirements
                ,teacherRequirements: d.field.teacherRequirements
                ,comment: d.field.comment
                ,projectMemberVos: memberTabData
                ,trainingProgramSchemas: schemeTabData
                ,courses: courseTabData
                ,summary: d.field.summary
                ,grade: d.field.grade
                ,state: d.field.state
            };
            if(data || programIdVal) {
                $.ajax({
                    type: 'POST',
                    url: '/trainingprogram/update',//发送请求
                    contentType: "application/json; charset=utf-8",
                    async: true,
                    data: JSON.stringify(param),
                    dataType: "json",
                    success: function (res) {
                        layer.closeAll("loading");
                        if(res.code == 200){
                            layer.msg("修改成功！", {icon: 1});
                            // layer.closeAll('page');
                            table.reload('trainingTable', {where: {trainingName: trainingName, projectType: projectType, serviceType: serviceType}});
                            $("#testListAction").trigger("click");
                        }else{
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
            } else {
                $.ajax({
                    type: 'POST',
                    url: '/trainingprogram/create',//发送请求
                    contentType: "application/json; charset=utf-8",
                    async: true,
                    data: JSON.stringify(param),
                    dataType: "json",
                    success: function (res) {
                        layer.closeAll("loading");
                        if(res.code == 200){
                            layer.msg("添加成功！", {icon: 1});
                            // layer.closeAll('page');
                            table.reload('trainingTable', {where: {trainingName: trainingName, projectType: projectType, serviceType: serviceType}});
                            programIdVal = res.data;
                            isCanChangeTag = true;
                            $("#testListAction").trigger("click");
                            changeInternalShow();
                        }else{
                            layer.msg(res.msg, {icon: 2});
                        }
                    }
                });
            }
            return false;
        });
    }

    // 内部流程处理
    function internalProgramHandle() {
        // programIdVal
        internalProIdTemp = '';
        $.get("/internalprogram/getInternal", {id: programIdVal}, function (res) {
            if(res.code == 200) {
                if(res.data.length > 0){
                    internalProIdTemp = res.data[0].id;
                    // 内部流程业务处理--“供应商状态”Table
                    showEditIntrenalModel(internalProIdTemp);

                    form.val('consultinInternalgForm', res.data[0]);
                    internalURL = '/internalprogram/update';
                    // 编辑状态获取 业务流程 已经上传的文件
                    internalEditFiles(internalProIdTemp);

                    // 如果是查看功能，则需要隐藏部分的按钮
                    if(isHideCol) {
                        $("#testInternalList").css('display', 'none');
                        $("#internalCancelBtn").css('display', 'none');
                        $("#internalFormSubmit").css('display', 'none');
                        $('#internalUpdateList').find('.data-delete').addClass('layui-hide');
                        $("#addSupplierButton").css('display', 'none');
                        $("#closeDialog1").css('display', 'none');
                    }
                }else{
                    showEditIntrenalModel();
                    if(isHideCol) {
                        $("#testInternalList").css('display', 'none');
                        $("#internalCancelBtn").css('display', 'none');
                        $("#internalFormSubmit").css('display', 'none');
                        $('#internalUpdateList').find('.data-delete').addClass('layui-hide');
                        $("#addSupplierButton").css('display', 'none');
                        $("#closeDialog1").css('display', 'none');
                    }
                    internalURL = '/internalprogram/create';
                }
                loadInsteralFileUpload();
            }
        });
    }

    // 对象数组排序
    function compareDate(DateOne, DateTwo) {
        var OneMonth = DateOne.substring(5, DateOne.lastIndexOf("-"));
        var OneDay = DateOne.substring(DateOne.length, DateOne.lastIndexOf("-") + 1);
        var OneYear = DateOne.substring(0, DateOne.indexOf("-"));
        var TwoMonth = DateTwo.substring(5, DateTwo.lastIndexOf("-"));
        var TwoDay = DateTwo.substring(DateTwo.length, DateTwo.lastIndexOf("-") + 1);
        var TwoYear = DateTwo.substring(0, DateTwo.indexOf("-"));
        if (Date.parse(OneMonth + "/" + OneDay + "/" + OneYear) > Date.parse(TwoMonth + "/" + TwoDay + "/" + TwoYear)) {
            return true;
        } else {
            return false;
        }
    }

    // 获取供应商和标签的数据，并初始化对应的选择控件
    // 获取所有角色
    layer.load(2);
    $.get("/trainingprogram/getinitdata", {}, function (res) {
        layer.closeAll('loading');
        if(res.code == 200) {
            tagsData = res.data.tags;
            customsData = res.data.customs;
        } else {
            layer.msg('获取初始化数据失败');
        }
    });

    function initSelect(ctlId, data, tip) {
        var $sel = $("#"+ ctlId);
        $sel.empty();
        $sel.append('<option value="'+""+'">'+"请选择" + tip +'</option>');


        for(var i=0; i<data.length; i++){
            $sel.append('<option selected value="'+ data[i].id +'">'+ data[i].name +'</option>');
        }
        $sel.val('');
    }

// ==========================================客户栏处理 start===============================================
    // 绑定事件
    form.on('select(customId)', function(data){
        console.log(data);
        var text = data.elem[data.elem.selectedIndex].text;
        $.get("/linkman/getLinkMans", {customId: data.value}, function (res) {
            if(res.code == 200) {
                showCustomTab(res.data, text)
            }
        });
    });

    function showCustomTab(data, text) {
        $("#customList").html("");
        if(data.length > 0) {
            for(var i=0; i<data.length; i++) {
                var tr = $(['<tr id="'+ data[i].id +'">'
                    ,'<td>'+ text +'</td>'
                    ,'<td>'+ (data[i].name == undefined ? '':data[i].name) +'</td>'
                    , '<td>'+ (data[i].phone == undefined ? '':data[i].phone) +'</td>'
                    , '<td>'+ (data[i].email == undefined ? '':data[i].email) +'</td>'
                    , '<td>'+ (data[i].contactAddress == undefined ? '':data[i].contactAddress) +'</td>'
                    ,'</tr>'].join(''));
                $("#customList").append(tr);
            }
        }

    }
// ==========================================客户栏处理 end===============================================

// ==========================================筹备阶段文件处理 start===============================================
    function showModuleTab() {
        $.ajax({
            url: '/file/fileList?type=3&correlatorId=' + "",
            type:"get"
            ,async:false
            ,dataType:"json"
            , success: function(res){
                if(res.code == 200) {
                    var data = res.data;
                    // 显示请求的服务端数据
                    if(data.length > 0) {
                        for(var i=0; i<data.length; i++) {
                            (function (d) {
                                var tr = $(['<tr id="'+ d.fileId +'" name="' + d.fileUrl + '">'
                                    ,'<td>'+ d.fileName +'</td>'
                                    ,'<td>'
                                    ,'<button class="layui-btn layui-btn-xs data-download1">下载</button>'
                                    ,'</td>'
                                    ,'</tr>'].join(''));

                                // 下载
                                tr.find('.data-download1').on('click', function(){
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    var url = '/file/download';
                                    var option = {
                                        data: {fileId: fileId,
                                            fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)), fileName: fileName}
                                    }
                                    $.get("/file/isExistFile", {fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName))}, function (res) {
                                        if(res.code == 200) {
                                            download(url, option);
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    });
                                    return false;
                                });
                                $('#moduleList').append(tr);
                            })(data[i]);
                        }
                    }

                }

            }
        });

    }
// ==========================================筹备阶段文件处理 end===============================================

// ==========================================项目成员处理 start===============================================
    // 交集
    function intersection (arr1, arr2, key) {
        var tmpArr = [];
        for(var i=0;i<arr1.length;i++){
            for(var j=0;j<arr2.length;j++){
                var arr = arr2[j];
                if(arr1[i].id == eval('arr.' + key)){
                    tmpArr.push(arr1[i]);
                }
            }
        }
        return tmpArr;
    }
    // 差集
    function difference(arr1, arr2, key) {
        var tmpArr = [];
        for(var i=0;i<arr1.length;i++){
            var flag = true;
            for(var j=0;j<arr2.length;j++){
                var arr = arr2[j];
                if(arr1[i].id == eval('arr.' + key)){
                    flag = false;
                }
            }
            if(flag){
                tmpArr.push(arr1[i]);
            }
        }
        return tmpArr;
    }


    function showMemberTab(programId) {
        memberTabData = [];
        removeUserData = [];
        if(programId) {
            // 获取项目的数据
            $.get("/user/getMembers", {programId: programId}, function (res) {
                if(res.code == 200) {
                    memberTabData = res.data;
                    table.reload('memberTable',{data : memberTabData});
                    // 处理 userData，removeUserData
                    removeUserData = intersection(userData, memberTabData, 'userId');
                    userData = difference(userData, memberTabData, 'userId');
                }
            });
        }

        //渲染表格
        table.render({
            elem: '#memberTable',
            data: memberTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                , statusCode: 200 //规定成功的状态码，默认：0
                , msgName: 'msg' //规定状态信息的字段名称，默认：msg
                , countName: 'count' //规定数据总数的字段名称，默认：count
                , dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {field: 'name', title: '姓名'},
                {
                    templet: function (d) {
                        return d.role == "1"? "项目经理":"普通成员";
                    }, title: '角色'
                },
                {field: 'ratio', title: '占比'},
                {align: 'center', toolbar: '#tableMemberBar', title: '操作', minWidth: 200}
            ]],
            done: function () {
                if(isHideCol) {
                    $('#memberTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });

        // 添加按钮点击事件
        $('#addMemberButton').click(function () {
            showMemberForm();
        });

        // 工具条点击事件
        table.on('tool(memberTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doMemberDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showMemberForm(data);
            }
        });
    }

    // 项目成员表删除
    function doMemberDelete(obj) {
        var id = obj.data.userId;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理memberTabData, userData, removeUserData;
            var tmpMemberData = [];
            for(var i=0; i<memberTabData.length; i++) {
                if(id != memberTabData[i].userId) {
                    tmpMemberData.push(memberTabData[i]);
                }
            }
            memberTabData = tmpMemberData;
            // table.reload('memberTable',{data : memberTabData});

            var tempRmData = [];
            for(var i=0; i<removeUserData.length; i++) {
                if(id != removeUserData[i].id) {
                    tempRmData.push(removeUserData[i]);
                } else {
                    userData.push(removeUserData[i]);
                }
            }
            removeUserData = tempRmData;

            layer.msg("删除成功");
            obj.del();
        });
    }

    // 项目成员弹框
    function showMemberForm(data) {
        var index = layer.open({
            type: 1,
            area: ['360px','320px'],
            offset: '65px',
            title: data ? '修改项目成员' : '添加项目成员',
            content: $('#memberForm').html(),
            success: function () {
                // 渲染项目成员
                if(data) {
                    var tmp = [{id: data.userId, name: data.name}].concat(userData);
                    arraySort(tmp);
                    initSelect("name", tmp, "");
                } else {
                    arraySort(userData);
                    initSelect("name", userData, "");
                }

                if(data) {
                    form.val('memberForm', data);
                    $("#name").val(data.userId);
                }
                form.render();
                // 表单提交事件
                form.on('submit(memberFormSubmit)', function (d) {
                    layer.load(2);
                    if(data) {
                        layer.closeAll("loading");
                        // 实现思路，将form的数据认为是新的数据，直接进行添加，对表格的数据，重新更新memberTabData
                        var tmpMemberData = [];
                        var rowData_ = {};
                        rowData_.userId = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.role = d.field.role;
                        rowData_.ratio = d.field.ratio;

                        for(var i=0; i<memberTabData.length; i++) {
                            if(data.userId == memberTabData[i].userId) {
                                tmpMemberData.push(rowData_);
                            } else {
                                tmpMemberData.push(memberTabData[i]);
                            }
                        }
                        memberTabData = tmpMemberData;
                        table.reload('memberTable',{data : memberTabData});

                        var tempData = [];
                        for(var i=0; i<userData.length; i++) {
                            if(rowData_.userId != userData[i].id) {
                                tempData.push(userData[i]);
                            } else {
                                removeUserData.push(userData[i]);
                            }
                        }
                        userData = tempData;

                        var tempRmData = [];
                        for(var i=0; i<removeUserData.length; i++) {
                            if(data.userId != removeUserData[i].id) {
                                tempRmData.push(removeUserData[i]);
                            } else {
                                userData.push(removeUserData[i]);
                            }
                        }
                        removeUserData = tempRmData;

                        layer.msg("修改成功！", {icon: 1});
                        layer.close(index);
                    } else {
                        layer.closeAll("loading");
                        // 处理memberTabData, userData, removeUserData;
                        var rowData_ = {};
                        rowData_.userId = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.role = d.field.role;
                        rowData_.ratio = d.field.ratio;
                        memberTabData.push(rowData_);
                        table.reload('memberTable',{data : memberTabData});

                        var tempData = [];
                        for(var i=0; i<userData.length; i++) {
                            if(rowData_.userId != userData[i].id) {
                                tempData.push(userData[i]);
                            } else {
                                removeUserData.push(userData[i]);
                            }
                        }
                        userData = tempData;

                        layer.msg("添加成功！", {icon: 1});
                        layer.close(index);
                    }
                    return false;
                });

            }
        });
    }

    function getUserData() {
        // 获取项目的数据
        $.get("/user/getUserData", {}, function (res) {
            if(res.code == 200) {
                userData = res.data;
            }
        });
    }

    function initSelect(ctlId, data, tip) {
        var $sel = $("#"+ ctlId);
        $sel.empty();
        $sel.append('<option value="'+""+'">'+"请选择" + tip +'</option>');


        for(var i=0; i<data.length; i++){
            $sel.append('<option selected value="'+ data[i].id +'">'+ data[i].name +'</option>');
        }
        $sel.val('');
    }


    function arraySort(arr) {
        arr.sort(function(a,b){
            if(a.id>b.id) return 1 ;
            if(a.id<b.id) return -1 ;
            return 0 ;
        });
    }
// ==========================================项目成员处理 end===============================================

// ==========================================方案处理 start===============================================
    function showSchemeTab(programId) {
        schemeTabData = [];
        removeSchemeData = [];
        finalSchemeData = [];
        if(programId) {
            // 获取方案的数据
            $.get("/scheme/getSchemes", {programId: programId}, function (res) {
                if(res.code == 200) {
                    schemeTabData = res.data;
                    table.reload('schemeTable',{data : schemeTabData});

                    // 处理 schemeData，removeSchemeData
                    removeSchemeData = intersection(schemeData, schemeTabData,'schemeId');
                    schemeData = difference(schemeData, schemeTabData, 'schemeId');

                    // 初始化最终方案的下拉框和表格
                    initFinalSchemeSel();
                    for(var i=0; i<schemeTabData.length; i++) {
                        if (schemeTabData[i].finalScheme == 1) {
                            finalSchemeData.push(schemeTabData[i]);
                        }
                    }
                    table.reload('schemeFinalTable',{data : finalSchemeData});
                }
            });
        } else {
            initFinalSchemeSel();
        }

        //渲染表格
        table.render({
            elem: '#schemeTable',
            data: schemeTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                , statusCode: 200 //规定成功的状态码，默认：0
                , msgName: 'msg' //规定状态信息的字段名称，默认：msg
                , countName: 'count' //规定数据总数的字段名称，默认：count
                , dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {
                    templet: function (d) {
                        return d.schemeVo.name;
                    }, title: '名称'
                },
                {
                    templet: function (d) {
                        return d.schemeVo.supplierName == undefined ? '' : d.schemeVo.supplierName;
                    }, title: '供应商'
                },
                {
                    templet: function (d) {
                        var str = '';
                        for (var i = 0; i < d.schemeVo.teachers.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.schemeVo.teachers[i].name + '</span>');
                        }
                        return str;
                    }, title: '讲师'
                },
                {
                    templet: function (d) {
                        return d.schemeVo.period == undefined ? '' : d.schemeVo.period;
                    }, title: '课时'
                },
                {
                    templet: function (d) {
                        return d.schemeVo.offer == undefined ? '' : d.schemeVo.offer;
                    }, title: '课程报价'
                },
                {
                    templet: function (d) {
                        var str = '';
                        for (var i = 0; i < d.schemeVo.tags.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.schemeVo.tags[i].name + '</span>');
                        }
                        return str;
                    }, title: '方案类型'
                },
                {field: 'taxPoint', title: '税点'},
                {
                    templet: function (d) {
                        return methodObj[d.procurementMethod];
                    }, title: '采购方式'
                },
                {align: 'center', toolbar: '#tableSchemeBar', title: '操作', minWidth: 200}
            ]],
            done: function () {
                if(isHideCol) {
                    $('#schemeTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });

        // 添加按钮点击事件
        $('#addSchemeButton').click(function () {
            showSchemeForm();
        });

        // 工具条点击事件
        table.on('tool(schemeTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doSchemeDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showSchemeForm(data);
            }
        });

        //渲染表格
        table.render({
            elem: '#schemeFinalTable',
            data: [],
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                , statusCode: 200 //规定成功的状态码，默认：0
                , msgName: 'msg' //规定状态信息的字段名称，默认：msg
                , countName: 'count' //规定数据总数的字段名称，默认：count
                , dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {
                    templet: function (d) {
                        return d.schemeVo.name;
                    }, title: '名称'
                },
                {
                    templet: function (d) {
                        return d.schemeVo.supplierName == undefined ? '': d.schemeVo.supplierName;
                    }, title: '供应商'
                },
                {
                    templet: function (d) {
                        var str = '';
                        for (var i = 0; i < d.schemeVo.teachers.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.schemeVo.teachers[i].name + '</span>');
                        }
                        return str;
                    }, title: '讲师'
                },
                {
                    templet: function (d) {
                        return d.schemeVo.period == undefined ? '': d.schemeVo.period;
                    }, title: '课时'
                },
                {
                    templet: function (d) {
                        return d.schemeVo.offer == undefined ? '': d.schemeVo.offer;
                    }, title: '课程报价'
                },
                {
                    templet: function (d) {
                        var str = '';
                        for (var i = 0; i < d.schemeVo.tags.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.schemeVo.tags[i].name + '</span>');
                        }
                        return str;
                    }, title: '方案类型'
                },
                {field: 'taxPoint', title: '税点'},
                {
                    templet: function (d) {
                        return methodObj[d.procurementMethod];
                    }, title: '采购方式'
                },
            ]],
            done: function () {
                layer.closeAll('loading');
            }
        });

        formSelects.on('finanlScheme', function(id, vals, val, isAdd, isDisabled){
            for(var i=0; i<schemeTabData.length; i++) {
                if(val.value == schemeTabData[i].schemeId) {
                    if(isAdd) {
                        schemeTabData[i].finalScheme = 1;
                        finalSchemeData.push(schemeTabData[i]);
                    } else {
                        schemeTabData[i].finalScheme = 0;
                        var tempData = [];
                        for(var j=0; j<finalSchemeData.length; j++) {
                            if(val.value != finalSchemeData[j].schemeId) {
                                tempData.push(finalSchemeData[j]);
                            }
                        }
                        finalSchemeData =  tempData;
                    }
                }
            }
            table.reload('schemeFinalTable',{data : finalSchemeData});
            table.reload('schemeTable',{data : schemeTabData});
        });
    }

    function doSchemeDelete(obj) {
        var id = obj.data.schemeId;
        for(var i=0; i<schemeTabData.length; i++) {
            if(id ==schemeTabData[i].schemeId && schemeTabData[i].finalScheme == 1) {
                layer.msg("该方案已选为最终方案，不能删除！", {icon: 2});
                return false;
            }
        }

        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理schemeTabData, schemeData, removeSchemeData;
            var tmpSchemeData = [];
            for(var i=0; i<schemeTabData.length; i++) {
                if(id != schemeTabData[i].schemeId) {
                    tmpSchemeData.push(schemeTabData[i]);
                }
            }
            schemeTabData = tmpSchemeData;

            var tempRmData = [];
            for(var i=0; i<removeSchemeData.length; i++) {
                if(id != removeSchemeData[i].id) {
                    tempRmData.push(removeSchemeData[i]);
                } else {
                    schemeData.push(removeSchemeData[i]);
                }
            }
            removeSchemeData = tempRmData;

            layer.msg("删除成功");
            obj.del();

            initFinalSchemeSel();
        });
    }

    function showSchemeForm(data) {
        var index = layer.open({
            type: 1,
            area: ['360px','320px'],
            offset: '65px',
            title: data ? '修改方案' : '添加方案',
            content: $('#schemeForm').html(),
            success: function () {

                // 渲染方案
                if(data) {
                    var tmp = [{id: data.schemeId, name: data.schemeVo.name}].concat(schemeData);
                    arraySort(tmp);
                    initSelect("name", tmp, "");

                    form.val('schemeForm', data);
                    $("#name").val(data.schemeId);

                    // 控制方案名称是否可以编辑
                    if(data.finalScheme == 1) {
                        $("#name").attr('disabled', true);
                    }
                } else {
                    arraySort(schemeData);
                    initSelect("name", schemeData, "");
                }
                form.render();

                // 表单提交事件
                form.on('submit(schemeFormSubmit)', function (d) {
                    layer.load(2);
                    if(data) {
                        var rowData_ = {};
                        var schemeVo = {};
                        rowData_.schemeId = d.field.name;
                        schemeVo.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.procurementMethod = d.field.procurementMethod;
                        rowData_.taxPoint = d.field.taxPoint;
                        rowData_.finalScheme = data.finalScheme;
                        $.get("/scheme/getSchemeVo", {id: rowData_.schemeId}, function (res) {
                            layer.closeAll("loading");
                            if(res.code == 200) {
                                schemeVo.supplierName = res.data.supplierName;
                                schemeVo.teachers = res.data.teachers;
                                schemeVo.period = res.data.period;
                                schemeVo.offer = res.data.offer;
                                schemeVo.tags = res.data.tags;
                                rowData_.schemeVo =  schemeVo;

                                var tmpSchemeData = [];
                                for(var i=0; i<schemeTabData.length; i++) {
                                    if(data.schemeId == schemeTabData[i].schemeId) {
                                        tmpSchemeData.push(rowData_);
                                    } else {
                                        tmpSchemeData.push(schemeTabData[i]);
                                    }
                                }
                                schemeTabData = tmpSchemeData;
                                table.reload('schemeTable',{data : schemeTabData});

                                layer.msg("修改成功！", {icon: 1});
                                layer.close(index);

                                initFinalSchemeSel();
                                finalSchemeData = [];
                                for(var i=0; i<schemeTabData.length; i++) {
                                    if (schemeTabData[i].finalScheme == 1) {
                                        finalSchemeData.push(schemeTabData[i]);
                                    }
                                }
                                table.reload('schemeFinalTable',{data : finalSchemeData});
                            }
                        });
                    } else {
                        // 处理schemeTabData, schemeData, removeSchemeData;
                        var rowData_ = {};
                        var schemeVo = {};
                        rowData_.schemeId = d.field.name;
                        schemeVo.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.procurementMethod = d.field.procurementMethod;
                        rowData_.taxPoint = d.field.taxPoint;
                        rowData_.finalScheme = 0;
                        // 获取项目的数据
                        $.get("/scheme/getSchemeVo", {id: rowData_.schemeId}, function (res) {
                            layer.closeAll("loading");
                            if(res.code == 200) {
                                schemeVo.supplierName = res.data.supplierName;
                                schemeVo.teachers = res.data.teachers;
                                schemeVo.period = res.data.period;
                                schemeVo.offer = res.data.offer;
                                schemeVo.tags = res.data.tags;
                                rowData_.schemeVo = schemeVo;
                                schemeTabData.push(rowData_);
                                table.reload('schemeTable',{data : schemeTabData});

                                var tempData = [];
                                for(var i=0; i<schemeData.length; i++) {
                                    if(rowData_.schemeId != schemeData[i].id) {
                                        tempData.push(schemeData[i]);
                                    } else {
                                        removeSchemeData.push(schemeData[i]);
                                    }
                                }
                                schemeData = tempData;

                                layer.msg("添加成功！", {icon: 1});
                                layer.close(index);

                                initFinalSchemeSel();
                            }
                        });
                    }
                    return false;
                });

            }
        });
    }

    // 渲染最终方案下拉选项
    function initFinalSchemeSel() {
        var schemeSelData = new Array();
        for (var i = 0; i < schemeTabData.length; i++) {
            schemeSelData.push({name: schemeTabData[i].schemeVo.name, value: schemeTabData[i].schemeId});
        }
        formSelects.data('finanlScheme', 'local', {arr: schemeSelData});

        // 回写最终方案的下拉选中项
        var rds = new Array();
        for (var i = 0; i < schemeTabData.length; i++) {
            if(schemeTabData[i].finalScheme == 1) {
                rds.push(schemeTabData[i].schemeId);
            }
        }
        formSelects.value('finanlScheme', rds);  // 回显多选框
    }

    function getSchemesData() {
        // 获取项目的数据
        $.get("/scheme/getSchemesData", {}, function (res) {
            if(res.code == 200) {
                schemeData = res.data;
            }
        });
    }
// ==========================================方案处理 end===============================================

// ==========================================项目排期处理 start===============================================
    function showCourseTab(programId) {
        courseTabData = [];
        if(programId) {
            // 获取项目的数据
            $.get("/course/getCourses", {programId: programId}, function (res) {
                if(res.code == 200) {
                    courseTabData = res.data;
                    table.reload('courseTable',{data : courseTabData});
                }
            });
        }

        //渲染表格
        table.render({
            elem: '#courseTable',
            data: courseTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                , statusCode: 200 //规定成功的状态码，默认：0
                , msgName: 'msg' //规定状态信息的字段名称，默认：msg
                , countName: 'count' //规定数据总数的字段名称，默认：count
                , dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {field: 'name', title: '名称'},
                {field: 'lectureDate', title: '日期'},
                {field: 'lectureTime', title: '时间'},
                {field: 'lectureAddress', title: '地点'},
                {
                    templet: function (d) {
                        return d.state == undefined || d.state == "" ? "" : stateObj[d.state];
                    }, title: '状态'
                },,
                {field: 'grade', title: '评分'},
                {field: 'comment', title: '备注'},
                {align: 'center', toolbar: '#tableCourseBar', title: '操作', minWidth: 200}
            ]],
            done: function () {
                if(isHideCol) {
                    $('#courseTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });

        // 添加按钮点击事件
        $('#addCourseButton').click(function () {
            showCourseForm();
        });

        // 工具条点击事件
        table.on('tool(courseTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doCourseDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showCourseForm(data);
            }
        });
    }

    function showCourseForm(data) {
        var index = layer.open({
            type: 1,
            area: ['360px', '400px'],
            offset: '20px',
            title: data ? '修改项目排期' : '添加项目排期',
            content: $('#courseForm').html(),
            success: function () {
                if(data) {
                    form.val('courseForm', data);
                }

                // 初始化时间控件
                laydate.render({
                    elem: '#lectureDate' //指定元素
                });
                laydate.render({
                    elem: '#lectureTime' //指定元素
                    ,type: 'time',
                    format: 'HH:mm'
                });

                form.render();
                // 表单提交事件
                form.on('submit(courseFormSubmit)', function (d) {
                    layer.load(2);

                    // 处理courseTabData;
                    var rowData_ = {};
                    rowData_.id = d.field.id;
                    rowData_.name= d.field.name;
                    rowData_.state = d.field.state;
                    rowData_.lectureDate = d.field.lectureDate;
                    rowData_.lectureTime = d.field.lectureTime;
                    rowData_.lectureAddress = d.field.lectureAddress;
                    rowData_.grade = d.field.grade;
                    rowData_.comment = d.field.comment;
                    if(rowData_.id == undefined || rowData_.id == "") {
                        rowData_.id = uuid();
                        courseTabData.push(rowData_);
                    } else {
                        var tmpTableData = courseTabData;
                        var oldData = new Array();
                        for(var i=0; i<tmpTableData.length; i++) {
                            if(tmpTableData[i].id == rowData_.id) {
                                oldData.push(rowData_);
                            } else {
                                oldData.push(tmpTableData[i]);
                            }
                        }
                        courseTabData = oldData;
                    }
                    table.reload('courseTable',{data: courseTabData});

                    layer.closeAll("loading");
                    if(data) {
                        layer.msg("修改成功！", {icon: 1});
                    } else {
                        layer.msg("添加成功！", {icon: 1});
                    }

                    layer.close(index);
                    return false;
                });

            }
        });
    }

    function doCourseDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理courseTabData;
            var tmpCourseData = [];
            for(var i=0; i<courseTabData.length; i++) {
                if(id != courseTabData[i].id) {
                    tmpCourseData.push(courseTabData[i]);
                }
            }
            courseTabData = tmpCourseData;

            layer.msg("删除成功");
            obj.del();
        });
    }

    function uuid() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
// ==========================================附件上传 start===============================================
    function loadFileUpload() {
        //多文件列表示例
        var demoListView = $('#demoList')
            ,uploadListIns = upload.render({
            elem: '#testList'
            ,url: '/file/upload/'
            ,accept: 'file'
            ,size: 10240
            ,multiple: true
            ,number: 5
            ,auto: false
            ,bindAction: '#testListAction'
            ,before: function() {
                this.data = {"id": programIdVal, "type": 4}
            }
            ,choose: function(obj){
                var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                //读取本地文件
                obj.preview(function(index, file, result){
                    var tr = $(['<tr id="upload-'+ index +'">'
                        ,'<td>'+ file.name +'</td>'
                        , '<td>等待上传</td>'
                        ,'<td>'
                        ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
                        ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
                        ,'</td>'
                        ,'</tr>'].join(''));

                    //单个重传
                    tr.find('.demo-reload').on('click', function(){
                        obj.upload(index, file);
                        return false;
                    });

                    //删除
                    tr.find('.demo-delete').on('click', function(){
                        layer.confirm('确定要删除吗？', {
                            offset: '65px',
                            title: '提示'
                        }, function (i) {
                            layer.close(i);
                            delete files[index]; //删除对应的文件
                            tr.remove();
                            uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                        });
                    });

                    demoListView.append(tr);
                });
            }
            ,done: function(res, index, upload){
                if(res.code == 200){ //上传成功
                    var fileId = res.data; //附件id
                    fileIds.push(JSON.parse(fileId).id);
                    console.log(fileIds);
                    var tr = demoListView.find('tr#upload-'+ index)
                        ,tds = tr.children();
                    tds.eq(1).html('<span style="color: #5FB878;">上传成功</span>');
                    tds.eq(2).html(''); //清空操作
                    return delete this.files[index]; //删除文件队列已经上传成功的文件
                }
                this.error(index, upload);
            }
            ,error: function(index, upload){
                var tr = demoListView.find('tr#upload-'+ index)
                    ,tds = tr.children();
                tds.eq(2).find('.demo-reload').removeClass('layui-hide'); //显示重传
            }
        });
    }

    function initFileTab() {
        $.ajax({
            url: '/file/fileList?type=4&correlatorId=' + programIdVal,
            type:"get"
            ,async:false
            ,dataType:"json"
            , success: function(res){
                if(res.code == 200) {
                    var ds = res.data;
                    if(ds.length > 0) {
                        for (var i = 0; i < ds.length; i++) {
                            (function (d) {
                                var tr = $(['<tr id="' + d.fileId + '" name="' + d.fileUrl + '">'
                                    , '<td>' + d.fileName + '</td>'
                                    , '<td>已上传</td>'
                                    , '<td>'
                                    , '<button class="layui-btn layui-btn-xs layui-btn-danger data-delete">删除</button>'
                                    , '<button class="layui-btn layui-btn-xs data-download">下载</button>'
                                    , '</td>'
                                    , '</tr>'].join(''));
                                //删除
                                tr.find('.data-delete').on('click', function (param) {
                                    layer.confirm('确定要删除吗？', {
                                        offset: '65px',
                                        title: '提示'
                                    }, function (i) {
                                        layer.close(i);
                                        var fileId = tr.attr("id");
                                        var fileUrl = tr.attr("name");
                                        var tds = tr.children();
                                        var fileName = tds.eq(0).html();
                                        // 删除文件库中的文件
                                        $.post("/file/deleleFile", {
                                            fileId: fileId,
                                            fileUrl: fileUrl.substring(0, fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)),
                                            type: 4
                                        }, function (res) {
                                            if (res.code == 200) {
                                                layer.msg("删除成功！");
                                                tr.remove();
                                            } else {
                                                layer.msg(res.msg);
                                            }
                                        });
                                    });
                                    return false;
                                });

                                // 下载
                                tr.find('.data-download').on('click', function () {
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    var url = '/file/download';
                                    var option = {
                                        data: {fileId: fileId,
                                            fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)), fileName: fileName}
                                    }
                                    $.get("/file/isExistFile", {fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName))}, function (res) {
                                        if(res.code == 200) {
                                            download(url, option);
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    });
                                    return false;
                                });
                                $('#demoList').append(tr);
                            })(ds[i]);
                        }
                    }
                }

            }
        });
    }
// ==========================================附件上传 end===============================================

// ==========================================项目排期处理 end===============================================

// ==========================================培训项目弹框 end===============================================

// ==============================================内部流程功能 start==========================================
    // 处理“内部流程样式”,当当前的操作界面有项目ID的时候，则可以切换
    function changeInternalShow(){
        var tabId = document.getElementById("showP");
        if(isCanChangeTag){
            tabId.style.color = "";
            tabId.style.cursor = "";
            tabId.style.pointerEvents = "";
        }else{
            tabId.style.color = "slategrey";
            tabId.style.cursor = "not-allowed";
            tabId.style.pointerEvents = "none";
        }
    }

    // 内部流程业务处理--“供应商状态”Table
    function showEditIntrenalModel(internalId){
        supplierTabData = [];
        removeSupplierData = [];
        if(internalId) {
            // 获取项目的数据
            $.get("/supplierinternalprogram/getCustomState", {internalId: internalId}, function (res) {
                if(res.code == 200) {
                    supplierTabData = res.data;
                    table.reload('supplierShowTable',{data : supplierTabData});

                    // 处理 supplierData，removeSupplierData
                    removeSupplierData = intersection(supplierData, supplierTabData, 'id');
                    supplierData = difference(supplierData, supplierTabData, 'id');
                }
            });
        }

        //渲染表格
        table.render({
            elem: '#supplierShowTable',
            data: supplierTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                ,statusCode: 200 //规定成功的状态码，默认：0
                ,msgName: 'msg' //规定状态信息的字段名称，默认：msg
                ,countName: 'count' //规定数据总数的字段名称，默认：count
                ,dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {field: 'name', title: '名称'},
                {
                    templet: function (res) {
                        if(res.state == 0){
                            return '启动';
                        }else if(res.state == 1){
                            return '招采';
                        }else if(res.state == 2){
                            return '合同签订';
                        }else if(res.state == 3){
                            return '付款';
                        }else if(res.state == 4){
                            return '归档';
                        }else{
                            return '';
                        }

                    }, title: '状态'
                },
                {align: 'center', toolbar: '#supplierSateTableBar', title: '操作', minWidth: 200}
            ]],
            done: function () {
                if(isHideCol) {
                    $('#supplierShowTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });



        // “供应商状态”工具条点击事件
        table.on('tool(supplierShowTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doSupplierDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showEditSupplierSateModel(data);
            }
        });
    }

    // “内部流程”主表单提交处理
    form.on('submit(internalFormSubmit)', function (d){
        var data_row = d.field;
        var supplierInternalProgramList = [];

        // 处理数据
        var costSupplierTemp = data_row.costSupplier.replace(/,/g,'');
        var costFareTemp = data_row.costFare.replace(/,/g,'');
        var costMealTemp = data_row.costMeal.replace(/,/g,'');
        var costIncidentalTemp = data_row.costIncidental.replace(/,/g,'');
        var costHousingTemp = data_row.costHousing.replace(/,/g,'');
        var costTeaTemp = data_row.costTea.replace(/,/g,'');
        var costPrintTemp = data_row.costPrint.replace(/,/g,'');
        var costStoresTemp = data_row.costStores.replace(/,/g,'');
        var costTeacherTravelTemp = data_row.costTeacherTravel.replace(/,/g,'');
        var costTravelTemp = data_row.costTravel.replace(/,/g,'');
        var costOtherTemp = data_row.costOther.replace(/,/g,'');
        var contractAmountTemp = data_row.contractAmount.replace(/,/g,'');
        var taxRevenueTemp = data_row.taxRevenue.replace(/,/g,'');
        var aftertaxCostTemp = data_row.aftertaxCost.replace(/,/g,'');


        var totalTemp = Number(costSupplierTemp) +
            Number(costFareTemp) +
            Number(costMealTemp) +
            Number(costIncidentalTemp) +
            Number(costHousingTemp) +
            Number(costTeaTemp) +
            Number(costPrintTemp) +
            Number(costStoresTemp) +
            Number(costTeacherTravelTemp) +
            Number(costTravelTemp) +
            Number(costOtherTemp);
        var totalTempStr = String(totalTemp);

        // 处理“项目排期”数据
        for(var i=0; i<supplierTabData.length;i++){
            var internalIdTemp = '';

            if(supplierTabData[i].internalId){
                internalIdTemp = supplierTabData[i].internalId;
            }
            var supplierJson = {
                supplierId: supplierTabData[i].id,
                internalId: internalIdTemp,
                state: supplierTabData[i].state
            }
            supplierInternalProgramList.push(supplierJson);
        }

        var param = {
            id:internalProIdTemp,
            // 供应商
            costSupplier: costSupplierTemp,
            // 交通费
            costFare: costFareTemp,
            // 餐费
            costMeal: costMealTemp,
            // 杂费
            costIncidental: costIncidentalTemp,
            // 住宿费
            costHousing: costHousingTemp,
            // 茶歇费
            costTea: costTeaTemp,
            // 打印费
            costPrint: costPrintTemp,
            // 物料费
            costStores: costStoresTemp,
            // 讲师差旅费
            costTeacherTravel: costTeacherTravelTemp,
            // 差旅费
            costTravel: costTravelTemp,
            // 其他
            costOther: costOtherTemp,
            // 客户状态
            customState: data_row.customState,
            //合同总金额含税
            contractAmount: contractAmountTemp,
            // 项目税后总营收
            taxRevenue: taxRevenueTemp,
            // 总成本
            totalCost: totalTempStr,
            //税后成本
            aftertaxCost: aftertaxCostTemp,
            // 项目状态
            state: data_row.state,
            // 项目Id
            programId: programIdVal,
            // 内部关联
            supplierInternalProgramList: supplierInternalProgramList
        }
        if(param.costSupplier == '' && param.costFare == '' && param.costMeal == '' && param.costIncidental == '' && param.costHousing == '' && param.costTea == '' &&
            param.costPrint == '' &&  param.costStores == '' && param.costTeacherTravel == '' && param.costTravel == '' && param.costOther == '' && param.customState == '' &&
            param.contractAmount == '' && param.taxRevenue == '' && param.aftertaxCost == '' &&  param.supplierInternalProgramList.length == 0){

            layer.msg("没有填写项目，无法保存！", {icon: 1});
            return false;

        }
        $.ajax({
            type: 'POST',
            url: internalURL,//发送请求
            contentType: "application/json; charset=utf-8",
            async: true,
            data: JSON.stringify(param),
            dataType: "json",
            success: function (res) {
                layer.closeAll("loading");
                if(res.code == 200){
                    layer.msg("添加成功！", {icon: 1});
                    layer.closeAll('page');
                    //  table.reload('trainingTable', {where: {trainingName: trainingName, projectType: projectType, serviceType: serviceType}});
                    if(!internalProIdTemp) {
                        internalProIdTemp = res.data;
                    }
                    $("#testInternalListAction").trigger("click");
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
        return false;
    });

    // “供应商状态”删除
    function doSupplierDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理memberTabData, userData, removeUserData;
            var tmpMemberData = [];
            for(var i=0; i<supplierTabData.length; i++) {
                if(id != supplierTabData[i].id) {
                    tmpMemberData.push(supplierTabData[i]);
                }
            }
            supplierTabData = tmpMemberData;
            // table.reload('memberTable',{data : memberTabData});

            var tempRmData = [];
            for(var i=0; i<removeSupplierData.length; i++) {
                if(id != removeSupplierData[i].id) {
                    tempRmData.push(removeSupplierData[i]);
                } else {
                    supplierData.push(removeSupplierData[i]);
                }
            }
            removeSupplierData = tempRmData;

            layer.msg("删除成功");
            obj.del();
        });
    }

    // 获取 内部流程 “供应商”数据
    function getSupplierData(){
        // 获取供应商数据
        $.get("/base/supplier/getSupplierData", {}, function (res) {
            if(res.code == 200) {
                supplierData = res.data;
            }
        });
    }

    // 内部流程--弹出“供应商状态”新增对话框
    function showEditSupplierSateModel(data){
        layer.open({
            type: 1,
            area: ['360px','310px'],
            offset: '65px',
            // offset: '65px',
            title: data ? '编辑供应商状态信息' : '新增供应商状态信息',
            content: $('#supplierSateForm').html(),
            success: function (layero, index) {
// ======================== 新增逻辑处理 Start ========================
                // 渲染项目成员
                if(data) {
                    // 编辑 内部流程 -- 渲染“供应商状态”下拉框
                    var tmp = [{id: data.id, name: data.name}].concat(supplierData);
                    arraySort(tmp);
                    initSelect("name", tmp, "");
                } else {
                    arraySort(supplierData);
                    initSelect("name", supplierData, "");
                }
// ======================== 编辑逻辑处理 Start ========================
                if (data){
                    form.val('supplierSateForm', data);
                    $("#name").val(data.id);
                }
                form.render();

                // “供应商状态”表单提交处理
                form.on('submit(supplierSateFormSubmit)', function (d){
                    layer.load(2);
                    if(data) {
                        layer.closeAll("loading");
                        // 实现思路，将form的数据认为是新的数据，直接进行添加，对表格的数据，重新更新supplierTabData
                        var tmpSupplierData = [];
                        var rowData_ = {};
                        rowData_.id = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.state = d.field.state;

                        for(var i=0; i<supplierTabData.length; i++) {
                            if(data.id == supplierTabData[i].id) {
                                tmpSupplierData.push(rowData_);
                            } else {
                                tmpSupplierData.push(supplierTabData[i]);
                            }
                        }
                        supplierTabData = tmpSupplierData;
                        table.reload('supplierShowTable',{data : supplierTabData});

                        var tempData = [];
                        for(var i=0; i<supplierData.length; i++) {
                            if(rowData_.id != supplierData[i].id) {
                                tempData.push(supplierData[i]);
                            } else {
                                removeSupplierData.push(supplierData[i]);
                            }
                        }
                        supplierData = tempData;

                        var tempRmData = [];
                        for(var i=0; i<removeSupplierData.length; i++) {
                            if(data.id != removeSupplierData[i].id) {
                                tempRmData.push(removeSupplierData[i]);
                            } else {
                                supplierData.push(removeSupplierData[i]);
                            }
                        }
                        removeSupplierData = tempRmData;

                        layer.msg("修改成功！", {icon: 1});
                        layer.close(index);
                    } else {
                        layer.closeAll("loading");
                        // supplierTabData, supplierData, removeSupplierData;
                        var rowData_ = {};
                        rowData_.id = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.state = d.field.state;
                        supplierTabData.push(rowData_);
                        table.reload('supplierShowTable',{data : supplierTabData});

                        var tempData = [];
                        for(var i=0; i<supplierData.length; i++) {
                            if(rowData_.id != supplierData[i].id) {
                                tempData.push(supplierData[i]);
                            } else {
                                removeSupplierData.push(supplierData[i]);
                            }
                        }
                        supplierData = tempData;

                        layer.msg("添加成功！", {icon: 1});
                        layer.close(index);
                    }
                    return false;
                });
            }
        });
    }


    // 内部流程上传附件回调
    function internalEditFiles(obj){
        $('#internalUpdateList').html("");
        // “内部流程”上传附件数据回调
        $.ajax({
            url: '/file/fileList?type=12&correlatorId=' + obj,
            type:"get"
            ,async:false
            ,dataType:"json"
            , success: function(res){
                if(res.code == 200) {
                    var ds = res.data;
                    if(ds.length > 0) {
                        for (var i = 0; i < ds.length; i++) {
                            (function (d) {
                                var tr = $(['<tr id="' + d.fileId + '" name="' + d.fileUrl + '">'
                                    , '<td>' + d.fileName + '</td>'
                                    , '<td>已上传</td>'
                                    , '<td>'
                                    , '<button class="layui-btn layui-btn-xs layui-btn-danger data-delete">删除</button>'
                                    , '<button class="layui-btn layui-btn-xs data-download">下载</button>'
                                    , '</td>'
                                    , '</tr>'].join(''));
                                //删除
                                tr.find('.data-delete').on('click', function (param) {
                                    layer.confirm('确定要删除吗？', {
                                        offset: '65px',
                                        title: '提示'
                                    }, function (i) {
                                        layer.close(i);
                                        // delete files[index]; //删除对应的文件
                                        var fileId = tr.attr("id");
                                        var fileUrl = tr.attr("name");
                                        var tds = tr.children();
                                        var fileName = tds.eq(0).html();
                                        // 删除文件库中的文件
                                        $.post("/file/deleleFile", {
                                            fileId: fileId,
                                            fileUrl: fileUrl.substring(0, fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)),
                                            type: 12
                                        }, function (res) {
                                            if (res.code == 200) {
                                                layer.msg("删除成功！");
                                                tr.remove();
                                            } else {
                                                layer.msg(res.msg);
                                            }
                                        });
                                    });
                                    return false;
                                });

                                // 下载
                                tr.find('.data-download').on('click', function () {
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    var config = $.extend(true, {method: 'GET'}, {
                                        url: '/file/download', data: {
                                            fileId: fileId,
                                            fileUrl: fileUrl.substring(0, fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)),
                                            fileName: fileName
                                        }
                                    });
                                    $iframe = $('<iframe id="down-file-iframe" />'), $form = $('<form target="down-file-iframe" method="' + config.method + '" />');
                                    $form.attr('action', config.url);
                                    for (var key in config.data) {
                                        $form.append('<input type="hidden" name="' + key + '" value="' + config.data[key] + '" />');
                                    }
                                    $iframe.append($form);
                                    $(document.body).append($iframe);
                                    $form[0].submit();
                                    $iframe.remove();
                                    return false;
                                });
                                $('#internalUpdateList').append(tr);
                            })(ds[i]);
                        }
                    }
                }

            }
        });
    }

    // “内部流程”附件上传
    function loadInsteralFileUpload() {
        //多文件列表示例
        var demoListView = $('#internalUpdateList')
            ,uploadListIns = upload.render({
            elem: '#testInternalList'
            ,url: '/file/upload/'
            ,accept: 'file'
            ,size: 10240
            ,multiple: true
            ,number: 5
            ,auto: false
            ,bindAction: '#testInternalListAction'
            ,before: function() {
                this.data = {"id": internalProIdTemp, "type": 12}
            }
            ,choose: function(obj){
                var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                //读取本地文件
                obj.preview(function(index, file, result){
                    var tr = $(['<tr id="upload-'+ index +'">'
                        ,'<td>'+ file.name +'</td>'
                        , '<td>等待上传</td>'
                        ,'<td>'
                        ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
                        ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
                        ,'</td>'
                        ,'</tr>'].join(''));

                    //单个重传
                    tr.find('.demo-reload').on('click', function(){
                        obj.upload(index, file);
                        return false;
                    });

                    //删除
                    tr.find('.demo-delete').on('click', function(){
                        layer.confirm('确定要删除吗？', {
                            offset: '65px',
                            title: '提示'
                        }, function (i) {
                            layer.close(i);
                            delete files[index]; //删除对应的文件
                            tr.remove();
                            uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                        });
                        return false;
                    });

                    demoListView.append(tr);
                });
            }
            ,done: function(res, index, upload){
                if(res.code == 200){ //上传成功
                    var fileId = res.data; //附件id
                    fileIds.push(JSON.parse(fileId).id);
                    console.log(fileIds);
                    var tr = demoListView.find('tr#upload-'+ index)
                        ,tds = tr.children();
                    tds.eq(2).html(''); //清空操作
                    return delete this.files[index]; //删除文件队列已经上传成功的文件
                }
                this.error(index, upload);
            }
            ,error: function(index, upload){
                var tr = demoListView.find('tr#upload-'+ index)
                    ,tds = tr.children();
                tds.eq(2).find('.demo-reload').removeClass('layui-hide'); //显示重传
            }
        });
    }
// ==============================================内部流程功能 start==========================================
});