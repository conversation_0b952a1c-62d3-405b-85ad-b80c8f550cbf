layui.use(['form', 'jquery', 'table', 'layer',  'laydate', 'formSelects', 'upload', 'element', 'util'], function () {
    var form = layui.form;
    var element = layui.element; //Tab的切换功能，切换事件监听等，需要依赖element模块
    var $ = layui.jquery;
    var table = layui.table;
    var util = layui.util;
    var layer = layui.layer;
    var laydate = layui.laydate;
    var formSelects = layui.formSelects;
    var upload = layui.upload;
    var util = layui.util;
// ==========================================初始化变量 start===============================================
    var tagsData = new Array(), isHideCol = false, supplierData, removeSupplierData=[], internalProIdTemp, internalURL;
    var customsData, memberTabData = [], userData, removeUserData=[], schedulingTabData = [], fileIds, programIdVal, supplierTabData = [];
    var stateObj = {"1": "未开始", "2": "进行中", "3": "已结束", "4": "延期", "5": "中止", "6": "其他"}, isCanChangeTag = false;
// ==========================================初始化变量 end=================================================

// ==========================================渲染主页 start===============================================
    //渲染表格
    table.render({
        elem: '#othersTable',
        url: '/otherprogram/getlist',
        // toolbar: '#customtoolbar',
        method: 'post',
        page: true,
        cellMinWidth: 100,
        response: {
            statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
        },
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
            return {
                "code": res.code, //解析接口状态
                "msg": res.message, //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.data //解析数据列表
            };
        },
        cols: [[
            {type: 'numbers', title: '序号'},
            {field: 'name', title: '项目名称'},
            {
                templet: function (res) {
                    if(res.type == 1){
                        return '年度项目';
                    }else if(res.type == 2){
                        return '单个项目';
                    }else if(res.type == 3){
                        return '系列项目';
                    }
                    return "";
                }, title: '项目类型'
            },
            {
                templet: function (res) {
                    if(res.belong == 1){
                        return '集团内业务';
                    }else if(res.belong == 2){
                        return '集团外业务';
                    }
                    return "";
                }, title: '业务类型'
            },
            {
                templet: function (d) {
                    var str = '';
                    if(d.projectMemberVos != undefined) {
                        for (var i = 0; i < d.projectMemberVos.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.projectMemberVos[i].name + '</span>');
                        }
                    }
                    return str;
                }, title: '项目成员'
            },
            {
                templet: function (res) {
                    if(res.state == 1){
                        return '未开始';
                    }else if(res.state == 2){
                        return '正常开展';
                    }else if(res.state == 3){
                        return '已完成';
                    }else if(res.state == 4){
                        return '延期';
                    }else if(res.state == 5){
                        return '中止';
                    }else if(res.state == 6){
                        return '其他';
                    }
                    return "";
                }, title: '项目状态'
            },
            {
                templet: function (d) {
                    return util.toDateString(d.createDate, "yyyy-MM-dd");
                }, title: '创建时间'
            },
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 200}
        ]],
        done: function () {
            layer.closeAll('loading');
        }
    });

    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        var projectName = $('#projectNameCond').val();
        var projectType = $('#type').val();
        var belongTo = $('#belong').val();
        table.reload('othersTable', {where: {name: projectName, type: projectType, belong: belongTo}});
    });

    // 新增按钮点击事件
    $('#btnAdd').click(function () {
        programIdVal = '';
        showEditModel();
    });

    // 工具条点击事件
    table.on('tool(othersTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'del') { // 删除
            doDelete(obj);
        } else if (layEvent === 'detail') { // 查看
            showEditModel(data, 'detail');
        } else if (layEvent === 'edit') { // 修改
            showEditModel(data);
        }
    });

    // 删除
    function doDelete(obj) {
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);
            $.post("/otherprogram/deleteByID", {id: obj.data.id}, function (res){
                layer.closeAll('loading');
                if(res.code == 200){
                    layer.msg("删除成功");
                    obj.del();
                    table.reload('othersTable');
                }else{
                    layer.msg(res.msg);
                }
            });
        });
    }

    $(document).on('click', '#addSupplierButton', function () {
        showEditSupplierStateModel();
    });

    // 切换标签，内部流程“附件上传”
    function initInternalUpload(){
        loadInternalFileUpload();
    }

    $(document).on('click', '#cancelBtn', function () {
        layer.closeAll('page');
    });
// ==========================================渲染主页 end===============================================

// =====================================咨询-咨询业务弹框 start=========================================
    // 显示表单弹窗
    function showEditModel(data, showType) {
        fileIds = new Array();
        isCanChangeTag = false;
        layer.open({
            type: 1,
            area: ['61%', '100%'],
            // offset: '65px',
            title: showType? '查看咨询-咨询业务' : data ? '修改咨询-咨询业务' : '新增咨询-咨询业务',
            content: $('#othersForm').html(),
            success: function (layero, index) {
                // tab切换标签触发函数
                element.on('tab(docDemoTabBrief)', function(d){
                    if(d.index == 0){
                        // 表示为业务流程
                    }else{//表示为内部流程
                        changeInternalShow();
                        // 获取 内部流程“供应商状态”Table下拉框数据
                        getSupplierData();
                        //内部流程处理
                        getInternalProgramData();
                        // 切换标签，内部流程“附件上传”
                        initInternalUpload();
                    }
                    //layui.form.render();
                });

                // 处理当前tab标签是否可以切换
                if(data) {
                    isCanChangeTag = true;
                } else {
                    isCanChangeTag = false;
                }
                changeInternalShow();

                // 初始化时间控件
                laydate.render({
                    elem: '#starttime' //指定元素
                    ,done: function(value, date, finishedDate){
                        var eDate = $("#finishtime").val();
                        if(eDate!="" && compareDate(value, eDate)) {
                            $("#finishtime").val(value)
                        }
                    }
                });

                laydate.render({
                    elem: '#finishtime' //指定元素
                    ,done: function(value, date, finishedDate){
                        var sDate = $("#starttime").val();
                        if(finishedDate!="" && compareDate(sDate, value)) {
                            $("#starttime").val(value)
                        }
                    }
                });

                // 渲染标签下拉框
                var tagSelData = new Array();
                for (var i = 0; i < tagsData.length; i++) {
                    tagSelData.push({name: tagsData[i].name, value: tagsData[i].id});
                }
                formSelects.data('selTag', 'local', {arr: tagSelData});

                // 渲染客户
                initSelect("customId", customsData, "客户");

                // 查询当前业务的项目成员
                getUserData();

                if(!data) {
                    // 渲染项目成员
                    showMemberTab();
                    // 渲染项目排期
                    showSchedulingTab();
                } else {
                    programIdVal = data.id;
                    // 处理下时间数据
                    data.starttime = data.starttime == undefined ? '' : data.starttime.split(" ")[0];
                    data.finishtime = data.finishtime == undefined ? '' : data.finishtime.split(" ")[0];
                    form.val('othersForm', data);
                    $.get("/tag/getTag", {correlatorId: data.id}, function (res) {
                        if(res.code == 200) {
                            var rds = new Array();
                            for (var i = 0; i < res.data.length; i++) {
                                rds.push(res.data[i].id);
                            }
                            formSelects.value('selTag', rds);  // 回显多选框
                        }
                    });
                    // 渲染客户表格
                    var text = $("#customId option:selected").text();
                    $.get("/linkman/getLinkMans", {customId: data.customId}, function (res) {
                        if(res.code == 200) {
                            showCustomTab(res.data, text)
                        }
                    });
                    isHideCol = false;
                    if(showType) {
                        isHideCol = true;
                    }
                    // 渲染项目成员
                    showMemberTab(data.id);
                    // 渲染项目排期
                    showSchedulingTab(data.id);
                    // 渲染文件
                    initFileTab();
                }
                // 渲染文件上传控件信息
                loadFileUpload();

                // 控制form表单是否可以编辑
                if(showType) {
                    $('form').find('input,select,textarea').attr('disabled', true);
                    $("#testList").css('display', 'none');
                    $("#cancelBtn").css('display', 'none');
                    $("#othersFormSubmit").css('display', 'none');
                    $('#demoList').find('.data-delete').addClass('layui-hide');
                    $('#addMemberButton').css('display', 'none');
                    $('#addSchedulingButton').css('display', 'none');
                    $('#addSupplierButton').css('display', 'none');
                    $('#testInternalList').css('display', 'none');
                    $("#closeInternalDialog").css('display', 'none');
                    $("#internalFormSubmit").css('display', 'none');
                    $('#internalUpdateList').find('.data-delete').addClass('layui-hide');
                    formSelects.disabled();
                }
                form.render();
                // 表单提交事件
                form.on('submit(othersFormSubmit)', function (d) {
                    var projectName1 = $('#projectNameCond').val();
                    var projectType = $('#type').val();
                    var belongTo = $('#belong').val();
                    // 判断当前的项目成员是否为空,
                    if(memberTabData.length == 0) {
                        layer.msg("项目成员数据需要维护！", {icon: 2});
                        return false;
                    }
                    layer.load(2);
                    // 处理标签
                    var tempTags = [];
                    if(d.field.tagId != '') {
                        var tag = d.field.tagId.split(",");
                        for(var i=0; i<tag.length; i++) {
                            tempTags.push({id: tag[i]});
                        }
                    }
                    // 组装数据
                    var param = {
                        id: d.field.id || programIdVal
                        ,name: d.field.name
                        ,type: d.field.type
                        ,belong: d.field.belong
                        ,expectedRevenue: d.field.expectedRevenue
                        ,projectedCost: d.field.projectedCost
                        ,starttime: d.field.starttime
                        ,finishtime: d.field.finishtime
                        ,serverPeople: d.field.serverPeople
                        ,tags: tempTags
                        ,customId: d.field.customId
                        ,content: d.field.content
                        ,comment: d.field.comment
                        ,projectMemberVos: memberTabData
                        ,consultingProgramScheduling: schedulingTabData
                        ,summary: d.field.summary
                        ,grade: d.field.grade
                        ,state: d.field.state
                    };
                    if(data || programIdVal) {
                        $.ajax({
                            type: 'POST',
                            url: '/otherprogram/update',//发送请求
                            contentType: "application/json; charset=utf-8",
                            async: true,
                            data: JSON.stringify(param),
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll("loading");
                                if(res.code == 200){
                                    layer.msg("修改成功！", {icon: 1});
                                    //layer.closeAll('page');
                                    table.reload('othersTable', {where: {name: projectName1, type: projectType, belong: belongTo}});
                                    $("#testListAction").trigger("click");
                                }else{
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                    } else {
                        $.ajax({
                            type: 'POST',
                            url: '/otherprogram/create',//发送请求
                            contentType: "application/json; charset=utf-8",
                            async: true,
                            data: JSON.stringify(param),
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll("loading");
                                if(res.code == 200){
                                    layer.msg("新增成功！", {icon: 1});
                                    table.reload('othersTable', {where: {name: projectName1, type: projectType, belong: belongTo}});
                                    programIdVal = res.data;
                                    isCanChangeTag = true;
                                    $("#testListAction").trigger("click");
                                    changeInternalShow();
                                }else{
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                    }
                    return false;
                });
            }
        });
    }

    $(document).on('click', '#closeDialog', function () {
        var index=parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        layer.close(layer.index);
    });

    $(document).on('click', '#closeDialog1', function () {
        var index=parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        layer.close(layer.index);
    });

    // =============================================================== “内部流程”逻辑处理 Start ===============================================================//
    $(document).on('click', '#closeInternalDialog', function () {
        layer.closeAll('page');
    });
    $(document).on('click', '#closeSupplierState', function () {
        var index=parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        layer.close(layer.index);

    });

    // 处理“内部流程样式”
    function changeInternalShow(){
        var tabId = document.getElementById("showP");
        if(isCanChangeTag){
            tabId.style.color = "";
            tabId.style.cursor = "";
            tabId.style.pointerEvents = "";
        }else{
            tabId.style.color = "slategrey";
            tabId.style.cursor = "not-allowed";
            tabId.style.pointerEvents = "none";
        }
    }

    // 内部流程--弹出“供应商状态”新增对话框
    function showEditSupplierStateModel(data){
        layer.open({
            type: 1,
            area: ['360px', '310px'],
            offset: '65px',
            title: data ? '编辑供应商状态信息' : '新增供应商状态信息',
            content: $('#supplierStateForm').html(),
            success: function (layero, index) {
// ======================== 新增逻辑处理 Start ========================
                // 渲染项目成员
                if(data) {
                    // 编辑 内部流程 -- 渲染“供应商状态”下拉框
                    var tmp = [{id: data.id, name: data.name, state: data.state}].concat(supplierData);
                    arraySort(tmp);
                    initSelect("name", tmp, "");
                } else {
                    arraySort(supplierData);
                    initSelect("name", supplierData, "");
                }
// ======================== 编辑逻辑处理 Start ========================
                if (data){
                    form.val('supplierStateForm', data)
                    $("#name").val(data.id);
                }
                form.render();

                // “供应商状态”表单提交处理
                form.on('submit(supplierStateFormSubmit)', function (d){
                    layer.load(2);
                    if(data) {
                        layer.closeAll("loading");
                        // 实现思路，将form的数据认为是新的数据，直接进行新增，对表格的数据，重新更新memberTabData
                        var tmpSupplierData = [];
                        var rowData_ = {};
                        rowData_.id = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.state = d.field.state;

                        for(var i=0; i<supplierTabData.length; i++) {
                            if(data.id == supplierTabData[i].id) {
                                tmpSupplierData.push(rowData_);
                            } else {
                                tmpSupplierData.push(supplierTabData[i]);
                            }
                        }
                        supplierTabData = tmpSupplierData;
                        table.reload('supplierShowTable',{data : supplierTabData});

                        var tempData = [];
                        for(var i=0; i<supplierData.length; i++) {
                            if(rowData_.id != supplierData[i].id) {
                                tempData.push(supplierData[i]);
                            } else {
                                removeSupplierData.push(supplierData[i]);
                            }
                        }
                        supplierData = tempData;

                        var tempRmData = [];
                        for(var i=0; i<removeSupplierData.length; i++) {
                            if(data.id != removeSupplierData[i].id) {
                                tempRmData.push(removeSupplierData[i]);
                            } else {
                                supplierData.push(removeSupplierData[i]);
                            }
                        }
                        removeSupplierData = tempRmData;

                        layer.msg("修改成功！", {icon: 1});
                        layer.close(index);
                    } else {
                        layer.closeAll("loading");
                        // supplierTabData, supplierData, removeSupplierData;
                        var rowData_ = {};
                        rowData_.id = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.state = d.field.state;
                        supplierTabData.push(rowData_);
                        table.reload('supplierShowTable',{data : supplierTabData});

                        var tempData = [];
                        for(var i=0; i<supplierData.length; i++) {
                            if(rowData_.id != supplierData[i].id) {
                                tempData.push(supplierData[i]);
                            } else {
                                removeSupplierData.push(supplierData[i]);
                            }
                        }
                        supplierData = tempData;

                        layer.msg("新增成功！", {icon: 1});
                        layer.close(index);
                    }
                    return false;
                });
            }
        });
    }

    // 内部流程业务处理--“供应商状态”Table
    function showEditIntrenalModel(internalId){
        supplierTabData = [];
        removeSupplierData = [];
        if(internalId) {
            // 获取项目的数据
            $.get("/supplierinternalprogram/getCustomState", {internalId: internalId}, function (res) {
                if(res.code == 200) {
                    supplierTabData = res.data;
                    table.reload('supplierShowTable',{data : supplierTabData});
                    // 处理 supplierData，removeSupplierData
                    removeSupplierData = intersection(supplierData, supplierTabData, 'id');
                    supplierData = difference(supplierData, supplierTabData, 'id');
                }
            });
        }

        //渲染表格
        table.render({
            elem: '#supplierShowTable',
            //url: '/supplierinternalprogram/getlist',
            // toolbar: '#customtoolbar',
            data: supplierTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                ,statusCode: 200 //规定成功的状态码，默认：0
                ,msgName: 'msg' //规定状态信息的字段名称，默认：msg
                ,countName: 'count' //规定数据总数的字段名称，默认：count
                ,dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {type: 'numbers', title: '序号'},
                {field: 'name', title: '名称'},
                {
                    templet: function (res) {
                        if(res.state == 0){
                            return '启动';
                        }else if(res.state == 1){
                            return '招采';
                        }else if(res.state == 2){
                            return '合同签订';
                        }else if(res.state == 3){
                            return '付款';
                        }else if(res.state == 4){
                            return '归档';
                        }else{
                            return '';
                        }

                    }, title: '状态'
                },
                {align: 'center', toolbar: '#supplierStateTableBar', title: '操作', minWidth: 200}
            ]],
            done: function () {
                if(isHideCol) {
                    $('#supplierShowTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });

        // 内部流程“供应商状态”工具条点击事件
        table.on('tool(supplierShowTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doSupplierDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showEditSupplierStateModel(data);
            }
        });
    }

    // “内部流程”主表单提交处理
    form.on('submit(internalFormSubmit)', function (d){
        var data_row = d.field;
        var supplierInternalProgramList = [];

        // 处理数据
        var costSupplierTemp = data_row.costSupplier.replace(/,/g,'');
        var costFareTemp = data_row.costFare.replace(/,/g,'');
        var costMealTemp = data_row.costMeal.replace(/,/g,'');
        var costIncidentalTemp = data_row.costIncidental.replace(/,/g,'');
        var costHousingTemp = data_row.costHousing.replace(/,/g,'');
        var costTeaTemp = data_row.costTea.replace(/,/g,'');
        var costPrintTemp = data_row.costPrint.replace(/,/g,'');
        var costStoresTemp = data_row.costStores.replace(/,/g,'');
        var costTeacherTravelTemp = data_row.costTeacherTravel.replace(/,/g,'');
        var costTravelTemp = data_row.costTravel.replace(/,/g,'');
        var costOtherTemp = data_row.costOther.replace(/,/g,'');
        var contractAmountTemp = data_row.contractAmount.replace(/,/g,'');
        var taxRevenueTemp = data_row.taxRevenue.replace(/,/g,'');
        var aftertaxCostTemp = data_row.aftertaxCost.replace(/,/g,'');
        var totalTemp = Number(costSupplierTemp) +
            Number(costFareTemp) +
            Number(costMealTemp) +
            Number(costIncidentalTemp) +
            Number(costHousingTemp) +
            Number(costTeaTemp) +
            Number(costPrintTemp) +
            Number(costStoresTemp) +
            Number(costTeacherTravelTemp) +
            Number(costTravelTemp) +
            Number(costOtherTemp);
        //$("#totalCost").val(totalTemp);
        var totalTempStr = String(totalTemp);

        // 处理“项目排期”数据
        for(var i=0; i<supplierTabData.length;i++){
            var internalIdTemp = '';

            if(supplierTabData[i].internalId){
                internalIdTemp = supplierTabData[i].internalId;
            }
            var supplierJson = {
                supplierId: supplierTabData[i].id,
                internalId: internalIdTemp,
                state: supplierTabData[i].state
            }
            supplierInternalProgramList.push(supplierJson);
        }
        // 供应商删除id
        /*            for(var i=0;i<removeSupplierData.length;i++){
                        var removeSuppTem = {
                            tempIds: removeSupplierData[i].id,
                            tempBelong: 'supplierInternal'
                        }
                        removesupplierInternalProgramList.push(removeSuppTem);
                    }*/

        var param = {
            id:internalProIdTemp,
            // 供应商
            costSupplier: costSupplierTemp,
            // 交通费
            costFare: costFareTemp,
            // 餐费
            costMeal: costMealTemp,
            // 杂费
            costIncidental: costIncidentalTemp,
            // 住宿费
            costHousing: costHousingTemp,
            // 茶歇费
            costTea: costTeaTemp,
            // 打印费
            costPrint: costPrintTemp,
            // 物料费
            costStores: costStoresTemp,
            // 讲师差旅费
            costTeacherTravel: costTeacherTravelTemp,
            // 差旅费
            costTravel: costTravelTemp,
            // 其他
            costOther: costOtherTemp,
            // 客户状态
            customState: data_row.customState,
            //合同总金额含税
            contractAmount: contractAmountTemp,
            // 项目税后总营收
            taxRevenue: taxRevenueTemp,
            // 总成本
            totalCost: totalTempStr,
            //税后成本
            aftertaxCost: aftertaxCostTemp,
            // 项目状态
            state: data_row.state,
            // 项目Id
            programId: programIdVal,
            // 内部关联
            supplierInternalProgramList: supplierInternalProgramList
        }
        if(param.costSupplier == '' && param.costFare == '' && param.costMeal == '' && param.costIncidental == '' && param.costHousing == '' && param.costTea == '' &&
            param.costPrint == '' &&  param.costStores == '' && param.costTeacherTravel == '' && param.costTravel == '' && param.costOther == '' && param.customState == '' &&
            param.contractAmount == '' && param.taxRevenue == '' && param.aftertaxCost == '' &&  param.supplierInternalProgramList.length == 0){

            layer.msg("没有填写项目，无法保存！", {icon: 1});
            return false;

        }
        $.ajax({
            type: 'POST',
            url: internalURL,//发送请求
            contentType: "application/json; charset=utf-8",
            async: true,
            data: JSON.stringify(param),
            dataType: "json",
            success: function (res) {
                layer.closeAll("loading");
                if(res.code == 200){
                    layer.msg("新增成功！", {icon: 1});
                    layer.closeAll('page');
                    // 如果内部流程新增，那么新增完成后获取“内部流程”ID
                    if(!internalProIdTemp){
                        internalProIdTemp = res.data;
                    }
                    $("#testInternalListAction").trigger("click");
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
        return false;
    });

    // 内部流程 “供应商状态”删除
    function doSupplierDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理memberTabData, userData, removeUserData;
            var tmpMemberData = [];
            for(var i=0; i<supplierTabData.length; i++) {
                if(id != supplierTabData[i].id) {
                    tmpMemberData.push(supplierTabData[i]);
                }
            }
            supplierTabData = tmpMemberData;
            // table.reload('memberTable',{data : memberTabData});

            var tempRmData = [];
            for(var i=0; i<removeSupplierData.length; i++) {
                if(id != removeSupplierData[i].id) {
                    tempRmData.push(removeSupplierData[i]);
                } else {
                    supplierData.push(removeSupplierData[i]);
                }
            }
            removeSupplierData = tempRmData;

            layer.msg("删除成功");
            obj.del();
        });
    }

    // 内部流程 编辑获取数据
    function getInternalProgramData(){
        internalProIdTemp = '';
        $.get("/internalprogram/getInternal", {id: programIdVal}, function (res) {
            if(res.code == 200) {
                if(res.data.length > 0){
                    internalProIdTemp = res.data[0].id;
                    // 内部流程业务处理--“供应商状态”Table
                    showEditIntrenalModel(internalProIdTemp);
                    internalEditFiles(internalProIdTemp);
                    form.val('othersInternalForm', res.data[0]);
                    internalURL = '/internalprogram/update';
                    if(isHideCol){
                        $("#testInternalList").css('display', 'none');
                        $("#internalCancelBtn").css('display', 'none');
                        $("#internalFormSubmit").css('display', 'none');
                        $('#internalUpdateList').find('.data-delete').addClass('layui-hide');
                        $("#addSupplierButton").css('display', 'none');
                    }
                }else{
                    showEditIntrenalModel();
                    if(isHideCol){
                        $("#testInternalList").css('display', 'none');
                        $("#internalCancelBtn").css('display', 'none');
                        $("#internalFormSubmit").css('display', 'none');
                        $('#internalUpdateList').find('.data-delete').addClass('layui-hide');
                        $("#addSupplierButton").css('display', 'none');
                    }
                    internalURL = '/internalprogram/create';
                }

            }
        });
    }

    // 内部流程上传附件回调
    function internalEditFiles(tempProgramId){
        // “内部流程”上传附件数据回调
        $('#internalUpdateList').html("");
        $.ajax({
            url: '/file/fileList?type=12&correlatorId=' + tempProgramId,
            type:"get"
            ,async:false
            ,dataType:"json"
            , success: function(res){
                if(res.code == 200) {
                    var ds = res.data;
                    if(ds.length > 0) {
                        for (var i = 0; i < ds.length; i++) {
                            (function (d) {
                                var tr = $(['<tr id="' + d.fileId + '" name="' + d.fileUrl + '">'
                                    , '<td>' + d.fileName + '</td>'
                                    , '<td>已上传</td>'
                                    , '<td>'
                                    , '<button class="layui-btn layui-btn-xs layui-btn-danger data-delete">删除</button>'
                                    , '<button class="layui-btn layui-btn-xs data-download">下载</button>'
                                    , '</td>'
                                    , '</tr>'].join(''));
                                //删除
                                tr.find('.data-delete').on('click', function (param) {
                                    // delete files[index]; //删除对应的文件
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    // 删除文件库中的文件
                                    $.post("/file/deleleFile", {
                                        fileId: fileId,
                                        fileUrl: fileUrl.substring(0, fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)),
                                        type: 12
                                    }, function (res) {
                                        if (res.code == 200) {
                                            layer.msg("删除成功！");
                                            tr.remove();
                                        } else {
                                            layer.msg(res.msg);
                                        }
                                    });
                                    return false;
                                    // uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                                });

                                // 下载
                                tr.find('.data-download').on('click', function () {
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    var url = '/file/download';
                                    var option = {
                                        data: {fileId: fileId,
                                            fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)), fileName: fileName}
                                    }
                                    $.get("/file/isExistFile", {fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName))}, function (res) {
                                        if(res.code == 200) {
                                            download(url, option);
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    });
                                    return false;
                                });
                                $('#internalUpdateList').append(tr);
                            })(ds[i]);
                        }
                    }
                }

            }
        });
    }

    // 获取 内部流程 “供应商”数据
    function getSupplierData(){
        // 获取供应商数据
        $.get("/base/supplier/getSupplierData", {}, function (res) {
            if(res.code == 200) {
                supplierData = res.data;
            }
        });
    }

    // “内部流程”附件上传
    function loadInternalFileUpload() {
        //多文件列表示例
        var demoListView = $('#internalUpdateList')
            ,uploadListIns = upload.render({
            elem: '#testInternalList'
            ,url: '/file/upload'
            ,accept: 'file'
            ,size: 10240
            ,multiple: true
            ,number: 5
            ,auto: false
            ,bindAction: '#testInternalListAction'
            ,before: function() {
                this.data = {"id": internalProIdTemp, "type": 12}
            }
            ,choose: function(obj){
                var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                //读取本地文件
                obj.preview(function(index, file, result){
                    var tr = $(['<tr id="upload-'+ index +'">'
                        ,'<td>'+ file.name +'</td>'
                        , '<td>等待上传</td>'
                        ,'<td>'
                        ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
                        ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
                        ,'</td>'
                        ,'</tr>'].join(''));

                    //单个重传
                    tr.find('.demo-reload').on('click', function(){
                        obj.upload(index, file);
                        return false;
                    });

                    //删除
                    tr.find('.demo-delete').on('click', function(){
                        layer.confirm('确定要删除吗？', {
                            offset: '65px',
                            title: '提示'
                        }, function (i) {
                            layer.close(i);
                            delete files[index]; //删除对应的文件
                            tr.remove();
                            uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                        });
                    });

                    demoListView.append(tr);
                });
            }
            ,done: function(res, index, upload){
                if(res.code == 200){ //上传成功
                    var fileId = res.data; //附件id
                    fileIds.push(JSON.parse(fileId).id);
                    console.log(fileIds);
                    var tr = demoListView.find('tr#upload-'+ index)
                        ,tds = tr.children();
                    tds.eq(2).html(''); //清空操作
                    return delete this.files[index]; //删除文件队列已经上传成功的文件
                }
                this.error(index, upload);
            }
            ,error: function(index, upload){
                var tr = demoListView.find('tr#upload-'+ index)
                    ,tds = tr.children();
                tds.eq(2).find('.demo-reload').removeClass('layui-hide'); //显示重传
            }
        });
    }
// =============================================================== “内部流程”逻辑处理 End ===============================================================//
    // 对象数组排序
    function compareDate(DateOne, DateTwo) {
        var OneMonth = DateOne.substring(5, DateOne.lastIndexOf("-"));
        var OneDay = DateOne.substring(DateOne.length, DateOne.lastIndexOf("-") + 1);
        var OneYear = DateOne.substring(0, DateOne.indexOf("-"));
        var TwoMonth = DateTwo.substring(5, DateTwo.lastIndexOf("-"));
        var TwoDay = DateTwo.substring(DateTwo.length, DateTwo.lastIndexOf("-") + 1);
        var TwoYear = DateTwo.substring(0, DateTwo.indexOf("-"));
        if (Date.parse(OneMonth + "/" + OneDay + "/" + OneYear) > Date.parse(TwoMonth + "/" + TwoDay + "/" + TwoYear)) {
            return true;
        } else {
            return false;
        }
    }

    // 获取客户和标签的数据，并初始化对应的选择控件
    layer.load(2);
    $.get("/otherprogram/getinit", {}, function (res) {
        layer.closeAll('loading');
        if(res.code == 200) {
            customsData = res.data.customs;
            initSelect("customId", customsData, "客户");
            tagsData = res.data.tags;
            var tagSelData = new Array();
            for (var i = 0; i < tagsData.length; i++) {
                tagSelData.push({name: tagsData[i].name, value: tagsData[i].id});
            }
            formSelects.data('selTag', 'local', {arr: tagSelData});
            form.render();
        } else {
            layer.msg('获取初始化数据失败');
        }
    });

    function initSelect(ctlId, data, tip) {
        var $sel = $("#"+ ctlId);
        $sel.empty();
        $sel.append('<option value="'+""+'">'+"请选择" + tip +'</option>');


        for(var i=0; i<data.length; i++){
            $sel.append('<option selected value="'+ data[i].id +'">'+ data[i].name +'</option>');
        }
        $sel.val('');
    }
// ========================================咨询-咨询业务弹框 end============================================

// ==========================================客户栏处理 start===============================================
    // 绑定事件
    form.on('select(customId)', function(data){
        console.log(data);
        var text = data.elem[data.elem.selectedIndex].text;
        $.get("/linkman/getLinkMans", {customId: data.value}, function (res) {
            if(res.code == 200) {
                showCustomTab(res.data, text)
            }
        });
    });
    function showCustomTab(data, text) {
        $("#customList").html("");
        if(data.length > 0) {
            for(var i=0; i<data.length; i++) {
                var tr = $(['<tr id="'+ data[i].id +'">'
                    ,'<td>'+ text +'</td>'
                    ,'<td>'+ (data[i].name == undefined ? '':data[i].name) +'</td>'
                    , '<td>'+ (data[i].phone == undefined ? '':data[i].phone) +'</td>'
                    , '<td>'+ (data[i].email == undefined ? '':data[i].email) +'</td>'
                    , '<td>'+ (data[i].contactAddress == undefined ? '':data[i].contactAddress) +'</td>'
                    ,'</tr>'].join(''));
                $("#customList").append(tr);
            }
        }

    }
// ==========================================客户栏处理 end===================================================

// ==========================================项目成员处理 start===============================================
    // 交集
    function intersection (arr1, arr2, key) {
        var tmpArr = [];
        for(var i=0;i<arr1.length;i++){
            for(var j=0;j<arr2.length;j++){
                var arr = arr2[j];
                if(arr1[i].id == eval('arr.' + key)){
                    tmpArr.push(arr1[i]);
                }
            }
        }
        return tmpArr;
    }
    // 差集
    function difference(arr1, arr2, key) {
        var tmpArr = [];
        for(var i=0;i<arr1.length;i++){
            var flag = true;
            for(var j=0;j<arr2.length;j++){
                var arr = arr2[j];
                if(arr1[i].id == eval('arr.' + key)){
                    flag = false;
                }
            }
            if(flag){
                tmpArr.push(arr1[i]);
            }
        }
        return tmpArr;
    }

    function showMemberTab(programId) {
        memberTabData = [];
        removeUserData = [];
        if(programId) {
            // 获取项目的数据
            $.get("/user/getMembers", {programId: programId}, function (res) {
                if(res.code == 200) {
                    memberTabData = res.data;
                    table.reload('memberTable',{data : memberTabData});
                    // 处理 userData，removeUserData
                    removeUserData = intersection(userData, memberTabData, 'userId');
                    userData = difference(userData, memberTabData, 'userId');
                }
            });
        }

        //渲染表格
        table.render({
            elem: '#memberTable',
            data: memberTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                , statusCode: 200 //规定成功的状态码，默认：0
                , msgName: 'msg' //规定状态信息的字段名称，默认：msg
                , countName: 'count' //规定数据总数的字段名称，默认：count
                , dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {type: 'numbers', title: '序号'},
                {field: 'name', title: '姓名'},
                {
                    templet: function (d) {
                        return d.role == "1"? "项目经理":"普通成员";
                    }, title: '角色'
                },
                {field: 'ratio', title: '占比'},
                {align: 'center', toolbar: '#tableMemberBar', title: '操作', minWidth: 200}
            ]],
            done: function () {
                if(isHideCol) {
                    $('#memberTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });

        // 新增按钮点击事件
        $('#addMemberButton').click(function () {
            showMemberForm();
        });

        // 工具条点击事件
        table.on('tool(memberTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doMemberDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showMemberForm(data);
            }
        });
    }

    // 项目成员表删除
    function doMemberDelete(obj) {
        var id = obj.data.userId;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理memberTabData, userData, removeUserData;
            var tmpMemberData = [];
            for(var i=0; i<memberTabData.length; i++) {
                if(id != memberTabData[i].userId) {
                    tmpMemberData.push(memberTabData[i]);
                }
            }
            memberTabData = tmpMemberData;
            // table.reload('memberTable',{data : memberTabData});

            var tempRmData = [];
            for(var i=0; i<removeUserData.length; i++) {
                if(id != removeUserData[i].id) {
                    tempRmData.push(removeUserData[i]);
                } else {
                    userData.push(removeUserData[i]);
                }
            }
            removeUserData = tempRmData;

            layer.msg("删除成功");
            obj.del();
        });
    }

    // 项目成员弹框
    function showMemberForm(data) {
        var index = layer.open({
            type: 1,
            area: ['360px','320px'],
            offset: '65px',
            title: data ? '修改项目成员' : '新增项目成员',
            content: $('#memberForm').html(),
            success: function () {
                // 渲染项目成员
                if(data) {
                    var tmp = [{id: data.userId, name: data.name}].concat(userData);
                    arraySort(tmp);
                    initSelect("name", tmp, "");
                } else {
                    arraySort(userData);
                    initSelect("name", userData, "");
                }

                if(data) {
                    form.val('memberForm', data);
                    $("#name").val(data.userId);
                }
                form.render();
                // 表单提交事件
                form.on('submit(memberFormSubmit)', function (d) {
                    layer.load(2);
                    if(data) {
                        layer.closeAll("loading");
                        // 实现思路，将form的数据认为是新的数据，直接进行新增，对表格的数据，重新更新memberTabData
                        var tmpMemberData = [];
                        var rowData_ = {};
                        rowData_.userId = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.role = d.field.role;
                        rowData_.ratio = d.field.ratio;

                        for(var i=0; i<memberTabData.length; i++) {
                            if(data.userId == memberTabData[i].userId) {
                                tmpMemberData.push(rowData_);
                            } else {
                                tmpMemberData.push(memberTabData[i]);
                            }
                        }
                        memberTabData = tmpMemberData;
                        table.reload('memberTable',{data : memberTabData});

                        var tempData = [];
                        for(var i=0; i<userData.length; i++) {
                            if(rowData_.userId != userData[i].id) {
                                tempData.push(userData[i]);
                            } else {
                                removeUserData.push(userData[i]);
                            }
                        }
                        userData = tempData;

                        var tempRmData = [];
                        for(var i=0; i<removeUserData.length; i++) {
                            if(data.userId != removeUserData[i].id) {
                                tempRmData.push(removeUserData[i]);
                            } else {
                                userData.push(removeUserData[i]);
                            }
                        }
                        removeUserData = tempRmData;

                        layer.msg("修改成功！", {icon: 1});
                        layer.close(index);
                    } else {
                        layer.closeAll("loading");
                        // 处理memberTabData, userData, removeUserData;
                        var rowData_ = {};
                        rowData_.userId = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.role = d.field.role;
                        rowData_.ratio = d.field.ratio;
                        memberTabData.push(rowData_);
                        table.reload('memberTable',{data : memberTabData});

                        var tempData = [];
                        for(var i=0; i<userData.length; i++) {
                            if(rowData_.userId != userData[i].id) {
                                tempData.push(userData[i]);
                            } else {
                                removeUserData.push(userData[i]);
                            }
                        }
                        userData = tempData;
                        //linkManList = userData;
                        layer.msg("新增成功！", {icon: 1});
                        layer.close(index);
                    }
                    return false;
                });

            }
        });
    }

    function getUserData() {
        // 获取项目的数据
        $.get("/user/getUserData", {}, function (res) {
            if(res.code == 200) {
                userData = res.data;
            }
        });
    }

    function arraySort(arr) {
        arr.sort(function(a,b){
            if(a.id>b.id) return 1 ;
            if(a.id<b.id) return -1 ;
            return 0 ;
        });
    }
// ==========================================项目成员处理 end=================================================

// ==========================================项目排期处理 start===============================================
    function showSchedulingTab(programId) {
        schedulingTabData = [];
        if(programId) {
            // 获取项目排期的数据
            $.get("/consultingprogramscheduling/getConsultingProgramScheduling", {programId: programId}, function (res) {
                if(res.code == 200) {
                    schedulingTabData = res.data;
                    table.reload('schedulingTable',{data : schedulingTabData});
                }
            });
        }

        //渲染表格
        table.render({
            elem: '#schedulingTable',
            data: schedulingTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                , statusCode: 200 //规定成功的状态码，默认：0
                , msgName: 'msg' //规定状态信息的字段名称，默认：msg
                , countName: 'count' //规定数据总数的字段名称，默认：count
                , dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {type: 'numbers', title: '序号'},
                {field: 'name', title: '项目阶段'},
                {
                    templet: function (d) {
                        return stateObj[d.state];
                    }, title: '状态'
                },
                {
                    templet: function (res) {
                        return res.starttime.split(" ")[0];
                    }, title: '开始日期'
                },
                {
                    templet: function (res) {
                        return res.finishtime.split(" ")[0];
                    }, title: '结束日期'},
                {align: 'center', toolbar: '#tableSchedulingBar', title: '操作', minWidth: 200}
            ]],
            done: function () {
                if(isHideCol) {
                    $('#schedulingTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });

        // 新增按钮点击事件
        $('#addSchedulingButton').click(function () {
            showSchedulingForm();
        });

        // 工具条点击事件
        table.on('tool(schedulingTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doSchedulingDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showSchedulingForm(data);
            }
        });
    }

    function showSchedulingForm(data) {
        var index = layer.open({
            type: 1,
            area: ['360px'],
            offset: '65px',
            title: data ? '修改项目排期' : '新增项目排期',
            content: $('#schedulingForm').html(),
            success: function () {
                if(data) {
                    // 处理下时间数据
                    data.starttime = data.starttime == undefined ? '' : data.starttime.split(" ")[0];
                    data.finishtime = data.finishtime == undefined ? '' : data.finishtime.split(" ")[0];
                }
                if(data) {
                    form.val('schedulingForm', data);
                }
                // 初始化时间控件
                laydate.render({
                    elem: '#starttimy'//指定元素
                });
                laydate.render({
                    elem: '#finishtimy' //指定元素
                });
                form.render();
                // 表单提交事件
                form.on('submit(schedulingFormSubmit)', function (d) {
                    layer.load(2);
                    var rowData_ = {};
                    if(data){
                        // 处理schedulingTabData;
                        rowData_.id = data.id;
                        rowData_.name = d.field.name;
                        rowData_.state = d.field.state;
                        rowData_.starttime = d.field.starttime;
                        rowData_.finishtime = d.field.finishtime;
                        var tmpTableData = schedulingTabData;
                        var oldData = new Array();
                        for (var i = 0; i < tmpTableData.length; i++) {
                            if (tmpTableData[i].id == rowData_.id) {
                                oldData.push(rowData_);
                            } else {
                                oldData.push(tmpTableData[i]);
                            }
                        }
                        schedulingTabData = oldData;
                        layer.msg("修改成功！", {icon: 1});
                    }
                    else {
                        // 处理schedulingTabData;
                        rowData_.id = d.field.id;
                        rowData_.name = d.field.name;
                        rowData_.state = d.field.state;
                        rowData_.starttime = d.field.starttime;
                        rowData_.finishtime = d.field.finishtime;
                        if (rowData_.id == undefined || rowData_.id == "") {
                            rowData_.id = uuid();
                            schedulingTabData.push(rowData_);
                        } else {
                            var tmpTableData = schedulingTabData;
                            var oldData = new Array();
                            for (var i = 0; i < tmpTableData.length; i++) {
                                if (tmpTableData[i].id == rowData_.id) {
                                    oldData.push(rowData_);
                                } else {
                                    oldData.push(tmpTableData[i]);
                                }
                            }
                            schedulingTabData = oldData;
                        }
                        layer.msg("新增成功！", {icon: 1});
                    }
                    table.reload('schedulingTable',{data: schedulingTabData});
                    layer.close(index);
                    return false;
                });

            }
        });
    }

    function doSchedulingDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理schedulingTabData
            var tmpSchedulingData = [];
            for(var i=0; i<schedulingTabData.length; i++) {
                if(id != schedulingTabData[i].id) {
                    tmpSchedulingData.push(schedulingTabData[i]);
                }
            }
            schedulingTabData = tmpSchedulingData;

            layer.msg("删除成功");
            obj.del();
        });
    }

    function uuid() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
// ==========================================项目排期处理 end=============================================

// ==========================================附件上传 start===============================================
    function loadFileUpload() {
        //多文件列表示例
        var demoListView = $('#demoList')
            ,uploadListIns = upload.render({
            elem: '#testList'
            ,url: '/file/upload/'
            ,accept: 'file'
            ,size: 10240
            ,multiple: true
            ,number: 5
            ,auto: false
            ,bindAction: '#testListAction'
            ,before: function() {
                this.data = {"id": programIdVal, "type": 10}
            }
            ,choose: function(obj){
                var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                //读取本地文件
                obj.preview(function(index, file, result){
                    var tr = $(['<tr id="upload-'+ index +'">'
                        ,'<td>'+ file.name +'</td>'
                        , '<td>等待上传</td>'
                        ,'<td>'
                        ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
                        ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
                        ,'</td>'
                        ,'</tr>'].join(''));

                    //单个重传
                    tr.find('.demo-reload').on('click', function(){
                        obj.upload(index, file);
                        return false;
                    });

                    //删除
                    tr.find('.demo-delete').on('click', function(){
                        layer.confirm('确定要删除吗？', {
                            offset: '65px',
                            title: '提示'
                        }, function (i) {
                            layer.close(i);
                            delete files[index]; //删除对应的文件
                            tr.remove();
                            uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                        });
                    });

                    demoListView.append(tr);
                });
            }
            ,done: function(res, index, upload){
                if(res.code == 200){ //上传成功
                    var fileId = res.data; //附件id
                    fileIds.push(JSON.parse(fileId).id);
                    console.log(fileIds);
                    var tr = demoListView.find('tr#upload-'+ index)
                        ,tds = tr.children();
                    tds.eq(2).html(''); //清空操作
                    return delete this.files[index]; //删除文件队列已经上传成功的文件
                }
                this.error(index, upload);
            }
            ,error: function(index, upload){
                var tr = demoListView.find('tr#upload-'+ index)
                    ,tds = tr.children();
                tds.eq(2).find('.demo-reload').removeClass('layui-hide'); //显示重传
            }
        });
    }

    function initFileTab() {
        $.ajax({
            url: '/file/fileList?type=10&correlatorId=' + programIdVal,
            type:"get"
            ,async:false
            ,dataType:"json"
            , success: function(res){
                if(res.code == 200) {
                    var ds = res.data;
                    if(ds.length > 0) {
                        for (var i = 0; i < ds.length; i++) {
                            (function (d) {
                                var tr = $(['<tr id="' + d.fileId + '" name="' + d.fileUrl + '">'
                                    , '<td>' + d.fileName + '</td>'
                                    , '<td>已上传</td>'
                                    , '<td>'
                                    , '<button class="layui-btn layui-btn-xs layui-btn-danger data-delete">删除</button>'
                                    , '<button class="layui-btn layui-btn-xs data-download">下载</button>'
                                    , '</td>'
                                    , '</tr>'].join(''));
                                //删除
                                tr.find('.data-delete').on('click', function (param) {
                                    layer.confirm('确定要删除吗？', {
                                        offset: '65px',
                                        title: '提示'
                                    }, function (i) {
                                        layer.close(i);
                                        var fileId = tr.attr("id");
                                        var fileUrl = tr.attr("name");
                                        var tds = tr.children();
                                        var fileName = tds.eq(0).html();
                                        // 删除文件库中的文件
                                        $.post("/file/deleleFile", {
                                            fileId: fileId,
                                            fileUrl: fileUrl.substring(0, fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)),
                                            type: 10
                                        }, function (res) {
                                            if (res.code == 200) {
                                                layer.msg("删除成功！");
                                                tr.remove();
                                            } else {
                                                layer.msg(res.msg);
                                            }
                                        });
                                    });
                                    return false;
                                });

                                // 下载
                                tr.find('.data-download').on('click', function () {
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    var url = '/file/download';
                                    var option = {
                                        data: {fileId: fileId,
                                            fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)), fileName: fileName}
                                    }
                                    $.get("/file/isExistFile", {fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName))}, function (res) {
                                        if(res.code == 200) {
                                            download(url, option);
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    });
                                    return false;
                                });
                                $('#demoList').append(tr);
                            })(ds[i]);
                        }
                    }
                }

            }
        });
    }
// ==========================================附件上传 end===============================================
});