layui.use(['layer', 'element', 'form', 'table', 'formSelects', 'util', 'formSelects', 'laydate', 'upload', 'admin'], function () {
    var $ = layui.jquery,
        element = layui.element, //Tab的切换功能，切换事件监听等，需要依赖element模块
        layer = layui.layer,
        form = layui.form,
        table = layui.table,
        util = layui.util,
        formSelects = layui.formSelects,
        laydate = layui.laydate,
        admin = layui.admin,
        upload = layui.upload;

    // 定义“标签”，“客户”，“供应商”全局变量
    var tagData, customsData, memberTabData = [], userData, supplierData, supplierTabData = [], removeUserData=[],
        removeSupplierData=[], isHideCol = false;;
    // 定义“排期项目”全局变量
    var projectScheduleData = new Array();
    var projectScheduleList=[];
    // 定义“排期项目”Table临时data对象
    var tableResultData = [], tempVoList = [];
    // 定义全局变量“客户名称”
    var linkmanNameTemp;
    // 定义 “关联标签”全局变量
    var CorrTagTem = [];
    // 定义 “标签切换”标识
    var tabFlag=0;
    // 定义 “供应商状态URL”，“内部流程URL”，“内部流程ID” 全局变量
    var consuleIdURL, internalURL, consuleIdTemp, internalProIdTemp;
    // 是否新增表示
    var btnFlag;
    var fileIds;  //上传的附件id集合

// =============================================================== 首页列表逻辑js处理 Start ===============================================================//
    //渲染表格
    table.render({
        elem: '#consultingTable',
        url: '/consulting/getlist',
        method: 'post',
        page: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers', title: '序号'},
            {field: 'name', title: '项目名称'},
            {
                templet: function (res) {
                    if(res.type == 1){
                        return '年度项目';
                    }else if(res.type == 2){
                        return '单个项目';
                    }else if(res.type == 3){
                        return '系列项目';
                    }else{
                        return res.type;
                    }

                }, title: '项目类型'
            },
            {
                templet: function (res) {
                    if(res.serviceType == 1){
                        return '集团内业务';
                    }else if(res.serviceType == 2){
                        return '集团外业务';
                    }else{
                        return res.serviceType;
                    }

                }, title: '项目类型'
            },
            {
                templet: function (d) {
                    var str = '';
                    if(d.projectMemberVos != undefined) {
                        for (var i = 0; i < d.projectMemberVos.length; i++) {
                            str += ('<span class="layui-badge-rim">' + d.projectMemberVos[i].name + '</span>');
                        }
                    }
                    return str;
                }, title: '项目成员'
            },
            {
                templet: function (res) {
                    if(res.state == 0){
                        return '未开始';
                    }else if(res.state == 1){
                        return '正常开展';
                    }else if(res.state == 2){
                        return '已完成';
                    }else if(res.state == 3){
                        return '延期';
                    }else if(res.state == 4){
                        return '中止';
                    }else if(res.state == 5){
                        return '其他';
                    }
                    return res.createDate;
                }, title: '项目状态'
            },
            {
                templet: function (res) {
                    return util.toDateString(res.createDate, "yyyy-MM-dd");
                }, title: '创建时间'
            },
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 200}
        ]],
        response: {
            statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
        },
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
            return {
                "code": res.code, //解析接口状态
                "msg": res.msg, //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.data //解析数据列表
            };
        },
        done: function () {
            layer.closeAll('loading');
        }
    });

    // ==================================== 公共方法 Start ====================================
    // 临时表ID
    var uuid = function () {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }

    // 初始化 项目类型，业务类型， 服务类型
    initListType();

    function initListType(){
        // 初始化 业务流程 项目类型
        var projectType = [{
            id: '1',
            name: '年度项目'
        },{
            id: '2',
            name: '单个项目'
        },{
            id: '3',
            name: '系列项目'
        }];

        // 初始 业务流程 化业务类型
        var businessType = [{
            id: '1',
            name: '集团内业务'
        },{
            id: '2',
            name: '集团外业务'
        }];

        initSelect('projectType', projectType, "项目类型");
        initSelect('businessType', businessType, "业务类型");
        form.render();
    }

    function initType(projectnameTemp, businessnameTemp){
        // 初始化 业务流程 项目类型
        var projectType = [{
            id: '1',
            name: '年度项目'
        },{
            id: '2',
            name: '单个项目'
        },{
            id: '3',
            name: '系列项目'
        }];

        // 初始 业务流程 化业务类型
        var businessType = [{
            id: '1',
            name: '集团内业务'
        },{
            id: '2',
            name: '集团外业务'
        }];

        // 初始 业务流程 化服务类型
        var serviceType = [{
            id: '1',
            name: '笔试'
        },{
            id: '2',
            name: '面试'
        },{
            id: '3',
            name: '无领导小组讨论'
        },{
            id: '4',
            name: '在线测评'
        }];

        // 初始化 业务流程 “项目”状态
        var projectState = [{
            id: 0,
            name: '未开始'
        },{
            id: 1,
            name: '正常开展'
        },{
            id: 2,
            name: '已完成'
        },{
            id: 3,
            name: '延期'
        },{
            id: 4,
            name: '中止'
        },{
            id: 5,
            name: '其他'
        }];

        // 初始化 内部流程 == “状态”
        var customState = [{
            id: 0,
            name: '启动'
        },{
            id: 1,
            name: '招采'
        },{
            id: 2,
            name: '合同签订'
        },{
            id: 3,
            name: '开票'
        },{
            id: 4,
            name: '费用报销'
        },{
            id: 5,
            name: '归档'
        }];

        initSelect(projectnameTemp, projectType, "项目类型");
        initSelect(businessnameTemp, businessType, "业务类型");
        initSelect("serviceType", serviceType, "服务类型");
        initSelect("projectState", projectState, "项目状态");
        initSelect("customState", customState, "客户状态");
        form.render();
    }

    // 项目排期 状态
    function projectSchState(){
        // 项目排期 状态
        var projectScheduleState = [{
            id: 0,
            name: '未开始'
        },{
            id: 1,
            name: '进行中'
        },{
            id: 2,
            name: '已结束'
        },{
            id: 3,
            name: '延期'
        },{
            id: 4,
            name: '中止'
        },{
            id: 5,
            name: '其他'
        }];

        initSelect('projectScheduleState', projectScheduleState, "状态");
        form.render();
    }

    // 初始化“业务流程-附件上传”方法
    function initUpload(){
        loadConsuleFileUpload();
    }

    // 切换标签，内部流程“附件上传”
    function initInternalUpload(){
        loadInsteralFileUpload();
    }

    // 获取供应商和标签的数据，并初始化对应的选择控件
    // 获取所有角色
    layer.load(2);
    $.get("/consulting/getinitdata", {}, function (res) {
        layer.closeAll('loading');
        if(res.code == 200) {
            tagsData = res.data.tags;
            tagData = tagsData;
            customsData = res.data.customs;
        } else {
            layer.msg('获取初始化数据失败');
        }
    });
    // ==================================== 公共方法 End ====================================


    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        var consultingName = $('#consultingName').val();
        var projectType = $('#projectType').val();
        var businessType = $('#businessType').val();
        table.reload('consultingTable', {where: {name: consultingName, type: projectType, businessType: businessType}});
    });

    // 工具条点击事件
    table.on('tool(consultingTable)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        if (layEvent === 'del') { // 删除
            doDelete(obj);
        } else if (layEvent === 'detail') { // 查看
            btnFlag = 'detail';
            showEditModel(data, 'detail');
        } else if (layEvent === 'edit') { // 修改
            btnFlag = 'edit';
            showEditModel(data);
        }
    });

    // 删除
    function doDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);
           /* var url = '/consulting/deleteByID';
            apiCurrentPageDel(id, 'POST', url, callbackCloseCurrentPage, 'consultingTable', 'delete');
            return false;*/
           $.post("/consulting/deleteByID", {id: id}, function (res){
                 layer.closeAll('loading');
                 if(res.code == 200){
                     layer.msg("删除成功", {icon: 1});
                     obj.del();
                     table.reload('consultingTable');
                 }else{
                     layer.msg( res.msg, {icon: 1});
                 }
             });
        });
    }
    // 添加新增咨询点击事件
    $('#consultingAdd').click(function () {
        showEditModel();
    });
    // 添加新增供应商点击事件
    $(document).on('click', '#addSupplierButton', function () {
        showEditSupplierSateModel();
    });
// =============================================================== 首页列表逻辑js处理 Start ===============================================================//


// =============================================================== “业务流程”逻辑处理 Start ===============================================================//
    // 弹出“业务流程”新增与编辑窗口
    function showEditModel(data, showType) {
        fileIds = new Array();
        layer.open({
            type: 1,
            area: ['60%', '100%'],
            // offset: '65px',
            title: showType? '查看业务信息' : data ? '修改业务信息' : '添加业务信息',
            content: $('#consultingForm').html(),
            success: function (layero, index) {
// ======================== 新增逻辑处理 Start ========================
                // 初始化 项目类型，业务类型， 服务类型
                initType('projectTypeCond', 'businessTypeCond');
                // 初始化“业务流程-附件上传”方法
                initUpload();

                // tab切换标签触发函数
                element.on('tab(docDemoTabBrief)', function(data){
                    if(data.index == 0){
                        // 业务流程
                        tabFlag = 0;
                        // 保存切换变编辑
                        if(consuleIdTemp){
                            consuleIdURL = '/consulting/update';
                        }
                    }else{
                        tabFlag = 1;
                        // 获取 内部流程“供应商状态”Table下拉框数据
                        getSupplierData();
                        // 切换标签，内部流程“附件上传”
                        initInternalUpload();
                        if(consuleIdTemp){
                            if(btnFlag == 'add'){
                                // 如果内部流程 新增， 更改URL create
                                internalURL = '/internalprogram/create';
                                internalProIdTemp='';
                                // 内部流程业务处理--“供应商状态”Table
                                showEditIntrenalModel();
                            }else if(btnFlag == 'edit' || btnFlag == 'detail'){

                                // 如果内部流程 编辑， 更改URL update
                                internalURL = '/internalprogram/update';
                                // 内部流程编辑获取数据
                                getInternalProgramData(consuleIdTemp);
                            }
                        }
                    }
                    //layui.form.render();
                });
                // 渲染“标签”下拉框
                var tagSelData = new Array();
                for (var i = 0; i < tagData.length; i++) {
                    tagSelData.push({name: tagData[i].name, value: tagData[i].id});
                }
                formSelects.data('selTag', 'local', {arr: tagSelData});
                // 渲染“客户”下拉框
                initSelect("customId", customsData, "客户");
                form.render();
                // 初始化时间控件
                laydate.render({
                    elem: '#starttime' //指定元素
                    ,done: function(value, date, endDate){
                        var eDate = $("#finishtime").val();
                        if(eDate!="" && compareDate(value, eDate)) {
                            $("#finishtime").val(value)
                        }
                    }
                });
                laydate.render({
                    elem: '#finishtime' //指定元素`
                    ,done: function(value, date, endDate){
                        var sDate = $("#starttime").val();
                        if(endDate!="" && compareDate(sDate, value)) {
                            $("#starttime").val(value)
                        }
                    }
                });
                // 查询当前业务的项目成员
                getUserData();
                // 渲染下载模板（只有编辑和查看需要显示下载按钮）
                showModuleTab();
// ======================== 编辑逻辑处理 Start ========================
                if (data){

                    isHideCol = false;
                    if(showType) {
                        isHideCol = true;
                    }

                    btnFlag == 'edit';
                    consuleIdTemp = data.id;
                    changeInternalShow(data);
                    consuleIdURL = '/consulting/update';
                    // 编辑“标签”
                    searchCorrTag(data.id);
                    // 编辑“客户”
                    if(customsData.length > 0){
                        for(var i=0; i<customsData.length;i++){
                            if(data.customId == customsData[i].id){
                                linkmanNameTemp = customsData[i].name;
                                break;
                            }
                        }
                        $.get("/linkman/getLinkMans", {customId: data.customId}, function (res) {
                            if(res.code == 200) {
                                showCustomTab(res.data, linkmanNameTemp)
                            }
                        });
                    }
                    form.val('consultingForm', data);
                    $("#projectTypeCond").val(data.type);
                    $("#businessTypeCond").val(data.businessType);
                    $("#serviceType").val(data.serviceType);
                    $("#projectState").val(data.state);
                    // 编辑状态获取 业务流程 已经上传的文件
                    conssultingEditFiles(consuleIdTemp);

                    //editLinkmans(data.id);
                    // 编辑“项目成员”Table
                    showMemberTab(data.id);
                    projectScheduleInit(data.id);
                }else{
                    projectScheduleData = [];
                    consuleIdTemp = '';
                    btnFlag = 'add';
                    changeInternalShow();
                    consuleIdURL = '/consulting/create';
                    // 初始化“项目成员”Table
                    showMemberTab();
                    // 初始化“项目排期”Table
                    projectScheduleInit();
                    // 初始化“筹备阶段文件模板”Table
                    $('#moduleList').find('.data-download1').addClass('layui-hide');
                }
                form.render();

                // 控制form表单是否可以编辑
                if(showType) {
                    $('form').find('input,select,textarea').attr('disabled', true);
                    $("#testList").css('display', 'none');
                    $("#closeDialog").css('display', 'none');
                    $("#consultingFormSubmit").css('display', 'none');
                    $('#testList').find('.data-delete').addClass('layui-hide');


                    $("#testInternalList").css('display', 'none');
                    $("#closeDialog1").css('display', 'none');
                    $("#internalFormSubmit").css('display', 'none');
                    $('#testInternalList').find('.data-delete').addClass('layui-hide');

                    formSelects.disabled();

                    // 项目排期
                    $("#addMemberButton").css('display', 'none');
                    // 项目成员
                    $("#projectScheduleAddBtn").css('display', 'none');
                    // 供应商状态
                    $("#addSupplierButton").css('display', 'none');
                }
                form.render();

                // “业务流程”表单提交处理
                form.on('submit(consultingFormSubmit)', function (d){
                    var data_row = d.field;

                    // 判断当前的项目成员是否为空,
                    if(memberTabData.length == 0) {
                        layer.msg("项目成员数据需要维护！", {icon: 2});
                        return false;
                    }

                    layer.load(2);

                    // 处理“项目排期”数据
                    for(var i=0; i<projectScheduleList.length;i++){
                        if(projectScheduleList[i].id.length < 32){
                            projectScheduleList[i].id = '';
                        }
                    }
                    var correlatorTagList = [];
                    if(data_row.tagIdCond){

                        var temP = {
                            tagId: data_row.tagIdCond
                        }
                        correlatorTagList.push(temP);
                    }

                   // var reg =  data_row.expectedRevenue.replace(/,/g,'');
                    var expectedRevenueTemp = data_row.expectedRevenue.replace(/,/g,'');
                    var projectedCostTemp = data_row.projectedCost.replace(/,/g,'');
                    //  预计营收
                   // var expectedRevenueTemp = data_row.expectedRevenue.replace(/[\ |\~|\`|\!|\@|\#|\$|\%|\^|\&|\*|\(|\)|\-|\_|\+|\=|\||\\|\[|\]|\{|\}|\;|\:|\"|\'|\,|\<|\.|\>|\/|\?]/g,"");
                    // 预计成本
                   // var projectedCostTemp = data_row.projectedCost.replace(/[\ |\~|\`|\!|\@|\#|\$|\%|\^|\&|\*|\(|\)|\-|\_|\+|\=|\||\\|\[|\]|\{|\}|\;|\:|\"|\'|\,|\<|\.|\>|\/|\?]/g,"");

                    var param = {
                        // id
                        id: consuleIdTemp,
                        // 项目名称
                        name: data_row.name,
                        // 项目类型
                        type: data_row.projectTypeCond,
                        // 业务类型
                        businessType: data_row.businessTypeCond,
                        // 服务类型
                        serviceType: data_row.serviceType,
                        // 标签
                        correlatorTagList: correlatorTagList,
                        // 预计营收
                        expectedRevenue: expectedRevenueTemp,
                        // 预计成本
                        projectedCost: projectedCostTemp,
                        // 启动时间
                        starttime: data_row.starttime,
                        // 完成时间
                        finishtime: data_row.finishtime,
                        // 招聘岗位数
                        positionNumber: data_row.positionNumber,
                        // 服务人次
                        serviceTimes: data_row.serviceTimes,
                        // 客户Id
                        customId: data_row.customId,
                        // 项目总结
                        summary: data_row.summary,
                        // 项目评分
                        grade: data_row.grade,
                        // 项目状态
                        state: data_row.projectState,
                        // 项目排期
                        programSchedulingList: projectScheduleList,
                        // 项目成员
                        projectMemberList: memberTabData
                    }
                    $.ajax({
                        type: 'POST',
                        url: consuleIdURL,//发送请求
                        contentType: "application/json; charset=utf-8",
                        async: true,
                        data: JSON.stringify(param),
                        dataType: "json",
                        success: function (res) {
                            layer.closeAll("loading");
                            if(res.code == 200){
                                consuleIdTemp = res.data;

                                consuleIdURL = '/consulting/update';
                                layer.msg("添加成功！", {icon: 1});
                                changeInternalShow(consuleIdTemp);
                                $("#testListAction").trigger("click");
                                //layer.closeAll('page');
                                table.reload('consultingTable');
                                //programIdVal = res.data;
                                // $("#testListAction").trigger("click");
                            }else{
                                layer.msg(res.msg, {icon: 2});
                            }
                        }
                    });
                    return false;
                });
            }
        });
    }

    // 获取关联Tag数据
    function searchCorrTag(corrId){
        layer.load(2);
        $.get("/base/custom/getCorrTag", {correlatorId: corrId}, function (res) {
            layer.closeAll('loading');
            if(res.code == 200) {
                corrtagsData = res.data;
                CorrTagTem = corrtagsData;
                editTagInit();
            } else {
                layer.msg('获取初始化数据失败');
            }
        });
    }

    function editTagInit(){
        if(CorrTagTem.length > 0){
            var CorrTagItems = [];
            for(var i=0; i<CorrTagTem.length; i++){
                CorrTagItems.push(CorrTagTem[i].tagId);
            }
            formSelects.value('selTag', CorrTagItems);  // 回显多选框
        }
    }

    // ==========================================筹备阶段文件处理 start===============================================
    function showModuleTab() {
        $.ajax({
            url: '/file/fileList?type=4&correlatorId=' + "",
            type:"get"
            ,async:false
            ,dataType:"json"
            , success: function(res){
                if(res.code == 200) {
                    var data = res.data;
                    // 显示请求的服务端数据
                    if(data.length > 0) {
                        for(var i=0; i<data.length; i++) {
                            (function (d) {
                                var tr = $(['<tr id="'+ d.fileId +'" name="' + d.fileUrl + '">'
                                    ,'<td>'+ d.fileName +'</td>'
                                    ,'<td>'
                                    ,'<button class="layui-btn layui-btn-xs data-download1">下载</button>'
                                    ,'</td>'
                                    ,'</tr>'].join(''));

                                // 下载
                                tr.find('.data-download1').on('click', function(){
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    var url = '/file/download';
                                    var option = {
                                        data: {fileId: fileId,
                                            fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)), fileName: fileName}
                                    }
                                    $.get("/file/isExistFile", {fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName))}, function (res) {
                                        if(res.code == 200) {
                                            download(url, option);
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    });
                                    return false;
                                });
                                $('#moduleList').append(tr);
                            })(data[i]);
                        }
                    }
                }
            }
        });
    }
// ==========================================筹备阶段文件处理 end===============================================

    // 渲染 业务流程 “项目成员”Table
    function showMemberTab(programId) {
        memberTabData = [];
        removeUserData = [];
        if(programId) {
            // 获取项目的数据
            $.get("/user/getMembers", {programId: programId}, function (res) {
                if(res.code == 200) {
                    memberTabData = res.data;
                    table.reload('memberTable',{data : memberTabData});
                    // 处理 userData，removeUserData
                    removeUserData = intersection(userData, memberTabData, 'userId');
                    userData = difference(userData, memberTabData, 'userId');
                }
            });
        }

        // 渲染 业务流程 “项目成员” Table
        table.render({
            elem: '#memberTable',
            data: memberTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            cols: [[
                {field: 'name', title: '姓名'},
                {
                    templet: function (d) {
                        return d.role == "1"? "项目经理":"普通成员";
                    }, title: '角色'
                },
                {field: 'ratio', title: '占比'},
                {align: 'center', toolbar: '#tableMemberBar', title: '操作', minWidth: 200}
            ]],
            response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            },
            parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.data //解析数据列表
                };
            },
            done: function () {
                if(isHideCol) {
                    $('#memberTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });

        // 添加 业务流程 “项目成员”按钮点击事件
        $('#addMemberButton').click(function () {
            showMemberForm();
        });

        // 业务流程 “项目成员” 工具 删除 编辑事件
        table.on('tool(memberTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doMemberDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showMemberForm(data);
            }
        });
    }


    // 业务流程 “项目成员”表删除
    function doMemberDelete(obj) {
        var id = obj.data.userId;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理memberTabData, userData, removeUserData;
            var tmpMemberData = [];
            for(var i=0; i<memberTabData.length; i++) {
                if(id != memberTabData[i].userId) {
                    tmpMemberData.push(memberTabData[i]);
                }
            }
            memberTabData = tmpMemberData;
            // table.reload('memberTable',{data : memberTabData});

            var tempRmData = [];
            for(var i=0; i<removeUserData.length; i++) {
                if(id != removeUserData[i].id) {
                    tempRmData.push(removeUserData[i]);
                } else {
                    userData.push(removeUserData[i]);
                }
            }
            removeUserData = tempRmData;

            layer.msg("删除成功");
            obj.del();
        });
    }

    // 业务流程 “项目成员”弹框
    function showMemberForm(data) {
        var index = layer.open({
            type: 1,
            area: ['360px','320px'],
            offset: '65px',
            title: data ? '修改项目成员' : '添加项目成员',
            content: $('#memberForm').html(),
            success: function () {
                // 渲染项目成员
                if(data) {
                    var tmp = [{id: data.userId, name: data.name}].concat(userData);
                    arraySort(tmp);
                    initSelect("name", tmp, "");
                } else {
                    arraySort(userData);
                    initSelect("name", userData, "");
                }

                if(data) {
                    form.val('memberForm', data);
                    $("#name").val(data.userId);
                }
                form.render();
                // 表单提交事件
                form.on('submit(memberFormSubmit)', function (d) {
                    layer.load(2);
                    if(data) {
                        layer.closeAll("loading");
                        // 实现思路，将form的数据认为是新的数据，直接进行添加，对表格的数据，重新更新memberTabData
                        var tmpMemberData = [];
                        var rowData_ = {};
                        rowData_.userId = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.role = d.field.role;
                        rowData_.ratio = d.field.ratio;

                        for(var i=0; i<memberTabData.length; i++) {
                            if(data.userId == memberTabData[i].userId) {
                                tmpMemberData.push(rowData_);
                            } else {
                                tmpMemberData.push(memberTabData[i]);
                            }
                        }
                        memberTabData = tmpMemberData;
                        table.reload('memberTable',{data : memberTabData});

                        var tempData = [];
                        for(var i=0; i<userData.length; i++) {
                            if(rowData_.userId != userData[i].id) {
                                tempData.push(userData[i]);
                            } else {
                                removeUserData.push(userData[i]);
                            }
                        }
                        userData = tempData;

                        var tempRmData = [];
                        for(var i=0; i<removeUserData.length; i++) {
                            if(data.userId != removeUserData[i].id) {
                                tempRmData.push(removeUserData[i]);
                            } else {
                                userData.push(removeUserData[i]);
                            }
                        }
                        removeUserData = tempRmData;

                        layer.msg("修改成功！", {icon: 1});
                        layer.close(index);
                    } else {
                        layer.closeAll("loading");
                        // 处理memberTabData, userData, removeUserData;
                        var rowData_ = {};
                        rowData_.userId = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.role = d.field.role;
                        rowData_.ratio = d.field.ratio;
                        memberTabData.push(rowData_);
                        table.reload('memberTable',{data : memberTabData});

                        var tempData = [];
                        for(var i=0; i<userData.length; i++) {
                            if(rowData_.userId != userData[i].id) {
                                tempData.push(userData[i]);
                            } else {
                                removeUserData.push(userData[i]);
                            }
                        }
                        userData = tempData;

                        layer.msg("添加成功！", {icon: 1});
                        layer.close(index);
                    }
                    return false;
                });

            }
        });
    }

    // 渲染 业务流程 “项目排期”Table
    function projectScheduleInit(id){
        tableResultData = [];
        // removeSchemeData = [];
        if(id) {
            // 获取项目的数据
            $.post("/programscheduling/getlist", {pId: id}, function (res) {
                if(res.code == 200) {
                    tableResultData = res.data;
                    projectScheduleData = tableResultData;
                    table.reload('projectScheduleTable',{data : tableResultData});
                }
            });
        }
        // 渲染 业务流程 “项目排期”
        table.render({
            elem: '#projectScheduleTable',
            data: tableResultData,
            method: 'post',
            page: false,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers', title: '序号'},
                {field: 'name', title: '名称'},
                {
                    templet: function (d) {
                        return util.toDateString(d.programDate, "yyyy-MM-dd");
                    }, title: '日期'
                },
                {field: 'programTime', title: '时间'},
                {field: 'programAddress', title: '地点'},
                {
                    templet: function (d) {
                        if(d.state == 0){
                            return '未开始';
                        }else if(d.state == 1){
                            return '进行中';
                        }else if(d.state == 2){
                            return '已结束';
                        }else if(d.state == 3){
                            return '延期';
                        }else if(d.state == 4){
                            return '中止';
                        }else if(d.state == 5){
                            return '其他';
                        }
                    }, title: '状态'
                },
                {align: 'center', toolbar: '#projectScheduleTableBar', title: '操作', minWidth: 200}
            ]],
            response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            },
            parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.data //解析数据列表
                };
            },
            done: function () {
                if(isHideCol) {
                    $('#projectScheduleTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });
        // 业务流程 “项目排期”新增点击事件
        $('#projectScheduleAddBtn').click(function () {
            showEditProjectScheduleModel();
        });
        // 业务流程 “项目排期”工具点击事件
        table.on('tool(projectScheduleTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;
            if (layEvent === 'del') { // 删除
                deleteProjectScheduleModel(obj);
            } else if (layEvent === 'edit') { // 修改
                showEditProjectScheduleModel(data);
            }
        });
    }


    $(document).on('click', '#cancerProjectScheduleOpen', function () {
        var index=parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        layer.close(layer.index);

    });

    // 业务流程 “项目排期” 处理 弹出框
    function showEditProjectScheduleModel(data){
        var index = layer.open({
            type: 1,
            //area: ['30%', '75%'],
            area: ['35%', '54%'],
            offset: '10%',
            resize:false,
            title: data ? '编辑项目排期' : '新增项目排期',
            content: $('#projectScheduleForm').html(),
            success: function (){
                projectSchState();
                //渲染日期控件
                laydate.render({elem: '#programDate', type: 'date'});
                //时间选择器
                laydate.render({elem: '#programTime',type: 'time', format: 'HH:mm'});
                if(data){
                    $("#projectScheduleId").val(data.id);
                    $("#projectScheduleName").val(data.name);
                    data.programDate = data.programDate == undefined ? '' : data.programDate.split(" ")[0];
                    $("#programDate").val(data.programDate);
                    $("#programTime").val(data.programTime);
                    $("#programAddress").val(data.programAddress);
                    $("#projectScheduleState").val(data.state);
                    form.render();
                }

                // 表单提交事件
                form.on('submit(projectScheduleFormSubmit)', function (d) {
                    var oldData = new Array();
                    var rowData_ = {};
                    rowData_.id = $("#projectScheduleId").val();
                    rowData_.name = $("#projectScheduleName").val();
                    rowData_.programDate = $("#programDate").val() == undefined ? '' : $("#programDate").val().split(" ")[0];
                    rowData_.programTime = $("#programTime").val();
                    rowData_.programAddress = $("#programAddress").val();
                    rowData_.state = $("#projectScheduleState").val();

                    if(rowData_.id == undefined || rowData_.id == "") {
                        rowData_.id = uuid();
                        projectScheduleData.push(rowData_);
                    } else {
                        var tmpTableData = projectScheduleData;
                        for(var i=0; i<tmpTableData.length; i++) {
                            if(tmpTableData[i].id == rowData_.id) {
                                oldData.push(rowData_);
                            } else {
                                oldData.push(tmpTableData[i]);
                            }
                        }
                        projectScheduleData = oldData;
                    }
                    projectScheduleList = projectScheduleData;
                    table.reload('projectScheduleTable',{data : projectScheduleData});
                    layer.close(index);
                    return false;
                });

            }
        });
    }
    //  业务流程 删除“项目排期”
    function deleteProjectScheduleModel(obj){
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (index) {
            obj.del();
            var tmp = tableResultData;
            var tmpList = [];
            var tempJson = {};
            for(var i=0; i<tableResultData.length; i++){
                if(tableResultData[i].id != id){
                    tmpList.push(tableResultData[i]);
                }else{
                    tempJson = {
                        tempIds: id,
                        tempBelong: 'projectSchedule'
                    }
                    tempVoList.push(tempJson);
                }
            }
            projectScheduleData = tmpList;
            projectScheduleList = projectScheduleData;
            tableData =  table.cache["relation_table"];
            layer.close(index);
        });
    }

    // 获取 业务流程 “项目成员”数据
    function getUserData() {
        // 获取项目的数据
        $.get("/user/getUserData", {}, function (res) {
            if(res.code == 200) {
                userData = res.data;
            }
        });
    }

    function conssultingEditFiles(obj){
        // 上传附件数据回调
        $.ajax({
            url: '/file/fileList?type=9&correlatorId=' + obj,
            type:"get"
            ,async:false
            ,dataType:"json"
            , success: function(res){
                if(res.code == 200) {
                    var ds = res.data;
                    if(ds.length > 0) {
                        for (var i = 0; i < ds.length; i++) {
                            (function (d) {
                                var tr = $(['<tr id="' + d.fileId + '" name="' + d.fileUrl + '">'
                                    , '<td>' + d.fileName + '</td>'
                                    , '<td>已上传</td>'
                                    , '<td>'
                                    , '<button class="layui-btn layui-btn-xs layui-btn-danger data-delete">删除</button>'
                                    , '<button class="layui-btn layui-btn-xs data-download">下载</button>'
                                    , '</td>'
                                    , '</tr>'].join(''));
                                //删除
                                tr.find('.data-delete').on('click', function (param) {
                                    // delete files[index]; //删除对应的文件
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    // 删除文件库中的文件
                                    $.post("/file/deleleFile", {
                                        fileId: fileId,
                                        fileUrl: fileUrl.substring(0, fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)),
                                        type: 9
                                    }, function (res) {
                                        if (res.code == 200) {
                                            layer.msg("删除成功！");
                                            tr.remove();
                                        } else {
                                            layer.msg(res.msg);
                                        }
                                    });
                                    return false;
                                    // uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                                });

                                // 下载
                                tr.find('.data-download').on('click', function () {
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    var url = '/file/download';
                                    var option = {
                                        data: {fileId: fileId,
                                            fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)), fileName: fileName}
                                    }
                                    $.get("/file/isExistFile", {fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName))}, function (res) {
                                        if(res.code == 200) {
                                            download(url, option);
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    });
                                    return false;
                                });
                                $('#consultingUpdateList').append(tr);
                            })(ds[i]);
                        }
                    }
                }

            }
        });
    }

    // “业务流程”附件上传
    function loadConsuleFileUpload() {
        //多文件列表示例
        var demoListView = $('#consultingUpdateList')
            ,uploadListIns = upload.render({
            elem: '#testList'
            ,url: '/file/upload/'
            ,accept: 'file'
            ,size: 10240
            ,multiple: true
            ,number: 5
            ,auto: false
            ,bindAction: '#testListAction'
            ,before: function() {
                this.data = {"id": consuleIdTemp, "type": 9}
            }
            ,choose: function(obj){
                var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                //读取本地文件
                obj.preview(function(index, file, result){
                    var tr = $(['<tr id="upload-'+ index +'">'
                        ,'<td>'+ file.name +'</td>'
                        , '<td>等待上传</td>'
                        ,'<td>'
                        ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
                        ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
                        ,'</td>'
                        ,'</tr>'].join(''));

                    //单个重传
                    tr.find('.demo-reload').on('click', function(){
                        obj.upload(index, file);
                        return false;
                    });

                    //删除
                    tr.find('.demo-delete').on('click', function(){
                        delete files[index]; //删除对应的文件
                        tr.remove();
                        uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                    });

                    demoListView.append(tr);
                });
            }
            ,done: function(res, index, upload){
                if(res.code == 200){ //上传成功
                    var fileId = res.data; //附件id
                    fileIds.push(JSON.parse(fileId).id);
                    console.log(fileIds);
                    var tr = demoListView.find('tr#upload-'+ index)
                        ,tds = tr.children();
                    tds.eq(2).html(''); //清空操作
                    return delete this.files[index]; //删除文件队列已经上传成功的文件
                }
                this.error(index, upload);
            }
            ,error: function(index, upload){
                var tr = demoListView.find('tr#upload-'+ index)
                    ,tds = tr.children();
                tds.eq(2).find('.demo-reload').removeClass('layui-hide'); //显示重传
            }
        });
    }

// =============================================================== “业务流程”逻辑处理 End ===============================================================//

// =============================================================== “内部流程”逻辑处理 Start ===============================================================//

    $(document).on('click', '#closeSupplierState', function () {
        var index=parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        layer.close(layer.index);

    });
    $(document).on('click', '#closeDialog1', function () {
        var index=parent.layer.getFrameIndex(window.name);
        parent.layer.close(index);
        layer.close(layer.index);

    });


    // 处理切换标签“内部流程样式”
    function changeInternalShow(data){
        var tabId = document.getElementById("showP");
        if(data){
            tabId.style.color = "";
            tabId.style.cursor = "";
            tabId.style.pointerEvents = "";
        }else{
            tabId.style.color = "slategrey";
            tabId.style.cursor = "not-allowed";
            tabId.style.pointerEvents = "none";
        }
    }

    // 内部流程 弹出“供应商状态”新增对话框
    function showEditSupplierSateModel(data){
        layer.open({
            type: 1,
            area: ['360px','320px'],
            offset: '10%',
            resize:false,
            title: data ? '编辑供应商状态信息' : '新增供应商状态信息',
            content: $('#supplierSateForm').html(),
            success: function (layero, index) {
// ======================== 新增逻辑处理 Start ========================
                // 渲染项目成员
                if(data) {
                    // 编辑 内部流程 -- 渲染“供应商状态”下拉框
                    var tmp = [{id: data.id, name: data.name, state: data.state}].concat(supplierData);
                    arraySort(tmp);
                    initSelect("name", tmp, "");
                } else {
                    arraySort(supplierData);
                    initSelect("name", supplierData, "");
                }
// ======================== 编辑逻辑处理 Start ========================
                if (data){
                   // form.val('consultingForm', data);
                    $("#name").val(data.id);
                    $("#state").val(data.state);
                }
                form.render();

                // “供应商状态”表单提交处理
                form.on('submit(supplierSateFormSubmit)', function (d){
                    layer.load(2);
                    if(data) {
                        layer.closeAll("loading");
                        // 实现思路，将form的数据认为是新的数据，直接进行添加，对表格的数据，重新更新memberTabData
                        var tmpMemberData = [];
                        var rowData_ = {};
                        rowData_.id = d.field.name;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.state = d.field.state;

                        for(var i=0; i<supplierTabData.length; i++) {
                            if(data.id == supplierTabData[i].id) {
                                tmpMemberData.push(rowData_);
                            } else {
                                tmpMemberData.push(supplierTabData[i]);
                            }
                        }
                        supplierTabData = tmpMemberData;
                        table.reload('supplierShowTable',{data : supplierTabData});

                        var tempData = [];
                        for(var i=0; i<supplierData.length; i++) {
                            if(rowData_.id != supplierData[i].id) {
                                tempData.push(supplierData[i]);
                            } else {
                                removeSupplierData.push(supplierData[i]);
                            }
                        }
                        supplierData = tempData;

                        var tempRmData = [];
                        for(var i=0; i<removeSupplierData.length; i++) {
                            if(data.id != removeSupplierData[i].id) {
                                tempRmData.push(removeSupplierData[i]);
                            } else {
                                supplierData.push(removeSupplierData[i]);
                            }
                        }
                        removeSupplierData = tempRmData;

                        layer.msg("修改成功！", {icon: 1});
                        layer.close(index);
                    } else {
                        layer.closeAll("loading");
                        // supplierTabData, supplierData, removeSupplierData;
                        var rowData_ = {};
                        rowData_.id = d.field.name;
                        //rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.name = d.form.name[d.form.name.selectedIndex].text;
                        rowData_.state = d.field.state;
                        supplierTabData.push(rowData_);
                        table.reload('supplierShowTable',{data : supplierTabData});

                        var tempData = [];
                        for(var i=0; i<supplierData.length; i++) {
                            if(rowData_.id != supplierData[i].id) {
                                tempData.push(supplierData[i]);
                            } else {
                                removeSupplierData.push(supplierData[i]);
                            }
                        }
                        supplierData = tempData;

                        layer.msg("添加成功！", {icon: 1});
                        layer.close(index);
                    }
                    return false;
                });
            }
        });
    }

    // 内部流程业务处理--“供应商状态”Table
    function showEditIntrenalModel(internalId){
        supplierTabData = [];
        removeSupplierData = [];

        if(internalId) {
            // 获取项目的数据
            $.get("/supplierinternalprogram/getCustomState", {internalId: internalId}, function (res) {
                if(res.code == 200) {
                    supplierTabData = res.data;

                    // 如果是查看功能，则需要隐藏部分的按钮
                    if(isHideCol) {

                        $('#internalUpdateList').find('.data-delete').addClass('layui-hide');

                    }

                    table.reload('supplierShowTable',{data : supplierTabData});
                    // 处理 userData，removeUserData
                    removeSupplierData = intersection(supplierData, supplierTabData, 'id');
                    supplierData = difference(supplierData, supplierTabData, 'id');
                }
            });
        }

        //渲染表格
        table.render({
            elem: '#supplierShowTable',
            data: supplierTabData,
            method: 'get',
            page: true,
            cellMinWidth: 100,
            cols: [[
                {type: 'numbers', title: '序号'},
                {field: 'name', title: '名称'},
                {
                    templet: function (res) {
                        if(res.state == 0){
                            return '启动';
                        }else if(res.state == 1){
                            return '招采';
                        }else if(res.state == 2){
                            return '合同签订';
                        }else if(res.state == 3){
                            return '付款';
                        }else if(res.state == 4){
                            return '归档';
                        }else{
                            return '';
                        }

                    }, title: '状态'
                },
                {align: 'center', toolbar: '#supplierSateTableBar', title: '操作', minWidth: 200}
            ]],
            response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            },
            parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.data //解析数据列表
                };
            },
            done: function () {
                if(isHideCol) {
                    $('#supplierShowTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });
        // 内部流程“供应商状态”工具条点击事件
        table.on('tool(supplierShowTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doSupplierDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showEditSupplierSateModel(data);
            }
        });
    }


    // “内部流程”主表单提交处理
    form.on('submit(internalFormSubmit)', function (d){
        var data_row = d.field;
        var supplierInternalProgramList = [];

      //  replace(/[\ |\~|\`|\!|\@|\#|\$|\%|\^|\&|\*|\(|\)|\-|\_|\+|\=|\||\\|\[|\]|\{|\}|\;|\:|\"|\'|\,|\<|\.|\>|\/|\?]/g,"");

        // 处理数据
        var costSupplierTemp = data_row.costSupplier.replace(/,/g,'');
        var costFareTemp = data_row.costFare.replace(/,/g,'');
        var costMealTemp = data_row.costMeal.replace(/,/g,'');
        var costIncidentalTemp = data_row.costIncidental.replace(/,/g,'');
        var costHousingTemp = data_row.costHousing.replace(/,/g,'');
        var costTeaTemp = data_row.costTea.replace(/,/g,'');
        var costPrintTemp = data_row.costPrint.replace(/,/g,'');
        var costStoresTemp = data_row.costStores.replace(/,/g,'');
        var costTeacherTravelTemp = data_row.costTeacherTravel.replace(/,/g,'');
        var costTravelTemp = data_row.costTravel.replace(/,/g,'');
        var costOtherTemp = data_row.costOther.replace(/,/g,'');
        var contractAmountTemp = data_row.contractAmount.replace(/,/g,'');
        var taxRevenueTemp = data_row.taxRevenue.replace(/,/g,'');
        var aftertaxCostTemp = data_row.aftertaxCost.replace(/,/g,'');

        var removesupplierInternalProgramList = [];
                    var totalTemp = Number(costSupplierTemp) +
                                    Number(costFareTemp) +
                                    Number(costMealTemp) +
                                    Number(costIncidentalTemp) +
                                    Number(costHousingTemp) +
                                    Number(costTeaTemp) +
                                    Number(costPrintTemp) +
                                    Number(costStoresTemp) +
                                    Number(costTeacherTravelTemp) +
                                    Number(costTravelTemp) +
                                    Number(costOtherTemp);
        //$("#totalCost").val(totalTemp);
        var totalTempStr = String(totalTemp);

        // 处理“项目排期”数据
        for(var i=0; i<supplierTabData.length;i++){
            var internalIdTemp = '';

            if(supplierTabData[i].internalId){
                internalIdTemp = supplierTabData[i].internalId;
            }
            var supplierJson = {
                supplierId: supplierTabData[i].id,
                internalId: internalIdTemp,
                state: supplierTabData[i].state
            }
            supplierInternalProgramList.push(supplierJson);
        }

        var param = {
            id:internalProIdTemp,
            // 供应商
            costSupplier: costSupplierTemp,
            // 交通费
            costFare: costFareTemp,
            // 餐费
            costMeal: costMealTemp,
            // 杂费
            costIncidental: costIncidentalTemp,
            // 住宿费
            costHousing: costHousingTemp,
            // 茶歇费
            costTea: costTeaTemp,
            // 打印费
            costPrint: costPrintTemp,
            // 物料费
            costStores: costStoresTemp,
            // 讲师差旅费
            costTeacherTravel: costTeacherTravelTemp,
            // 差旅费
            costTravel: costTravelTemp,
            // 其他
            costOther: costOtherTemp,
            // 客户状态
            customState: data_row.customState,
            //合同总金额含税
            contractAmount: contractAmountTemp,
            // 项目税后总营收
            taxRevenue: taxRevenueTemp,
            // 总成本
            totalCost: totalTempStr,
            //税后成本
            aftertaxCost: aftertaxCostTemp,
            // 项目Id
            programId: consuleIdTemp,
            // 内部关联
            supplierInternalProgramList: supplierInternalProgramList

        }
        if(param.costSupplier == '' && param.costFare == '' && param.costMeal == '' && param.costIncidental == '' && param.costHousing == '' && param.costTea == '' &&
            param.costPrint == '' &&  param.costStores == '' && param.costTeacherTravel == '' && param.costTravel == '' && param.costOther == '' && param.customState == '' &&
           param.contractAmount == '' && param.taxRevenue == '' && param.aftertaxCost == '' &&  param.supplierInternalProgramList.length == 0){

            layer.msg("没有填写项目，无法保存！", {icon: 1});
            return false;

        }
        layer.load(2);

        $.ajax({
            type: 'POST',
            url: internalURL,//发送请求
            contentType: "application/json; charset=utf-8",
            async: true,
            data: JSON.stringify(param),
            dataType: "json",
            success: function (res) {
                layer.closeAll("loading");
                if(res.code == 200){
                    // 如果内部流程新增，那么新增完成后获取“内部流程”ID
                    internalProIdTemp = res.data;
                    layer.msg("添加成功！", {icon: 1});
                    layer.closeAll('page');
                    $("#testInternalListAction").trigger("click");
                }else{
                    layer.msg(res.msg, {icon: 2});
                }
            }
        });
        return false;
    });

    // 内部流程 “供应商状态”删除
    function doSupplierDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            // 处理memberTabData, userData, removeUserData;
            var tmpMemberData = [];
            for(var i=0; i<supplierTabData.length; i++) {
                if(id != supplierTabData[i].id) {
                    tmpMemberData.push(supplierTabData[i]);
                }
            }
            supplierTabData = tmpMemberData;
            // table.reload('memberTable',{data : memberTabData});

            var tempRmData = [];
            for(var i=0; i<removeSupplierData.length; i++) {
                if(id != removeSupplierData[i].id) {
                    tempRmData.push(removeSupplierData[i]);
                } else {
                    supplierData.push(removeSupplierData[i]);
                }
            }
            removeSupplierData = tempRmData;

            layer.msg("删除成功");
            obj.del();
        });
    }

    // 内部流程 编辑获取数据
    function getInternalProgramData(data){
        $.get("/internalprogram/getInternal", {id: data}, function (res) {
            if(res.code == 200) {
                if(res.data.length > 0){
                    internalProIdTemp = res.data[0].id;
                    // 内部流程业务处理--“供应商状态”Table
                    showEditIntrenalModel(internalProIdTemp);
                    // 内部流程上传数据回调
                    internalEditFiles(internalProIdTemp);
                    form.val('consultinInternalgForm', res.data[0]);
                }else{
                    showEditIntrenalModel();
                }

            }
        });
    }

    // 内部流程上传附件回调
    function internalEditFiles(obj){
        // “内部流程”上传附件数据回调
        $('#internalUpdateList').html('');
        $.ajax({
            url: '/file/fileList?type=12&correlatorId=' + obj,
            type:"get"
            ,async:false
            ,dataType:"json"
            , success: function(res){
                if(res.code == 200) {
                    var ds = res.data;
                    if(ds.length > 0) {
                        for (var i = 0; i < ds.length; i++) {
                            (function (d) {
                                var tr = $(['<tr id="' + d.fileId + '" name="' + d.fileUrl + '">'
                                    , '<td>' + d.fileName + '</td>'
                                    , '<td>已上传</td>'
                                    , '<td>'
                                    , '<button class="layui-btn layui-btn-xs layui-btn-danger data-delete">删除</button>'
                                    , '<button class="layui-btn layui-btn-xs data-download">下载</button>'
                                    , '</td>'
                                    , '</tr>'].join(''));
                                //删除
                                tr.find('.data-delete').on('click', function (param) {
                                    // delete files[index]; //删除对应的文件
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    // 删除文件库中的文件
                                    $.post("/file/deleleFile", {
                                        fileId: fileId,
                                        fileUrl: fileUrl.substring(0, fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)),
                                        type: 12
                                    }, function (res) {
                                        if (res.code == 200) {
                                            layer.msg("删除成功！");
                                            tr.remove();
                                        } else {
                                            layer.msg(res.msg);
                                        }
                                    });
                                    return false;
                                    // uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                                });

                                // 下载
                                tr.find('.data-download').on('click', function () {
                                    var fileId = tr.attr("id");
                                    var fileUrl = tr.attr("name");
                                    var tds = tr.children();
                                    var fileName = tds.eq(0).html();
                                    var url = '/file/download';
                                    var option = {
                                        data: {fileId: fileId,
                                            fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName)), fileName: fileName}
                                    }
                                    $.get("/file/isExistFile", {fileUrl: fileUrl.substring(0,fileUrl.indexOf(fileName)) + "(" + fileId + ")" + fileUrl.substring(fileUrl.indexOf(fileName))}, function (res) {
                                        if(res.code == 200) {
                                            download(url, option);
                                        } else {
                                            layer.msg(res.msg, {icon: 2});
                                        }
                                    });
                                    return false;
                                });

                                $('#internalUpdateList').append(tr);

                            })(ds[i]);
                        }
                    }
                }

            }
        });
    }

    // 获取 内部流程 “供应商”数据
    function getSupplierData(){
        // 获取供应商数据
        $.get("/base/supplier/getSupplierData", {}, function (res) {
            if(res.code == 200) {
                supplierData = res.data;
            }
        });
    }


    // “内部流程”附件上传
    function loadInsteralFileUpload() {
        //多文件列表示例
        var demoListView = $('#internalUpdateList')
            ,uploadListIns = upload.render({
            elem: '#testInternalList'
            ,url: '/file/upload/'
            ,accept: 'file'
            ,size: 10240
            ,multiple: true
            ,number: 5
            ,auto: false
            ,bindAction: '#testInternalListAction'
            ,before: function() {
                this.data = {"id": internalProIdTemp, "type": 12}
            }
            ,choose: function(obj){
                var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                //读取本地文件
                obj.preview(function(index, file, result){
                    var tr = $(['<tr id="upload-'+ index +'">'
                        ,'<td>'+ file.name +'</td>'
                        , '<td>等待上传</td>'
                        ,'<td>'
                        ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
                        ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">删除</button>'
                        ,'</td>'
                        ,'</tr>'].join(''));

                    //单个重传
                    tr.find('.demo-reload').on('click', function(){
                        obj.upload(index, file);
                        return false;
                    });

                    //删除
                    tr.find('.demo-delete').on('click', function(){
                        delete files[index]; //删除对应的文件
                        tr.remove();
                        uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                    });

                    demoListView.append(tr);
                });
            }
            ,done: function(res, index, upload){
                if(res.code == 200){ //上传成功
                    var fileId = res.data; //附件id
                    fileIds.push(JSON.parse(fileId).id);
                    console.log(fileIds);
                    var tr = demoListView.find('tr#upload-'+ index)
                        ,tds = tr.children();
                    tds.eq(2).html(''); //清空操作
                    return delete this.files[index]; //删除文件队列已经上传成功的文件
                }
                this.error(index, upload);
            }
            ,error: function(index, upload){
                var tr = demoListView.find('tr#upload-'+ index)
                    ,tds = tr.children();
                tds.eq(2).find('.demo-reload').removeClass('layui-hide'); //显示重传
            }
        });
    }

// =============================================================== “内部流程”逻辑处理 End ===============================================================//

// =============================================================== “业务流程，内部流程”公共方法处理 Start ===============================================================//

    // 交集
    function intersection (arr1, arr2, key) {
        var tmpArr = [];
        for(var i=0;i<arr1.length;i++){
            for(var j=0;j<arr2.length;j++){
                var arr = arr2[j];
                if(arr1[i].id == eval('arr.' + key)){
                    tmpArr.push(arr1[i]);
                }
            }
        }
        return tmpArr;
    }
    // 差集
    function difference(arr1, arr2, key) {
        var tmpArr = [];
        for(var i=0;i<arr1.length;i++){
            var flag = true;
            for(var j=0;j<arr2.length;j++){
                var arr = arr2[j];
                if(arr1[i].id == eval('arr.' + key)){
                    flag = false;
                }
            }
            if(flag){
                tmpArr.push(arr1[i]);
            }
        }
        return tmpArr;
    }

    // 根据客户Id查找联系人
    form.on('select(customId)', function(data){
        var text = data.elem[data.elem.selectedIndex].text;
        $.get("/linkman/getLinkMans", {customId: data.value}, function (res) {
            if(res.code == 200) {
                showCustomTab(res.data, text)
            }
        });
    });

    // 日期处理
    function compareDate(DateOne, DateTwo) {
        var OneMonth = DateOne.substring(5, DateOne.lastIndexOf("-"));
        var OneDay = DateOne.substring(DateOne.length, DateOne.lastIndexOf("-") + 1);
        var OneYear = DateOne.substring(0, DateOne.indexOf("-"));
        var TwoMonth = DateTwo.substring(5, DateTwo.lastIndexOf("-"));
        var TwoDay = DateTwo.substring(DateTwo.length, DateTwo.lastIndexOf("-") + 1);
        var TwoYear = DateTwo.substring(0, DateTwo.indexOf("-"));
        if (Date.parse(OneMonth + "/" + OneDay + "/" + OneYear) > Date.parse(TwoMonth + "/" + TwoDay + "/" + TwoYear)) {
            return true;
        } else {
            return false;
        }
    }

    // 展示 业务流程 客户
    function showCustomTab(data, text) {
        $("#customList").html("");
        if(data.length > 0) {
            for(var i=0; i<data.length; i++) {
                var tr = $(['<tr id="'+ data[i].id +'">'
                    ,'<td>'+ text +'</td>'
                    ,'<td>'+ (data[i].name == undefined ? '':data[i].name) +'</td>'
                    , '<td>'+ (data[i].phone == undefined ? '':data[i].phone) +'</td>'
                    , '<td>'+ (data[i].email == undefined ? '':data[i].email) +'</td>'
                    , '<td>'+ (data[i].contactAddress == undefined ? '':data[i].contactAddress) +'</td>'
                    ,'</tr>'].join(''));
                $("#customList").append(tr);
            }
        }
    }

    // 排序
    function arraySort(arr) {
        arr.sort(function(a,b){
            if(a.id>b.id) return 1 ;
            if(a.id<b.id) return -1 ;
            return 0 ;
        });
    }

// =============================================================== “业务流程，内部流程”公共方法处理 End ===============================================================//

});