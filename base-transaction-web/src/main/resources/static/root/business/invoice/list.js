layui.use(['layer', 'form', 'table', 'util', 'admin'], function () {
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var table = layui.table;
    var util = layui.util;
    var admin = layui.admin;

    var programs = [], customs = [], infoTabData = [], isHideCol = false;

    //渲染表格
    table.render({
        elem: '#invoiceTable',
        url: '/invoicedetail/getlist',
        method: 'post',
        page: true,
        cellMinWidth: 100,
        cols: [[
            {type: 'numbers'},
            {field: 'correlatorName', title: '项目名称'},
            {field: 'invoiceCustomName', title: '开票客户名称'},
            {field: 'invoiceAmount', title: '本次开票金额（含税）'},
            {align: 'center', toolbar: '#tableBar', title: '操作', minWidth: 200}
        ]],
        response: {
            statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
        },
        parseData: function (res) { //将原始数据解析成 table 组件所规定的数据
            return {
                "code": res.code, //解析接口状态
                "msg": res.msg, //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.data //解析数据列表
            };
        },
        done: function () {
            layer.closeAll('loading');
        }
    });

    // 添加按钮点击事件
    $('#btnAdd').click(function () {
        showEditModel();
    });

    // 搜索按钮点击事件
    $('#btnSearch').click(function () {
        var programName = $('#programName').val();
        var invoiceName = $('#invoiceName').val();
        table.reload('invoiceTable', {where: {programName: programName, invoiceName: invoiceName}});
    });

    // 工具条点击事件
    table.on('tool(invoiceTable)', function (obj) {
        var data = obj.data;
        if (obj.event === 'edit') { //修改
            showEditModel(data);
        } else if (obj.event === 'del') { // 删除
            doDelete(obj);
        } else if (obj.event === 'detail') { // 查看
            showEditModel(data, 'detail');
        } else if (obj.event === 'export') { // 查看
            exportPdf(data);
        }

    });

    function exportPdf(data) {
        window.open('/invoicedetail/htmltopdf?id=' + data.id,'target','');
    }

    // 删除
    function doDelete(obj) {
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            layer.load(2);
            $.post("/invoicedetail/deleteByID", {id: obj.data.id}, function (res){
                layer.closeAll('loading');
                if(res.code == 200){
                    layer.msg("删除成功");
                    obj.del();
                }else{
                    layer.msg(res.msg);
                }
            });
        });
    }

    // 显示编辑弹窗
    function showEditModel(data, showType) {
        isHideCol = false;
        layer.open({
            type: 1,
            area: ['60%', '100%'],
            title: showType? '查看开票' : data ? '修改开票' : '添加开票',
            content: $('#invoiceForm').html(),
            success: function () {

                // 渲染下拉框
                initSelect('correlatorId', programs, '');
                initSelect('invoiceCustomId', customs, '');

                if(data) {
                    // 渲染开票客户信息
                    if(data.invoiceCustomId) {
                        showInvoiceCustomTable(data.invoiceCustomId);
                    }
                    // 渲染开票信息
                    showInvoiceInfoTable(data.id);
                } else {
                    showInvoiceInfoTable();
                }

                // 控制form表单是否可以编辑
                if(showType) {
                    $('form').find('input,select').attr('disabled', true);
                    $("#addInfoButton").css('display', 'none');
                    $("#cancleBtn").css('display', 'none');
                    $("#invoiceFormSubmit").css('display', 'none');
                    isHideCol = true;
                }

                form.val('invoiceForm', data);
                // 表单提交事件
                form.on('submit(invoiceFormSubmit)', function (d) {
                    layer.load(2);

                    // 组装保存数据
                    var programName = $("#programName").val();
                    var invoiceName = $("#invoiceName").val();

                    var param = {
                        id: d.field.id
                        ,correlatorId: d.field.correlatorId
                        ,invoiceCustomId: d.field.invoiceCustomId
                        ,contractNumber: d.field.contractNumber
                        ,contractAmount: d.field.contractAmount
                        ,contractPeriod: d.field.contractPeriod
                        ,hasPaid: d.field.hasPaid
                        ,bankAndDate: d.field.bankAndDate
                        ,invoiceAmount: d.field.invoiceAmount
                        ,invoiceInfos: infoTabData
                    }
                    if(data) {
                        $.ajax({
                            type: 'POST',
                            url: '/invoicedetail/update',
                            contentType: "application/json; charset=utf-8",
                            async: true,
                            data: JSON.stringify(param),
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll("loading");
                                if(res.code == 200){
                                    layer.msg("修改成功！", {icon: 1});
                                    layer.closeAll('page');
                                    table.reload('invoiceTable', {where: {programName: programName, invoiceName: invoiceName}});
                                }else{
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                    } else {
                        $.ajax({
                            type: 'POST',
                            url: '/invoicedetail/create',//发送请求
                            contentType: "application/json; charset=utf-8",
                            async: true,
                            data: JSON.stringify(param),
                            dataType: "json",
                            success: function (res) {
                                layer.closeAll("loading");
                                if(res.code == 200){
                                    layer.msg("添加成功！", {icon: 1});
                                    layer.closeAll('page');
                                    table.reload('invoiceTable', {where: {programName: programName, invoiceName: invoiceName}});
                                }else{
                                    layer.msg(res.msg, {icon: 2});
                                }
                            }
                        });
                    }
                    return false;
                });

            }
        });
    }

    // 绑定事件
    form.on('select(invoiceCustomId)', function(data){
        showInvoiceCustomTable(data.value);
    });

    function showInvoiceCustomTable(invoiceCustomId) {
        $.get("/invoicecustom/findInvoiceCustomById", {invoiceCustomId: invoiceCustomId}, function (res) {
            if(res.code == 200) {
                $("#customList").html("");
                var data = res.data;
                if(data) {
                    var tr = $(['<tr id="'+ data.id +'">'
                        ,'<td>'+ (data.name == undefined ? '':data.name) +'</td>'
                        , '<td>'+ (data.customTaxid == undefined ? '':data.customTaxid) +'</td>'
                        , '<td>'+ (data.customAddress == undefined ? '':data.customAddress) +'</td>'
                        , '<td>'+ (data.customPhone == undefined ? '':data.customPhone) +'</td>'
                        , '<td>'+ (data.customBank == undefined ? '':data.customBank) +'</td>'
                        , '<td>'+ (data.customBanknum == undefined ? '':data.customBanknum) +'</td>'
                        ,'</tr>'].join(''));
                    $("#customList").append(tr);
                }
            }
        });
    }

    function showInvoiceInfoTable(id) {
        infoTabData = [];
        if(id) {
            // 获取开票信息的数据
            $.get("/invoiceinfo/findInvoiceInfoByDetailId", {invoiceDetailId: id}, function (res) {
                if(res.code == 200) {
                    infoTabData = res.data;
                    table.reload('infoTable',{data : infoTabData});

                    if(infoTabData.length > 0) {
                        // 计算合计金额含税（元）的数据
                        var val = $("#totolAmount").val() == "" ? 0 : parseFloat($("#totolAmount").val());
                        var amount = 0;
                        for(var i=0; i<infoTabData.length; i++) {
                            amount += infoTabData[i].amount == undefined || infoTabData[i].amount == "" ? 0: parseFloat(infoTabData[i].amount);
                        }
                        $("#totolAmount").val(val + amount);
                    }
                }
            });
        }

        //渲染表格
        table.render({
            elem: '#infoTable',
            data: infoTabData,
            method: 'get',
            page: false,
            cellMinWidth: 100,
            response: {
                statusName: 'code' //规定数据状态的字段名称，默认：code
                , statusCode: 200 //规定成功的状态码，默认：0
                , msgName: 'msg' //规定状态信息的字段名称，默认：msg
                , countName: 'count' //规定数据总数的字段名称，默认：count
                , dataName: 'data' //规定数据列表的字段名称，默认：data
            },
            cols: [[
                {field: 'name', title: '应税劳务名称'},
                {field: 'specification', title: '规格型号'},
                {field: 'unit', title: '单位'},
                {field: 'quantity', title: '数量'},
                {field: 'price', title: '单价'},
                {field: 'amount', title: '金额含税（元）'},
                {field: 'invoiceNumber', title: '发票号'},
                {align: 'center', toolbar: '#infoTableBar', title: '操作', minWidth: 200}
            ]],
            done: function () {
                if(isHideCol) {
                    $('#infoTable').next().find('a').css("display","none");
                }
                layer.closeAll('loading');
            }
        });

        // 添加按钮点击事件
        $('#addInfoButton').click(function () {
            showInvoiceInfoForm();
        });

        // 工具条点击事件
        table.on('tool(infoTable)', function (obj) {
            var data = obj.data;
            var layEvent = obj.event;

            if (layEvent === 'del') { // 删除
                doInvoiceInfoDelete(obj);
            } else if (layEvent === 'edit') { // 修改
                showInvoiceInfoForm(data);
            }
        });
    }

    function uuid() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }

    function showInvoiceInfoForm(data) {
        var index = layer.open({
            type: 1,
            area: ['400px', '55%'],
            offset: '65px',
            title: data ? '修改开票信息' : '添加开票信息',
            content: $('#infoForm').html(),
            success: function () {
                if(data) {
                    form.val('infoForm', data);
                }

                form.render();
                // 表单提交事件
                form.on('submit(infoFormSubmit)', function (d) {
                    layer.load(2);

                    // 处理courseTabData;
                    var rowData_ = {};
                    rowData_.id = d.field.id;
                    rowData_.name= d.field.name;
                    rowData_.specification = d.field.specification;
                    rowData_.unit = d.field.unit;
                    rowData_.quantity = d.field.quantity;
                    rowData_.price = d.field.price;
                    rowData_.amount = d.field.amount;
                    rowData_.invoiceNumber = d.field.invoiceNumber;
                    if(rowData_.id == undefined || rowData_.id == "") {
                        rowData_.id = uuid();
                        infoTabData.push(rowData_);
                    } else {
                        var tmpTableData = infoTabData;
                        var oldData = new Array();
                        for(var i=0; i<tmpTableData.length; i++) {
                            if(tmpTableData[i].id == rowData_.id) {
                                oldData.push(rowData_);
                            } else {
                                oldData.push(tmpTableData[i]);
                            }
                        }
                        infoTabData = oldData;
                    }
                    table.reload('infoTable',{data: infoTabData});

                    layer.closeAll("loading");
                    if(data) {
                        // 计算合计金额含税（元）的数据
                        var val = $("#totolAmount").val() == "" ? 0 : parseFloat($("#totolAmount").val());
                        var amount = d.field.amount == "" ? 0: parseFloat(d.field.amount);
                        var subamount = data.amount == "" ? 0: parseFloat(data.amount);
                        $("#totolAmount").val(val - subamount + amount);
                        layer.msg("修改成功！", {icon: 1});
                    } else {
                        // 计算合计金额含税（元）的数据
                        var val = $("#totolAmount").val() == "" ? 0 : parseFloat($("#totolAmount").val());
                        var amount = d.field.amount == "" ? 0: parseFloat(d.field.amount);
                        $("#totolAmount").val(val + amount);
                        layer.msg("添加成功！", {icon: 1});
                    }

                    layer.close(index);
                    return false;
                });

            }
        });
    }

    function doInvoiceInfoDelete(obj) {
        var id = obj.data.id;
        layer.confirm('确定要删除吗？', {
            offset: '65px',
            title: '提示'
        }, function (i) {
            layer.close(i);
            var tmpInfoData = [];
            for(var i=0; i<infoTabData.length; i++) {
                if(id != infoTabData[i].id) {
                    tmpInfoData.push(infoTabData[i]);
                }
            }
            infoTabData = tmpInfoData;

            // 计算合计金额含税（元）的数据
            var val = $("#totolAmount").val() == "" ? 0 : parseFloat($("#totolAmount").val());
            var amount = obj.data.amount == "" ? 0: parseFloat(obj.data.amount);
            $("#totolAmount").val(val - amount);

            layer.msg("删除成功");
            obj.del();
        });
    }

    // 获取初始化数据
    layer.load(2);
    $.get("/invoicedetail/getinitdata", {}, function (res) {
        layer.closeAll('loading');
        if(res.code == 200) {
            programs = res.data.programVos;
            customs = res.data.invoiceCustoms;
        } else {
            layer.msg('获取初始化数据失败');
        }
    });

    function initSelect(ctlId, data, tip) {
        var $sel = $("#"+ ctlId);
        $sel.empty();
        $sel.append('<option value="'+""+'">'+"请选择" + tip +'</option>');


        for(var i=0; i<data.length; i++){
            $sel.append('<option selected value="'+ data[i].id +'">'+ data[i].name +'</option>');
        }
        $sel.val('');
    }

});