package com.huafagroup.webapp.base;

import com.huafagroup.core.configuration.Globals;
import com.huafagroup.core.oauth.enums.OAuthGrantType;
import com.huafagroup.core.oauth.response.OAuthToken;
import com.huafagroup.core.util.TokenUtil;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 自定义拦截器
 * 通过在session中实现局部回话
 * note: 要注意session共享 （多实例应用）
 * @Author: Kevin
 * @Date: 00:24 2018/12/3
 */
@Component
public class LoginHandlerInterceptor implements HandlerInterceptor {
    private static final Logger logger = org.slf4j.LoggerFactory.getLogger(LoginHandlerInterceptor.class);
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private Globals globals;

    /**
     *
     * @param request
     * @param response
     * @param o
     * @return
     * @throws IOException
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws IOException {
      /*  if (isAjaxRequest(request))
            return preHandleAjax(request, response, o);
        else
            return preHandleNormalRequest(request, response, o);*/
      return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) {

    }

    public boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWith = request.getHeader("x-requested-with");
        return requestedWith != null && requestedWith.equalsIgnoreCase("XMLHttpRequest");
    }

    private boolean preHandleAjax(HttpServletRequest request, HttpServletResponse response, Object o) {
        logger.info("当前Ajax请求路径.." + request.getRequestURI());
        OAuthToken accessToken = (OAuthToken) request.getSession().getAttribute("user_access_token");
        return accessToken != null && !TokenUtil.isTokenExpired(accessToken) ;
    }

    private boolean preHandleNormalRequest(HttpServletRequest request, HttpServletResponse response, Object o) throws IOException{
        logger.info("当前请求路径.." + request.getRequestURI());
        OAuthToken accessToken = (OAuthToken) request.getSession().getAttribute("user_access_token");
        if (accessToken == null || TokenUtil.isTokenExpired(accessToken)){
            //未登陆或token过期
            redirectSSOPage(request,response);
            return false;
        }

        return true;
    }

    private void redirectSSOPage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String goingUrl =  request.getRequestURL() + (StringUtils.isEmpty(request.getQueryString()) ? "" : "?" + request.getQueryString());
        String state = "" + new Random().nextInt();
        request.getSession().setAttribute("sso_login_state", state);
        request.getSession().setAttribute("sso_login_next_url", goingUrl);
        String ssoUrl = new StringBuilder(globals.getSsoAuthUrl())
                .append("?grantType=").append(OAuthGrantType.AUTHORIZATION_CODE.toString())
                .append("&appId=").append(globals.getAppId())
                .append("&redirectUri=").append(URLEncoder.encode(globals.getSsoAuthRedirectUrl(),"utf-8"))
                .append("&sessionId=").append(request.getSession().getId())
                .append("&userDevice=1")
                .append("&loginType=1")
                .append("&ssoLogoutUrl=").append(URLEncoder.encode(globals.getSsoAuthCurrentNoteLogoutUrl(),"utf-8"))
                .append("&scope=login")
                .append("&state=").append(state).toString();
        logger.info("重定向到认证页面:{}", ssoUrl);
        response.sendRedirect(ssoUrl);
    }
}
