package com.huafagroup.webapp.base;

import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 控制器基类
 *
 * @Author: Kevin
 * @Date: 23:54 2018/12/2
 */
public class BaseController {


    @Autowired
    private RedissonClient redissonClient;

//    @PostMapping(value = "/listDtree")
//    @ResponseBody
//    public String listDtree(@Nullable String id) {
//        return erpDepartmentService.listAllMetersFormat("0");
//    }

}
