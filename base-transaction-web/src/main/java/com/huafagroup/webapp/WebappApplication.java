package com.huafagroup.webapp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;

@SpringBootApplication
//@EnableRedissonHttpSession
@ServletComponentScan
public class WebappApplication {

	public static void main(String[] args) {
		SpringApplication.run(WebappApplication.class, args);
		//程序启动后，启用kafka日志写入功能
		//KafkaAppender.applicationStatus = 1;
	}
}
