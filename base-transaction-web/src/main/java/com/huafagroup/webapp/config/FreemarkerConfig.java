package com.huafagroup.webapp.config;

import freemarker.template.Configuration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * 2018/12/28
 */
@Component
public class FreemarkerConfig {


    @Autowired
    private Configuration configuration;


/*
    @Autowired
    private SystemDictFilterTemplate systemDictFilterTemplate;

    @Autowired
    private SystemDictBlackListTemplate systemDictBlackListTemplate;
*/

    @PostConstruct
    public void setSharedVariable() {
        //系统字典标签
//        configuration.setSharedVariable("user_dict", systemDictTemplate);
      /*      configuration.setSharedVariable("erp_filter_dict", systemDictFilterTemplate);
        configuration.setSharedVariable("erp_blacklist_dict", systemDictBlackListTemplate);*/
        /* configuration.setSharedVariable("shiro", new ShiroTags());*/
    }

}
