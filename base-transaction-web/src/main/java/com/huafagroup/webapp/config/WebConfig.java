package com.huafagroup.webapp.config;

import brave.spring.webmvc.SpanCustomizingAsyncHandlerInterceptor;
import com.fasterxml.jackson.databind.ObjectMapper;
//import VersionHandlerMapping;
import com.huafagroup.core.configuration.ProtostuffHttpMessageConverter;
import com.huafagroup.core.util.DateConverter;
import com.huafagroup.webapp.base.LoginHandlerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.*;
import org.springframework.core.convert.support.GenericConversionService;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter;
import org.springframework.web.bind.support.ConfigurableWebBindingInitializer;
import org.springframework.web.servlet.config.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;
import org.springframework.web.servlet.view.InternalResourceViewResolver;

import javax.annotation.PostConstruct;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * springmvc 配置适配器
 *
 * @Author: Huangguohua
 * @Date: 00:24 2018/12/3
 */
@Import(SpanCustomizingAsyncHandlerInterceptor.class)
@Configuration
@ComponentScan(basePackages = {"com.huafagroup.core.configuration", "com.huafagroup.core.util", "com.huafagroup.webapp", "com.huafagroup.core.service"})
public class WebConfig extends WebMvcConfigurationSupport {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RequestMappingHandlerAdapter handlerAdapter;

    @Autowired
    public MappingJackson2HttpMessageConverter jsonConverter;

    @Primary
    @Bean
    public MappingJackson2HttpMessageConverter getCustomJacksonConverter(ObjectMapper objectMapper) {
        MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();

        mappingJackson2HttpMessageConverter.setObjectMapper(objectMapper);
        //设置中文编码格式
        List<MediaType> list = new ArrayList<MediaType>();
        list.add(MediaType.APPLICATION_JSON_UTF8);
        list.add(MediaType.APPLICATION_JSON);
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(list);
        return mappingJackson2HttpMessageConverter;
    }

    /*增加ajax跨域访问支持*/
    @Override
    protected void addCorsMappings(CorsRegistry registry) {

        registry.addMapping("/**").allowedMethods("*").allowedOrigins("*").allowedHeaders("*");
        // super.addCorsMappings(registry);
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loginHandlerInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/sso/**","/login", "/login/**", "/logout", "/logout/**", "/genCaptcha","/root/**","/app/**");
        super.addInterceptors(registry);
    }

    @Bean //将自定义拦截器注册到spring bean中
    public LoginHandlerInterceptor loginHandlerInterceptor(){
        return new LoginHandlerInterceptor();
    }

    //http://localhost:8081/root/layui/layui.js

    /*自定义jackson 消息序列化*/


    /**
     * 增加字符串转日期的功能
     */
    @PostConstruct
    public void initEditableValidation() {
        ConfigurableWebBindingInitializer initializer = (ConfigurableWebBindingInitializer) handlerAdapter.getWebBindingInitializer();
        if (initializer.getConversionService() != null) {
            GenericConversionService genericConversionService = (GenericConversionService) initializer.getConversionService();
            genericConversionService.addConverter(new DateConverter());
        }
    }



    //添加protobuf支持，需要client指定accept-type：application/x-protobuf
    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter();
        stringConverter.setDefaultCharset(Charset.forName("utf-8"));
        List<MediaType> list = new ArrayList<MediaType>();
        list.add(MediaType.TEXT_PLAIN);
        list.add(MediaType.TEXT_HTML);
        list.add(MediaType.TEXT_MARKDOWN);
        stringConverter.setSupportedMediaTypes(list);
        MappingJackson2XmlHttpMessageConverter xmlConverter = new MappingJackson2XmlHttpMessageConverter();
        xmlConverter.setDefaultCharset(Charset.forName("utf-8"));
        List<MediaType> list2 = new ArrayList<MediaType>();
        list2.add(MediaType.APPLICATION_XML);
        xmlConverter.setSupportedMediaTypes(list2);
        converters.add(0, stringConverter);
        converters.add(0, xmlConverter);
        converters.add(0, new ProtostuffHttpMessageConverter());
        converters.add(0, getCustomJacksonConverter(objectMapper));
    }

    //@Override
    //@Bean
    //public RequestMappingHandlerMapping requestMappingHandlerMapping() {
    //RequestMappingHandlerMapping handlerMapping = new VersionHandlerMapping("v");
    //handlerMapping.setOrder(0);
    //
    //handlerMapping.setInterceptors(getInterceptors());
    //AntPathMatcher pathMatcher = new AntPathMatcher();
    //pathMatcher.setCaseSensitive(false);
    //handlerMapping.setPathMatcher(pathMatcher);
    //return handlerMapping;
    //}

    /*忽略url大小写*/
    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {


        configurer.setUseSuffixPatternMatch(false).setUseTrailingSlashMatch(true);
    }

    /**
     * 发现如果继承了WebMvcConfigurationSupport，则在yml中配置的相关内容会失效。
     * 需要重新指定静态资源
     *
     *
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        super.addResourceHandlers(registry);
    }

    /**
     * 配置servlet处理
     */
    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer configurer) {

        configurer.enable();
    }

    @Bean
    public HttpMessageConverter<String> responseBodyConverter() {
        StringHttpMessageConverter converter = new StringHttpMessageConverter(Charset.forName("UTF-8"));
        return converter;
    }


    @Override
    protected void configureViewResolvers(ViewResolverRegistry registry) {
        InternalResourceViewResolver viewResolver = new InternalResourceViewResolver();
        viewResolver.setPrefix("/");
        viewResolver.setSuffix(".html");
        registry.viewResolver(viewResolver);
        //super.configureViewResolvers(registry);
    }

}