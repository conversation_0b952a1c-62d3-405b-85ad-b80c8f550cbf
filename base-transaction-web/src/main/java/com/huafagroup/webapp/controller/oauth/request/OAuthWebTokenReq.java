package com.huafagroup.webapp.controller.oauth.request;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

public class OAuthWebTokenReq {
    @NotEmpty(message="appId不能为空")
    private String appId;

    @NotEmpty(message="authCode不能为空")
    private String authCode;

    private Set<String> scope;

    @NotEmpty(message="grantType不能为空")
    private String grantType;

    private Integer deviceType;

    @NotEmpty(message="sessionId不能为空")
    private String sessionId;

    private String ssoLogoutUrl;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public Set<String> getScope() {
        return scope;
    }

    public void setScope(Set<String> scope) {
        this.scope = scope;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSsoLogoutUrl() {
        return ssoLogoutUrl;
    }

    public void setSsoLogoutUrl(String ssoLogoutUrl) {
        this.ssoLogoutUrl = ssoLogoutUrl;
    }

    @Override
    public String toString() {
        return "OAuthWebTokenReq{" +
                "appId='" + appId + '\'' +
                ", authCode='" + authCode + '\'' +
                ", scope=" + scope +
                ", grantType='" + grantType + '\'' +
                ", deviceType=" + deviceType +
                ", sessionId='" + sessionId + '\'' +
                ", ssoLogoutUrl='" + ssoLogoutUrl + '\'' +
                '}';
    }
}
