package com.huafagroup.webapp.controller.main;

import com.huafagroup.core.configuration.Globals;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.oauth.response.OAuthToken;
import com.huafagroup.core.util.JsonUtil;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.webapp.base.BaseController;
import com.huafagroup.webapp.controller.oauth.enums.OAuthGrantType;
import com.huafagroup.webapp.controller.oauth.request.OAuthAPPTokenReq;
import com.huafagroup.webapp.controller.oauth.request.OAuthWebTokenReq;
import com.huafagroup.webapp.controller.oauth.session.CustomizedSessionManager;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

/**
 * 登录授权控制器
 * <p>
 * Kevin
 */
@Controller
public class LoginController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(LoginController.class);

    @Autowired
    private RedissonClient redissonClient;
    @Resource(name = "restTemplateLB")
    private RestTemplate restTemplateLB;
    @Qualifier("signleTemplate")
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private Globals globals;

    public LoginController() {
        super();
    }

    /**
     * 登录成功后的跳转请求
     *
     * @param model
     * @return
     */
    @GetMapping("index")
    public ModelAndView showView(ModelAndView model) {
        return model;
    }

    /**
     * 空地址请求
     *
     * @return
     */
    @GetMapping(value = "")
    public String index() {
        //todo session校验
        //return StringUtils.isEmpty(MySysUser.loginName(request)) ? "/" : "redirect:/index";
        return "redirect:/index";
    }

    @GetMapping("main")
    public Model main(Model model) {
        return model;
    }

    @GetMapping("systemLogout")
    public String logOut(HttpServletRequest request) {
        doSSOLogout(request);
        request.getSession().invalidate();
        return "redirect:/index";
    }

    private void doSSOLogout(HttpServletRequest request) {
        OAuthToken accessToken = (OAuthToken) request.getSession().getAttribute("user_access_token");
        if (accessToken != null){
            String url = globals.getSsoAuthLogoutUrl();
            ParameterizedTypeReference<ResultBean<String>> typeRef = new ParameterizedTypeReference<ResultBean<String>>() {};
            HttpEntity<String> requestEntity = new HttpEntity<>(accessToken.getUserId());
            ResponseEntity<ResultBean<String>> responseEntity = restTemplate.exchange(
                    url, HttpMethod.POST, requestEntity, typeRef);
            ResultBean<String> resultBean = responseEntity.getBody();
            logger.info("认证中心返回注销结果; [{}]", resultBean.getData());
        }
    }

    @GetMapping("/sso/callback")
    public String callback(HttpServletRequest request, HttpServletResponse response, String code, String state) {
        logger.info("登陆验证callback:[code:{}, state:{}]", code, state);
        //String prevState = (String) redissonClient.getBucket("sso_login_state" + request.getSession().getId()).get();
        String prevState = (String) request.getSession().getAttribute("sso_login_state");
        if (!state.equalsIgnoreCase(prevState)) {
            //不是登陆请求，报错
            //todo: 收到非本应用发出的认证请求
        }
        HttpSession session = request.getSession();
        OAuthToken respVO = getAccessCode(code, session.getId());
        if (respVO != null && !StringUtils.isEmpty(respVO.getAccessToken())) {
            //登陆成功，认证中心返回token
            //将token保存到session (对于多实例服务，注意实现session共享)
            session.setAttribute("user_access_token",respVO);
            CustomizedSessionManager.set(respVO.getUserId(), session);
            String nextPage = (String) request.getSession().getAttribute("sso_login_next_url");
            if (!StringUtils.isEmpty(nextPage)) {
                logger.info("重定向到登录前的页面:{}", nextPage);
                return "redirect:" + nextPage;
            }

            return "redirect:/index";
        } else {
            return "/error";
            //todo: error处理
        }
    }

    @RequestMapping(value = "/sso/logout",  method = RequestMethod.POST)
    @ResponseBody
    public ResultBean<String> ssoLogout(@RequestBody String userId) {
        userId = userId.replace("\"","");
        logger.info("收到退出请求: userId[{}]", userId);
        HttpSession session = CustomizedSessionManager.get(userId);
        if (session != null) {
            CustomizedSessionManager.remove(userId);
            session.removeAttribute("user_access_token");
            session.invalidate();
            logger.info("session注销成功: userId[{}]", userId);
        }
        else{
            logger.info("userId[{}]不存在有效session", userId);
        }
        return new ResultBean<>("OK");
    }

    @GetMapping("/login/register")
    @ResponseBody
    public OAuthToken register(HttpServletRequest request, HttpServletResponse response) {
        String url = globals.getLoginRegisterUrl();
        ParameterizedTypeReference<ResultBean<String>> typeRef = new ParameterizedTypeReference<ResultBean<String>>() {};
        OAuthAPPTokenReq authAPPTokenReq = new OAuthAPPTokenReq();
        authAPPTokenReq.setAppId(globals.getAppId());
        authAPPTokenReq.setDeviceType(1);
        authAPPTokenReq.setGrantType(OAuthGrantType.CLIENT_CREDENTIALS.toString());
        authAPPTokenReq.setUserId("userId");
        authAPPTokenReq.setUserName("userName");
        authAPPTokenReq.setUserAccount("userAccount");
        authAPPTokenReq.setSessionId(request.getRequestedSessionId());
        authAPPTokenReq.setSsoLogoutUrl(globals.getSsoAuthCurrentNoteLogoutUrl());

        HttpEntity<OAuthAPPTokenReq> requestEntity = new HttpEntity<>(authAPPTokenReq);
        ResponseEntity<ResultBean<String>> responseEntity = restTemplate.exchange(
                url, HttpMethod.POST, requestEntity, typeRef);
        ResultBean<String> resultBean = responseEntity.getBody();
        logger.info("认证中心返回token结果; [{}]", resultBean.getData());
        return ExceptionEnum.SUCCESS.getCode() == resultBean.getCode() ? JsonUtil.json2Bean(resultBean.getData(),OAuthToken.class) : null;
    }

    private OAuthToken getAccessCode(String code, String sessionId){
        String urlBuilder = globals.getRequestTokenUrl();
        OAuthWebTokenReq tokenReq = new OAuthWebTokenReq();
        tokenReq.setAppId(globals.getAppId());
        tokenReq.setGrantType(OAuthGrantType.AUTHORIZATION_CODE.toString());
        tokenReq.setAuthCode(code);
        tokenReq.setSessionId(sessionId);
        HttpEntity<OAuthWebTokenReq> requestEntity = new HttpEntity<>(tokenReq);
        ParameterizedTypeReference<ResultBean<String>> typeRef = new ParameterizedTypeReference<ResultBean<String>>() {};
        ResponseEntity<ResultBean<String>> responseEntity = restTemplate.exchange(
                urlBuilder.toString(), HttpMethod.POST, requestEntity , typeRef);
        ResultBean<String> resultBean = responseEntity.getBody();
        logger.info("认证中心返回token结果; [{}]", resultBean.getData());
        return ExceptionEnum.SUCCESS.getCode() == resultBean.getCode() ? JsonUtil.json2Bean(resultBean.getData(),OAuthToken.class) : null;
    }
}
