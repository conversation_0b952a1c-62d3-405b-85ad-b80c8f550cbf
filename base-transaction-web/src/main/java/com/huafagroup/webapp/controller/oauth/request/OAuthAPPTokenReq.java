package com.huafagroup.webapp.controller.oauth.request;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

public class OAuthAPPTokenReq {
    @NotEmpty(message="appId不能为空")
    private String appId;

    private Set<String> scope;

    @NotEmpty(message="grantType不能为空")
    private String grantType;

    @NotNull(message="deviceType不能为空")
    private Integer deviceType;

    @NotEmpty(message="userId不能为空")
    private String userId;

    @NotEmpty(message="userName不能为空")
    private String userName;

    @NotEmpty(message="userAccount不能为空")
    private String userAccount;

    @NotEmpty(message="sessionId不能为空")
    private String sessionId;

    @NotEmpty(message="ssoLogoutUrl不能为空")
    private String ssoLogoutUrl;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Set<String> getScope() {
        return scope;
    }

    public void setScope(Set<String> scope) {
        this.scope = scope;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public Integer getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Integer deviceType) {
        this.deviceType = deviceType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getSsoLogoutUrl() {
        return ssoLogoutUrl;
    }

    public void setSsoLogoutUrl(String ssoLogoutUrl) {
        this.ssoLogoutUrl = ssoLogoutUrl;
    }

    @Override
    public String toString() {
        return "OAuthAPPTokenReq{" +
                "appId='" + appId + '\'' +
                ", scope=" + scope +
                ", grantType='" + grantType + '\'' +
                ", deviceType=" + deviceType +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", userAccount='" + userAccount + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", ssoLogoutUrl='" + ssoLogoutUrl + '\'' +
                '}';
    }
}
