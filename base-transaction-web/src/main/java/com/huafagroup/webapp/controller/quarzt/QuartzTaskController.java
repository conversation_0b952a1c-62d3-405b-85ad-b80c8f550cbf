//package com.huafa.webapp.controller.quarzt;
//
//import SysTLog;
//import QuartzTask;
//import com.huafa.webapp.quartz.service.impl.QuartzTaskServiceImpl;
//import PageBean;
//import PageResultBean;
//import RestResponse;
//import ResultBean;
//import org.apache.shiro.authz.annotation.RequiresPermissions;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.Model;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.util.WebUtils;
//
//import javax.servlet.ServletRequest;
//import java.util.List;
//import java.util.Map;
//
///**
// * <p>
// * 定时任务  前端控制器
// * </p>
// *
// *
// */
//@Controller
//@RequestMapping("/admin/quartzTask")
//public class QuartzTaskController {
//    private static final Logger LOGGER = LoggerFactory.getLogger(QuartzTaskController.class);
//
//
//    @Autowired
//    private QuartzTaskServiceImpl quartzTaskService;
//
//    @GetMapping("list")
//    @SysTLog("跳转定时任务列表")
//    public String list(){
//        return "/admin/quartzTask/list";
//    }
//
//    @RequiresPermissions("quartz:task:list")
//    @PostMapping("list")
//    @ResponseBody
//    public ResultBean<QuartzTask> list(@RequestParam(value = "page",defaultValue = "1")Integer page,
//                                       @RequestParam(value = "limit",defaultValue = "10")Integer limit,
//                                       ServletRequest request){
//        Map map = WebUtils.getParametersStartingWith(request, "s_");
//
//        PageBean pageBean = new PageBean();
//        pageBean.setPageNum(page);
//        pageBean.setPageSize(limit);
//
//
//        PageResultBean pageData = quartzTaskService.queryList(map, pageBean);
//
//        ResultBean resultBean = new ResultBean();
//        resultBean.setData(pageData.getData());
//        resultBean.setCount((int) pageData.getTotal());
//        return resultBean;
//    }
//
//    @GetMapping("add")
//    public String add(){
//        return "/admin/quartzTask/add";
//    }
//
//    @RequiresPermissions("quartz:task:add")
//    @PostMapping("add")
//    @SysTLog("保存新增定时任务数据")
//    @ResponseBody
//    public RestResponse add(QuartzTask quartzTask){
//        quartzTaskService.saveQuartzTask(quartzTask);
//        return RestResponse.success();
//    }
//
//    @GetMapping("edit")
//    public String edit(String id,Model model){
//        QuartzTask quartzTask = quartzTaskService.selectById(id);
//        model.addAttribute("quartzTask",quartzTask);
//        return "/admin/quartzTask/edit";
//    }
//
//    @RequiresPermissions("quartz:task:edit")
//    @PostMapping("edit")
//    @ResponseBody
//    @SysTLog("保存编辑定时任务数据")
//    public RestResponse edit(QuartzTask quartzTask){
//        if(null == quartzTask.getId() || quartzTask.getId().equals("")){
//            return RestResponse.failure("ID不能为空");
//        }
//        quartzTaskService.updateQuartzTask(quartzTask);
//        return RestResponse.success();
//    }
//
//    @RequiresPermissions("quartz:task:delete")
//    @PostMapping("delete")
//    @ResponseBody
//    @SysTLog("删除定时任务数据")
//    public RestResponse delete(@RequestParam(value = "ids[]",required = false)List<String> ids){
//        if(null == ids || 0 == ids.size()){
//            return RestResponse.failure("ID不能为空");
//        }
//        quartzTaskService.deleteBatchTasks(ids);
//        return RestResponse.success();
//    }
//
//    /**
//     * 暂停选中的定时任务
//     * @param ids 任务ID List
//     * @return
//     */
//    @RequiresPermissions("quartz:task:paush")
//    @PostMapping("paush")
//    @ResponseBody
//    public RestResponse paush(@RequestParam(value = "ids[]",required = false)List<String> ids){
//        if(null == ids || 0 == ids.size()){
//            return RestResponse.failure("ID不能为空");
//        }
//        quartzTaskService.paush(ids);
//        return RestResponse.success();
//    }
//
//    /**
//     * 恢复选中的定时任务运行
//     * @param ids 任务ID List
//     * @return
//     */
//    @RequiresPermissions("quartz:task:resume")
//    @PostMapping("resume")
//    @ResponseBody
//    public RestResponse resume(@RequestParam(value = "ids[]",required = false)List<String> ids){
//        if(null == ids || 0 == ids.size()){
//            return RestResponse.failure("ID不能为空");
//        }
//        quartzTaskService.resume(ids);
//        return RestResponse.success();
//    }
//
//    /**
//     * 立即执行选中的定时任务
//     * @param ids 任务ID List
//     * @return
//     */
//    @RequiresPermissions("quartz:task:run")
//    @PostMapping("run")
//    @ResponseBody
//    public RestResponse run(@RequestParam(value = "ids[]",required = false)List<String> ids){
//        if(null == ids || 0 == ids.size()){
//            return RestResponse.failure("ID不能为空");
//        }
//        quartzTaskService.run(ids);
//        return RestResponse.success();
//    }
//
//}