package com.huafagroup.webapp.controller.oauth.response;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class OAuthToken {

    private String accessToken;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expiresIn;

    private String appId;

    private String refreshToken;

    private String grantType;

    private String userId;

    private String userName;

    private String userAccount;

    private Boolean isSuperAdmin;

    public Boolean getIsSuperAdmin() {
        return isSuperAdmin;
    }

    public void setIsSuperAdmin(Boolean isSuperAdmin) {
        this.isSuperAdmin = isSuperAdmin;
    }

    public OAuthToken(){}

    public OAuthToken(String appId, String grantType, String accessToken, String refreshToken, Date expiresIn, String userId, String userName, String userAccount) {
        this.accessToken = accessToken;
        this.expiresIn = expiresIn;
        this.appId = appId;
        this.refreshToken = refreshToken;
        this.grantType = grantType;
        this.userId = userId;
        this.userName = userName;
        this.userAccount = userAccount;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Date getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(Date expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    @Override
    public String toString() {
        return "OAuthToken{" +
                "accessToken='" + accessToken + '\'' +
                ", expiresIn=" + expiresIn +
                ", appId='" + appId + '\'' +
                ", refreshToken='" + refreshToken + '\'' +
                ", grantType='" + grantType + '\'' +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", userAccount='" + userAccount + '\'' +
                '}';
    }
}
