//package com.huafa.core.controller.quarzt;
//
//import com.github.pagehelper.Page;
//import com.huafa.core.annotation.SysTLog;
//import QuartzTaskLog;
//import com.huafa.core.quartz.service.impl.QuartzTaskLogServiceImpl;
//import RestResponse;
//import ResultBean;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.shiro.authz.annotation.RequiresPermissions;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.ui.Model;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.util.WebUtils;
//
//import javax.servlet.ServletRequest;
//import java.util.Map;
//
///**
// * <p>
// * 任务执行日志  前端控制器
// * </p>
// *
// *
// */
//@Controller
//@RequestMapping("/admin/quartzTaskLog")
//public class QuartzTaskLogController {
//    private static final Logger LOGGER = LoggerFactory.getLogger(QuartzTaskLogController.class);
//
//    @Autowired
//    private QuartzTaskLogServiceImpl quartzTaskLogService;
//
//    @GetMapping("list")
//    @SysTLog("跳转任务执行日志列表")
//    public String list(){
//        return "/admin/quartzTaskLog/list";
//    }
//
//    @RequiresPermissions("quartz:log:list")
//    @PostMapping("list")
//    @ResponseBody
//    public ResultBean<QuartzTaskLog> list(@RequestParam(value = "page",defaultValue = "1")Integer page,
//                                          @RequestParam(value = "limit",defaultValue = "10")Integer limit,
//                                          ServletRequest request){
//        Map map = WebUtils.getParametersStartingWith(request, "s_");
//        ResultBean<QuartzTaskLog> resultBean = new ResultBean<>();
//        EntityWrapper<QuartzTaskLog> wrapper = new EntityWrapper<>();
//        wrapper.eq("del_flag",false);
//        if(!map.isEmpty()){
//            String name = (String) map.get("name");
//            if(StringUtils.isNotBlank(name)) {
//                wrapper.like("name",name);
//            }else{
//                map.remove("name");
//            }
//
//        }
//        Page<QuartzTaskLog> pageData = quartzTaskLogService.selectPage(new Page<QuartzTaskLog>(page, limit), wrapper);
//        resultBean.setData(pageData.getRecords());
//        resultBean.setCount(pageData.getTotal());
//        return resultBean;
//    }
//
//    @GetMapping("add")
//    public String add(){
//        return "/admin/quartzTaskLog/add";
//    }
//
//    @PostMapping("add")
//    @ResponseBody
//    public RestResponse add(QuartzTaskLog quartzTaskLog){
//        quartzTaskLogService.insert(quartzTaskLog);
//        return RestResponse.success();
//    }
//
//    @GetMapping("edit")
//    public String edit(String id,Model model){
//        QuartzTaskLog quartzTaskLog = quartzTaskLogService.selectById(id);
//        model.addAttribute("quartzTaskLog",quartzTaskLog);
//        return "/admin/quartzTaskLog/edit";
//    }
//
//    @PostMapping("edit")
//    @ResponseBody
//    public RestResponse edit(QuartzTaskLog quartzTaskLog){
//        if(null == quartzTaskLog.getId() ||quartzTaskLog.getId().equals("")){
//            return RestResponse.failure("ID不能为空");
//        }
//        quartzTaskLogService.updateById(quartzTaskLog);
//        return RestResponse.success();
//    }
//
//    @RequiresPermissions("quartz:log:delete")
//    @PostMapping("delete")
//    @ResponseBody
//    @SysTLog("删除任务执行日志数据")
//    public RestResponse delete(@RequestParam(value = "id",required = false)String id){
//        if(null == id || id.equals("")){
//            return RestResponse.failure("ID不能为空");
//        }
//        QuartzTaskLog quartzTaskLog = quartzTaskLogService.selectById(id);
//        quartzTaskLog.setDelFlag(true);
//        quartzTaskLogService.updateById(quartzTaskLog);
//        return RestResponse.success();
//    }
//
//}