package com.huafagroup.webapp.aspect;

import com.huafagroup.core.util.JsonUtil;
import com.huafagroup.core.util.ToolUtil;
import com.huafagroup.webapp.annotation.SysTLog;
import com.huafagroup.webapp.base.MySysUser;
import com.huafagroup.webapp.config.CommonExceptionHandler;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.lang.reflect.Method;
import java.util.HashMap;

/**
 * Created by wangl on 2018/1/13.
 * todo:
 */
@Aspect
@Order(5)
@Component
public class WebLogAspect {

    private ThreadLocal<Long> startTime = new ThreadLocal<>();

    @Autowired
    private CommonExceptionHandler exceptionHandle;

    @Autowired
    private HttpServletRequest request;

    private com.huafagroup.core.entity.SysTLog sysTLog = null;

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Pointcut("@annotation(com.huafagroup.webapp.annotation.SysTLog)")
    public void webLog() {
    }

    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) {
        startTime.set(System.currentTimeMillis());
        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        HttpSession session = (HttpSession) attributes.resolveReference(RequestAttributes.REFERENCE_SESSION);
        sysTLog = new com.huafagroup.core.entity.SysTLog();
        sysTLog.setClassMethod(joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());
        sysTLog.setHttpMethod(request.getMethod());
        //获取传入目标方法的参数
        Object[] args = joinPoint.getArgs();
        for (int i = 0; i < args.length; i++) {
            Object o = args[i];
            if (o instanceof ServletRequest || (o instanceof ServletResponse) || o instanceof MultipartFile) {
                args[i] = o.toString();
            }
        }
        String str = JsonUtil.bean2Json(args);
        sysTLog.setParams(str.length() > 5000 ? JsonUtil.bean2Json("请求参数数据过长不与显示") : str);
        String ip = ToolUtil.getClientIp(request);
        if ("0.0.0.0".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip) || "localhost".equals(ip) || "127.0.0.1".equals(ip)) {
            ip = "127.0.0.1";
        }
        sysTLog.setRemoteAddr(ip);
        sysTLog.setRequestUri(request.getRequestURL().toString());
        if (session != null) {
            sysTLog.setSessionId(session.getId());
        }
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        SysTLog mylog = method.getAnnotation(SysTLog.class);
        if (mylog != null) {
            //注解上的描述
            sysTLog.setTitle(mylog.value());
        }

        HashMap<String, String> browserMap = ToolUtil.getOsAndBrowserInfo(request);
        sysTLog.setBrowser(browserMap.get("os") + "-" + browserMap.get("browser"));

        if (!"127.0.0.1".equals(ip)) {
            if (session == null) {
                return;
            }
            HashMap<String, String> map = (HashMap<String, String>) session.getAttribute("addressIp");
            //if (map == null) {
            //    map = ToolUtil.getAddressByIP(ToolUtil.getClientIp(request));
            //    session.setAttribute("addressIp", map);
            //}
            //sysTLog.setArea(map.get("area"));
            //sysTLog.setProvince(map.get("province"));
            //sysTLog.setCity(map.get("city"));
            //sysTLog.setIsp(map.get("isp"));
        }

        //sysTLog.setId(UUID.randomUUID().toString().replace("-", ""));
        //sysTLog.setType(ToolUtil.isAjax(request) ? "Ajax请求" : "普通请求");
        //if (MySysUser.ShiroUser() != null) {
        //    sysTLog.setUsername(StringUtils.isNotBlank(MySysUser.nickName()) ? MySysUser.nickName() : MySysUser.loginName());
        //}
    }

    @Around("webLog()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        try {
            Object obj = proceedingJoinPoint.proceed();
            return obj;
        } catch (Exception e) {
            e.printStackTrace();
            sysTLog.setException(e.getMessage());
            throw e;
        }
    }

    @AfterReturning(returning = "ret", pointcut = "webLog()")
    public void doAfterReturning(Object ret) {
        if (MySysUser.id(request) != null) {
            sysTLog.setUsername(MySysUser.nickName(request));
        }
        String retString = JsonUtil.bean2Json(ret);
        sysTLog.setResponse(retString.length() > 5000 ? JsonUtil.bean2Json("请求参数数据过长不与显示") : retString);
        sysTLog.setUseTime(System.currentTimeMillis() - startTime.get());

        logger.debug(JsonUtil.bean2Json(sysTLog));

    }
}
