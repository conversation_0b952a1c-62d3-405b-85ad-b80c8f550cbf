#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 264241152 bytes for Failed to commit area from 0x0000000704e00000 to 0x0000000714a00000 of length 264241152.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./src/hotspot/os/windows/os_windows.cpp:3301), pid=185184, tid=185216
#
# JRE version:  (11.0.10+8) (build )
# Java VM: OpenJDK 64-Bit Server VM (11.0.10+8-b1145.96, mixed mode, sharing, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: org.jetbrains.git4idea.http.GitAskPassApp Username for 'http://gitlab.vendor.huafagroup.com': 

Host: 11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz, 8 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.1889)
Time: Thu Sep 22 20:48:26 2022 �й���׼ʱ�� elapsed time: 0.027078 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000019606160800):  JavaThread "Unknown thread" [_thread_in_vm, id=185216, stack(0x0000004f2b400000,0x0000004f2b500000)]

Stack: [0x0000004f2b400000,0x0000004f2b500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x5ece6a]
V  [jvm.dll+0x722cd5]
V  [jvm.dll+0x72422d]
V  [jvm.dll+0x7248e3]
V  [jvm.dll+0x2428d8]
V  [jvm.dll+0x5ea244]
V  [jvm.dll+0x5df2e5]
V  [jvm.dll+0x2fdddb]
V  [jvm.dll+0x2fdd4a]
V  [jvm.dll+0x2fdc22]
V  [jvm.dll+0x302b06]
V  [jvm.dll+0x34b033]
V  [jvm.dll+0x34b736]
V  [jvm.dll+0x34b133]
V  [jvm.dll+0x2d87d8]
V  [jvm.dll+0x2d9987]
V  [jvm.dll+0x701817]
V  [jvm.dll+0x70300c]
V  [jvm.dll+0x3585a9]
V  [jvm.dll+0x6e516e]
V  [jvm.dll+0x3c09e3]
V  [jvm.dll+0x3c2c71]
C  [jli.dll+0x5373]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17034]
C  [ntdll.dll+0x526a1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000196046a7010, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000019606179000 GCTaskThread "GC Thread#0" [stack: 0x0000004f2b500000,0x0000004f2b600000] [id=185220]
  0x00000196061d5800 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000004f2b600000,0x0000004f2b700000] [id=185224]
  0x00000196061d7000 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000004f2b700000,0x0000004f2b800000] [id=185228]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffe9f7a8db7]

VM state:not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001960615c300] Heap_lock - owner thread: 0x0000019606160800

Heap address: 0x0000000704e00000, size: 4018 MB, Compressed Oops mode: Non-zero based: 0x0000000704e00000
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

Events (1 events):
Event: 0.010 Loaded shared library C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\zip.dll


Dynamic libraries:
0x00007ff725760000 - 0x00007ff72576a000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\java.exe
0x00007ffeed790000 - 0x00007ffeed988000 	C:\windows\SYSTEM32\ntdll.dll
0x00007ffeec3c0000 - 0x00007ffeec47d000 	C:\windows\System32\KERNEL32.DLL
0x00007ffeeb420000 - 0x00007ffeeb6ee000 	C:\windows\System32\KERNELBASE.dll
0x00007ffeeb240000 - 0x00007ffeeb340000 	C:\windows\System32\ucrtbase.dll
0x00007ffed9770000 - 0x00007ffed9789000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\jli.dll
0x00007ffed9750000 - 0x00007ffed9767000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\VCRUNTIME140.dll
0x00007ffeed140000 - 0x00007ffeed2e0000 	C:\windows\System32\USER32.dll
0x00007ffeeb3f0000 - 0x00007ffeeb412000 	C:\windows\System32\win32u.dll
0x00007ffeecd90000 - 0x00007ffeecdba000 	C:\windows\System32\GDI32.dll
0x00007ffeeb060000 - 0x00007ffeeb16b000 	C:\windows\System32\gdi32full.dll
0x00007ffeeb1a0000 - 0x00007ffeeb23d000 	C:\windows\System32\msvcp_win.dll
0x00007ffededc0000 - 0x00007ffedf05a000 	C:\windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007ffeec840000 - 0x00007ffeec8de000 	C:\windows\System32\msvcrt.dll
0x00007ffeec6e0000 - 0x00007ffeec710000 	C:\windows\System32\IMM32.DLL
0x000000005edd0000 - 0x000000005eddc000 	C:\Program Files (x86)\360\360Safe\safemon\SafeWrapper.dll
0x00007ffeecaf0000 - 0x00007ffeecb9e000 	C:\windows\System32\ADVAPI32.dll
0x00007ffeecef0000 - 0x00007ffeecf8c000 	C:\windows\System32\sechost.dll
0x00007ffeec9c0000 - 0x00007ffeecae5000 	C:\windows\System32\RPCRT4.dll
0x00007ffed9a20000 - 0x00007ffed9ab6000 	C:\Program Files (x86)\360\360Safe\safemon\libzdtp64.dll
0x00007ffeeb890000 - 0x00007ffeebfd3000 	C:\windows\System32\SHELL32.dll
0x00007ffeecd30000 - 0x00007ffeecd85000 	C:\windows\System32\SHLWAPI.dll
0x00007ffeeaa40000 - 0x00007ffeeaa4a000 	C:\windows\SYSTEM32\VERSION.dll
0x00007ffebf500000 - 0x00007ffebf59d000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\msvcp140.dll
0x00007ffe9f4c0000 - 0x00007ffe9ff90000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\server\jvm.dll
0x00007ffeed130000 - 0x00007ffeed138000 	C:\windows\System32\PSAPI.DLL
0x00007ffee1860000 - 0x00007ffee1869000 	C:\windows\SYSTEM32\WSOCK32.dll
0x00007ffeec8e0000 - 0x00007ffeec94b000 	C:\windows\System32\WS2_32.dll
0x00007ffedfb10000 - 0x00007ffedfb37000 	C:\windows\SYSTEM32\WINMM.dll
0x00007ffee9590000 - 0x00007ffee95a2000 	C:\windows\SYSTEM32\kernel.appcore.dll
0x00007ffeda140000 - 0x00007ffeda151000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\verify.dll
0x00007ffee9290000 - 0x00007ffee9474000 	C:\windows\SYSTEM32\DBGHELP.DLL
0x00007ffee1830000 - 0x00007ffee185c000 	C:\windows\SYSTEM32\dbgcore.DLL
0x00007ffeeb6f0000 - 0x00007ffeeb772000 	C:\windows\System32\bcryptPrimitives.dll
0x00007ffed05a0000 - 0x00007ffed05c9000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\java.dll
0x00007ffede770000 - 0x00007ffede77b000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\jimage.dll
0x00007ffed9440000 - 0x00007ffed9458000 	C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin;C:\windows\SYSTEM32;C:\windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;C:\Program Files (x86)\360\360Safe\safemon;C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\jbr\bin\server

VM Arguments:
java_command: org.jetbrains.git4idea.http.GitAskPassApp Username for 'http://gitlab.vendor.huafagroup.com': 
java_class_path (initial): C:/Program Files/JetBrains/IntelliJ IDEA 2020.3.3/plugins/git4idea/lib/git4idea-rt.jar;C:/Program Files/JetBrains/IntelliJ IDEA 2020.3.3/lib/xmlrpc-2.0.1.jar;C:/Program Files/JetBrains/IntelliJ IDEA 2020.3.3/lib/commons-codec-1.14.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4213178368                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_281
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0;C:\windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Git\cmd;D:\Program Files\nodejs;C:\Program Files (x86)\NetSarang\Xshell 7;C:\Program Files (x86)\NetSarang\Xftp 7;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\Java\jdk1.8.0_281\bin;C:\Users\<USER>\AppData\Local\Programs\Fiddler
USERNAME=JueSu
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10 , 64 bit Build 19041 (10.0.19041.1889)
OS uptime: 7 days 20:03 hours

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0xa4, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, evex, sha, fma

Memory: 4k page, system-wide physical 16064M (631M free)
TotalPageFile size 39488M (AvailPageFile size 41M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 61M, peak: 313M

vm_info: OpenJDK 64-Bit Server VM (11.0.10+8-b1145.96) for windows-amd64 JRE (11.0.10+8-b1145.96), built on Feb 18 2021 01:38:16 by "" with MS VC++ 14.0 (VS2015)

END.
