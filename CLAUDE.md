# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Commands
- Build: `mvn clean install`
- Run tests: `mvn test`
- Run a single test: `mvn test -Dtest=TestClassName`

## Architecture
- **Core Module**: Contains business logic, entities, services, and DAO.
- **Web Module**: Handles web controllers, static resources, and templates.
- **API Module**: Provides REST API endpoints and version management.
- **Job Module**: Manages scheduled tasks using Quartz.

## Module Structure
- Each module has its own entry class (e.g., `CoreApplication.java`, `WebappApplication.java`).
- Modules depend on `core` for shared business logic.
- Configuration files are in `src/main/resources`.
- Tests are in `src/test`.