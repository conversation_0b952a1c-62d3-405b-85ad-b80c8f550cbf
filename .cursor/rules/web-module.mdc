---
description: 
globs: 
alwaysApply: false
---
# base-transaction-web 模块说明

该模块为 Web 端应用，主要包含：
- controller：Web 层控制器。
- config/aspect/annotation/base：Web 层相关配置、切面、基础类。
- static/：静态资源（JS、CSS、图片等）。
- templates/：Freemarker 模板文件。
- 入口类 [WebappApplication.java](mdc:../base-transaction-web/src/main/java/com/huafagroup/webapp/WebappApplication.java)
- 资源文件：bootstrap.yml 及静态资源目录结构。
- test 目录下包含 Web 层相关测试。

