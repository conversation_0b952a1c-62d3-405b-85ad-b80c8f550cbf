---
description: 
globs: 
alwaysApply: false
---
# base-transaction-core 模块说明

该模块为核心业务逻辑层，主要包含：
- entity：实体类，映射数据库表。
- dto：数据传输对象。
- service：业务服务接口与实现。
- configuration：核心配置类。
- util：通用工具类。
- enums/exception：枚举与异常定义。
- dao：数据访问层接口。
- 入口类 [CoreApplication.java](mdc:../base-transaction-core/src/main/java/com/huafagroup/core/CoreApplication.java)
- 资源文件：mapper/ 为 MyBatis 映射文件，sql/ 为初始化 SQL。
- test 目录下包含核心功能测试。

