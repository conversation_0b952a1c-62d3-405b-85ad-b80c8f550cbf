package com.huafagroup.restapi.controller;

import com.huafagroup.core.entity.trans.Transaction;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.trans.TransactionService;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.restapi.apiversion.ApiVersion;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
* <p>
    * </p>
*
* <AUTHOR>
* @date 2021-04-19 09:36:54
* @version
*/
@RestController
@RequestMapping(value = "/transaction")
@ApiVersion(1)
public class TransactionController {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private TransactionService service;


    @ApiOperation(value="获取单条记录", notes="根据url的id来获取详细信息")
    @RequestMapping(value = "/get",method = RequestMethod.GET)
    public ResultBean<Transaction> get(String id){
        Transaction item=  service.selectById(id);
        if(item!=null){
            return new ResultBean<Transaction>(item);
        }else {
            return new ResultBean<Transaction>(ExceptionEnum.RESOURCE_NOT_FOUND,"找不到该记录",null);
        }
    }


    @RequestMapping(value = "/getlist",method = RequestMethod.GET)
    public ResultBean<List<Transaction>> getList(){
        List<Transaction> list=  service.selectAll();
        ResultBean<List<Transaction>> resultBean=new ResultBean<List<Transaction>>(list);
        return  resultBean;
    }
}

