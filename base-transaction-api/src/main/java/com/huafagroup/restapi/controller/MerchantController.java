package com.huafagroup.restapi.controller;

import com.huafagroup.core.dto.CreateOrUpdateMerchantDTO;
import com.huafagroup.core.dto.QueryMerchantDTO;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.enums.ChannelEnum;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.trans.MerchantService;
import com.huafagroup.core.util.PageBean;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.restapi.apiversion.ApiVersion;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-16 16:49:29
 */
@RestController
@RequestMapping(value = "/merchant")
@ApiVersion(1)
public class MerchantController {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private MerchantService service;


    @ApiOperation(value = "获取单条记录", notes = "根据url的id来获取详细信息")
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public ResultBean<Merchant> get(Integer id) {
        return new ResultBean<>(service.selectById(id));
    }


    @RequestMapping(value = "/getlist", method = RequestMethod.POST)
    public ResultBean<PageBean<Merchant>> getList(@RequestBody QueryMerchantDTO dto) {
        return new ResultBean<>(service.selectAll(dto));
    }

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultBean<Integer> create(@RequestBody @Validated CreateOrUpdateMerchantDTO item) {
        return new ResultBean<>(service.create(item));
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultBean<Boolean> update(@RequestBody @Validated CreateOrUpdateMerchantDTO item) {
        return new ResultBean<>(service.update(item));

    }

    @RequestMapping(value = "/deleteByID", method = RequestMethod.POST)
    public ResultBean<Integer> delete(Integer id) {
        int result = service.deleteLogicById(id);
        ResultBean<Integer> resultBean = new ResultBean<Integer>(result);
        return resultBean;
    }

    @RequestMapping(value = "/getByProjectId", method = RequestMethod.GET)
    public ResultBean<Merchant> getByProjectId(@RequestParam("projectId") String projectId, ChannelEnum channel) {
        Merchant item = service.getByProjectId(projectId,channel);
        Merchant mer = new Merchant();
        mer.setMchId(item.getMchId());
        mer.setMchName(item.getMchName());
        return item != null ? new ResultBean<Merchant>(mer)
                : new ResultBean<Merchant>(ExceptionEnum.RESOURCE_NOT_FOUND, "找不到该记录", null);
    }

}
