package com.huafagroup.restapi.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huafagroup.core.dto.*;
import com.huafagroup.core.dto.wx.MiniProHbResultVO;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.service.trans.HbOrderService;
import com.huafagroup.core.service.trans.TransService;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.restapi.apiversion.ApiVersion;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.RequestEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/transaction-api")
@ApiVersion(1)
public class TransController {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TransService service;

    @Autowired
    private HbOrderService hbOrderService;

    @ApiOperation("统一支付接口")
    @PostMapping("/pay")
    public ResultBean<? extends PayOrderResultVO> pay(@RequestBody @Validated PayParamsVO orderParamsVO) throws Exception {
        PayOrderResultVO orderResultVO = service.pay(orderParamsVO);
        ResultBean<PayOrderResultVO> resultBean = new ResultBean<>(orderResultVO);
        return resultBean;
    }

    @ApiOperation("统一支付接口（H5）")
    @PostMapping("/pay/h5")
    public ResultBean<? extends PayOrderResultVO> h5Pay(@RequestBody @Validated PayParamsVO orderParamsVO) throws Exception {
        orderParamsVO.setApiTypeEnum(ApiTypeEnum.H5_API_PAY);
        //h5支付，使用客户端ip
        orderParamsVO.setOpenid(orderParamsVO.getClientIp());
        PayOrderResultVO orderResultVO = service.pay(orderParamsVO);
        ResultBean<PayOrderResultVO> resultBean = new ResultBean<>(orderResultVO);
        return resultBean;
    }

    @ApiOperation("统一支付接口（App）")
    @PostMapping("/pay/app")
    public ResultBean<? extends PayOrderResultVO> appPay(@RequestBody @Validated PayParamsVO orderParamsVO) throws Exception {
        orderParamsVO.setApiTypeEnum(ApiTypeEnum.APP_API_PAY);
        //app支付
        orderParamsVO.setOpenid("AppPayer");
        PayOrderResultVO orderResultVO = service.pay(orderParamsVO);
        ResultBean<PayOrderResultVO> resultBean = new ResultBean<>(orderResultVO);
        return resultBean;
    }

    @ApiOperation("支付通知接口")
    @PostMapping("/pay/notify/{orderNo}")
    public NotifyResult payNotify(RequestEntity<ObjectNode> requestEntity,@PathVariable String orderNo) throws Exception {

        NotifyResult result = service.payNotify(requestEntity,orderNo);

        return result;
    }

    @ApiOperation("查询支付结果接口")
    @GetMapping("/pay/result")
    public ResultBean<? extends BizNotifyResult> getPayResult(@RequestParam String orderNo) throws Exception {
        BizNotifyResult orderResultVO = service.getPayResult(orderNo,false);
        ResultBean<BizNotifyResult> resultBean = new ResultBean<>(orderResultVO);
        return resultBean;
    }

    @ApiOperation("申请退款")
    @PostMapping("/refunds")
    public ResultBean<RefundsResultVO> refunds(@RequestBody @Validated RefundParamsVO paramsVO) throws Exception {
        RefundsResultVO resultVO = service.refund(paramsVO);
        ResultBean<RefundsResultVO> resultBean = new ResultBean<>(resultVO);
        return resultBean;
    }

    @ApiOperation("查询退款结果接口")
    @GetMapping("/refunds/result")
    public ResultBean<? extends BizNotifyResult> getRefundResult(@RequestParam String orderNo) throws Exception {
        BizNotifyResult resultVO = service.getRefundResult(orderNo, false);
        ResultBean<BizNotifyResult> resultBean = new ResultBean<>(resultVO);
        return resultBean;
    }

    @ApiOperation("退款通知接口")
    @PostMapping("/refunds/notify/{orderNo}")
    public NotifyResult refundsNotify(RequestEntity<ObjectNode> requestEntity,@PathVariable String orderNo) throws Exception {
        NotifyResult result = service.refundNotify(requestEntity,orderNo);
        return result;
    }

    @ApiOperation("激励接口（公众号）")
    @PostMapping("/transfer")
    public ResultBean<TransferResultVO> transfer(@RequestBody @Validated TransferParamsVO paramsVO) throws Exception {
        logger.info("发起激励请求:{}", JSONUtil.toJsonStr(paramsVO));
        paramsVO.setApiTypeEnum(ApiTypeEnum.PROMOTION_TRANSFERS_V2);
        TransferResultVO resultVO = service.transfer(paramsVO);
        ResultBean<TransferResultVO> resultBean = new ResultBean<>(resultVO);
        return resultBean;
    }

    @ApiOperation("激励接口（小程序）")
    @PostMapping("/sendminiprogramhb")
    public ResultBean<MiniProHbResultVO> sendminiprogramhb(@RequestBody @Validated TransferParamsVO paramsVO) throws Exception {
        logger.info("发起激励请求(小程序):{}", JSONUtil.toJsonStr(paramsVO));
        paramsVO.setApiTypeEnum(ApiTypeEnum.PROMOTION_SENDMINIPROGRAMHB_V2);
        MiniProHbResultVO resultVO = (MiniProHbResultVO) service.transfer(paramsVO);
        ResultBean<MiniProHbResultVO> resultBean = new ResultBean<>(resultVO);
        return resultBean;
    }

    @ApiOperation("关闭支付订单接口")
    @PostMapping("/close")
    public ResultBean close(@RequestParam String orderNo) throws Exception {
        //没异常表明关单成功
        service.close(orderNo);
        ResultBean result = new ResultBean();
        return result;
    }

    @ApiOperation("同步一天内的发红包状态")
    @GetMapping("/syncHbStatus")
    public ResultBean syncStatus() throws Exception {
        List<HbOrder> hbOrders = hbOrderService.selectForSyncStatus(1);
        for (HbOrder hbOrder : hbOrders) {
            TransferResultVO resultVO = service.syncHbOrderStatus(hbOrder);
        }
        return new ResultBean();
    }

    @ApiOperation("获取公众号发红包状态")
    @GetMapping("/transfer/result")
    public ResultBean<TransferResultVO> getTransferResult(@RequestParam String orderNo) throws Exception {
        HbOrder hbOrder = hbOrderService.selectByNo(orderNo);
        String status = hbOrder.getStatus();
        TransferResultVO resultVO = null;
        if (OrderStatusEnum.RECEIVED.name().equals(status)
                || OrderStatusEnum.REFUND.name().equals(status)) {
            //已经有确定状态，则直接返回
            resultVO = new TransferResultVO(orderNo,status,
                    DateUtil.formatDateTime(hbOrder.getSuccessTime()));
        } else {
            resultVO = service.syncHbOrderStatus(hbOrder);
        }
        return new ResultBean(resultVO);
    }
}
