//package com.huafagroup.restapi.controller.uc;
//
//import cn.hutool.core.util.StrUtil;
//import com.huafagroup.core.entity.uc.UserAddDTO;
//import com.huafagroup.core.entity.uc.UserBasic;
//import com.huafagroup.core.entity.uc.UserThirdPartyDTO;
//import com.huafagroup.core.entity.uc.UserUpdateDTO;
//import com.huafagroup.core.exception.ExceptionEnum;
//import com.huafagroup.core.service.uc.UserBasicService;
//import com.huafagroup.core.service.uc.UserThirdPartyAuthService;
//import com.huafagroup.core.util.ResultBean;
//import io.swagger.annotations.ApiOperation;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * <p>
// * </p>
// *
// * <AUTHOR>
// * @date 2019-07-16 16:50:59
// */
//@RestController
//@RequestMapping(value = "/userbasic")
//public class UserBasicController {
//
//    Logger logger = LoggerFactory.getLogger(this.getClass());
//    @Autowired
//    private UserBasicService service;
//    @Autowired
//    private UserThirdPartyAuthService userThirdPartyAuthService;
//
//    @ApiOperation("用户注册接口")
//    @RequestMapping(value = "/register", method = RequestMethod.POST)
//    public ResultBean<UserBasic> register(@RequestBody @Validated UserAddDTO userAddDTO) {
//        if (StrUtil.isAllEmpty(userAddDTO.getAccount(), userAddDTO.getTel())) {
//            return new ResultBean<>(ExceptionEnum.PARAMS_NOT_VALID,  "用户名、手机号码不能同时为空！", null);
//        }
//        if(service.hasRegister(userAddDTO)){
//            return new ResultBean<>(ExceptionEnum.PARAMS_NOT_VALID, "用户名或手机号码已存在！", null);
//        }
//        UserBasic userBasic = service.insertUser(userAddDTO);
//        ResultBean<UserBasic> resultBean = new ResultBean<>(userBasic);
//        return resultBean;
//    }
//
//    @ApiOperation("用户基本信息更新接口")
//    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    public ResultBean<String> update(@RequestBody @Validated UserUpdateDTO userUpdateDTO) {
//        int result = service.updateUser(userUpdateDTO);
//        ResultBean<String> resultBean = new ResultBean<>(String.valueOf(result));
//        return resultBean;
//    }
//
//    @ApiOperation("账号密码验证接口")
//    @RequestMapping(value = "/checkPass", method = RequestMethod.POST)
//    public ResultBean<UserBasic> checkPass(String account, String password) {
//        UserBasic result = service.checkPass(account, password);
//        if(result == null){
//            return new ResultBean<>(ExceptionEnum.RESOURCE_NOT_FOUND, "账号密码验证不通过！", null);
//        }else {
//            return new ResultBean<>(result);
//        }
//    }
//
//    @ApiOperation("根据用户Id查询用户基本信息接口")
//    @RequestMapping(value = "/getUserById", method = RequestMethod.GET)
//    public ResultBean<UserBasic> getUserById(String id) {
//
//        UserBasic param = new UserBasic();
//        param.setId(id);
//        UserBasic item = service.selectByParam(param);
//        if (item != null) {
//            return new ResultBean<UserBasic>(item);
//        } else {
//            return new ResultBean<UserBasic>(ExceptionEnum.RESOURCE_NOT_FOUND,  "找不到该记录", null);
//        }
//    }
//
//    @ApiOperation("根据手机号码查询用户基本信息接口")
//    @RequestMapping(value = "/getUserByTel", method = RequestMethod.GET)
//    public ResultBean<UserBasic> getUserByTel(String tel) {
//
//        UserBasic param = new UserBasic();
//        param.setTel(tel);
//        UserBasic item = service.selectByParam(param);
//        if (item != null) {
//            return new ResultBean<UserBasic>(item);
//        } else {
//            return new ResultBean<UserBasic>(ExceptionEnum.RESOURCE_NOT_FOUND, "找不到该记录", null);
//        }
//    }
//
//    @ApiOperation("用户绑定第三方认证信息接口")
//    @RequestMapping(value = "/bindThirdParty", method = RequestMethod.POST)
//    public ResultBean<String> bindThirdParty(@RequestBody @Validated UserThirdPartyDTO thirdPartyDTO) {
//        if(userThirdPartyAuthService.hasBind(thirdPartyDTO)){
//            return new ResultBean<>(ExceptionEnum.PARAMS_NOT_VALID,  "该账号已绑定！", null);
//        }
//        if (getUserById(thirdPartyDTO.getUserId()).getData() == null){
//            return new ResultBean<>(ExceptionEnum.RESOURCE_NOT_FOUND,  "账号不存在！", null);
//        }
//        int result = userThirdPartyAuthService.bindThirdParty(thirdPartyDTO);
//        ResultBean<String> resultBean = new ResultBean<>(String.valueOf(result));
//        return resultBean;
//    }
//
//    @ApiOperation("根据unionId查询用户基本信息接口")
//    @RequestMapping(value = "/getUserByUnionId", method = RequestMethod.GET)
//    public ResultBean<UserBasic> getUserByUnionId(String unionId) {
//
//        UserBasic item = service.selectByUnionId(unionId);
//        if (item != null) {
//            return new ResultBean<UserBasic>(item);
//        } else {
//            return new ResultBean<UserBasic>(ExceptionEnum.RESOURCE_NOT_FOUND, "找不到该记录", null);
//        }
//    }
//
//}
//
