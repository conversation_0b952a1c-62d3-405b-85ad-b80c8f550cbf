package com.huafagroup.restapi.controller;

import com.huafagroup.core.dto.douyin.DouyinPayParamsVO;
import com.huafagroup.core.dto.douyin.OrderInfo;
import com.huafagroup.core.service.trans.DouYinPayService;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.restapi.apiversion.ApiVersion;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/***
 *  抖音支付控制器
 */
@RestController
@RequestMapping(value = "/transaction/douYin")
@ApiVersion(1)
public class DouYinPayController {
    private static final Logger LOGGER = LoggerFactory.getLogger(DouYinPayController.class);
    @Autowired
    private DouYinPayService douYinPayService;


    @ApiOperation("抖音支付接口")
    @PostMapping("/pay")
    public ResultBean<OrderInfo> pay(@RequestBody @Validated DouyinPayParamsVO orderParamsVO) throws Exception {
        LOGGER.info("收到抖音支付(微信)请求:{}", orderParamsVO);
        OrderInfo orderInfo = douYinPayService.pay(orderParamsVO);
        ResultBean<OrderInfo> resultBean = new ResultBean<>(orderInfo);
        return resultBean;
    }
}

