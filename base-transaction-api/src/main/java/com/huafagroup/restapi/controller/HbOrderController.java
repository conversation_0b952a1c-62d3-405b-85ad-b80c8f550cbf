package com.huafagroup.restapi.controller;

import com.huafagroup.core.service.trans.HbOrderService;
import com.huafagroup.restapi.apiversion.ApiVersion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
* <p>
    * </p>
*
* <AUTHOR>
* @date 2021-04-16 10:38:34
* @version
*/
@RestController
@RequestMapping(value = "/hborder")
@ApiVersion(1)
public class HbOrderController {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private HbOrderService service;

}

