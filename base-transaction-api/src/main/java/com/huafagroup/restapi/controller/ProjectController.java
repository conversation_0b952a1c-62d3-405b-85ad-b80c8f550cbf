package com.huafagroup.restapi.controller;

import com.huafagroup.core.dto.OrgTreeDto;
import com.huafagroup.core.dto.ProjectMerchantVO;
import com.huafagroup.core.dto.QueryProjectDTO;
import com.huafagroup.core.entity.trans.Project;
import com.huafagroup.core.enums.ProjectTypeEnum;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.trans.ProjectService;
import com.huafagroup.core.util.PageBean;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.restapi.apiversion.ApiVersion;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-16 16:50:39
 */
@RestController
@RequestMapping(value = "/project")
@ApiVersion(1)
public class ProjectController {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private HttpServletResponse response;
    @Autowired
    private ProjectService service;


    @ApiOperation(value = "获取单条记录", notes = "根据url的id来获取详细信息")
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public ResultBean<Project> get(String id) {
        Project item = service.selectById(id);
        if (item != null) {
            return new ResultBean<Project>(item);
        } else {
            return new ResultBean<Project>(ExceptionEnum.RESOURCE_NOT_FOUND, "找不到该记录", null);
        }
    }


    @RequestMapping(value = "/getlist", method = RequestMethod.POST)
    public ResultBean<PageBean<ProjectMerchantVO>> getList(@RequestBody QueryProjectDTO dto) {
        return new ResultBean<>(service.selectAll(dto));

    }

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResultBean<String> create(@RequestBody @Validated Project item) {
        int result = service.insert(item);
        ResultBean<String> resultBean = new ResultBean<String>("");
        return resultBean;
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultBean<String> update(@RequestBody @Validated Project item) {
        int result = service.update(item);
        ResultBean<String> resultBean = new ResultBean<String>("");
        return resultBean;
    }

    @RequestMapping(value = "/deleteByID", method = RequestMethod.POST)
    public ResultBean<Integer> delete(String id) {
        int result = service.deleteById(id);
        ResultBean<Integer> resultBean = new ResultBean<Integer>(result);
        return resultBean;
    }

    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResultBean<Integer> delete(@RequestBody @Validated Project item) {
        int result = service.delete(item);
        ResultBean<Integer> resultBean = new ResultBean<Integer>(result);
        return resultBean;
    }

    @RequestMapping(value = "/area", method = RequestMethod.GET)
    public ResultBean<List<Project>> getArea() {
        List<Project> list = service.getArea();
        ResultBean<List<Project>> resultBean = new ResultBean<List<Project>>(list);
        return resultBean;
    }

    @RequestMapping(value = "/getCityOrOrgOrProject", method = RequestMethod.GET)
    public ResultBean<List<Project>> getCityOrOrgOrProject(@RequestParam String type, @RequestParam String parentId) {

        Integer tag = ProjectTypeEnum.getCodeByTag(type);
        if (tag < 1) {
            return new ResultBean<List<Project>>(ExceptionEnum.RESOURCE_NOT_FOUND, "找不到该记录", null);
        }
        List<Project> list = service.getCityOrOrgOrProject(parentId, tag);
        ResultBean<List<Project>> resultBean = new ResultBean<List<Project>>(list);
        return resultBean;
    }

    @RequestMapping(value = "/getOrgTree", method = RequestMethod.GET)
    public ResultBean<List<OrgTreeDto>> getOrgTree(@RequestParam(value = "orgIds")List<String> orgIds) {
        List<OrgTreeDto> list = service.getOrgTree(orgIds);
        ResultBean<List<OrgTreeDto>> resultBean = new ResultBean<List<OrgTreeDto>>(list);
        return resultBean;
    }

}
