package com.huafagroup.restapi.controller;

import cn.hutool.core.util.StrUtil;
import com.huafagroup.core.dto.BindDTO;
import com.huafagroup.core.entity.trans.Account;
import com.huafagroup.core.enums.ChannelEnum;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.trans.AccountService;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.restapi.apiversion.ApiVersion;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-16 16:42:15
 */
@RestController
@RequestMapping(value = "/account")
@ApiVersion(1)
public class AccountController {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private AccountService service;

    @ApiOperation(value = "获取单条记录", notes = "根据url的id来获取详细信息")
    @RequestMapping(value = "/get", method = RequestMethod.GET)
    public ResultBean<Account> get(String id) {
        Account item = service.selectById(id);
        if (item != null) {
            return new ResultBean<Account>(item);
        } else {
            return new ResultBean<Account>(ExceptionEnum.RESOURCE_NOT_FOUND, "找不到该记录", null);
        }
    }

    @RequestMapping(value = "/getlist", method = RequestMethod.GET)
    public ResultBean<List<Account>> getList() {
        List<Account> list = service.selectAll();
        ResultBean<List<Account>> resultBean = new ResultBean<List<Account>>(list);
        return resultBean;
    }

    @RequestMapping(value = "/bind", method = RequestMethod.POST)
    public ResultBean<Integer> bind(@RequestBody @Validated BindDTO dto) {
        return new ResultBean<>(service.bind(dto));

    }

    @RequestMapping(value = "/unbind", method = RequestMethod.POST)
    public ResultBean<Boolean> unbind(@RequestParam Integer id) {
        return new ResultBean<>(service.unbind(id));
    }

    @RequestMapping(value = "/getByProjectId", method = RequestMethod.GET)
    public ResultBean<Account> getByProjectId(@RequestParam("projectId")String projectId, ChannelEnum channel) {
        Account item = service.getForPay(projectId,channel);
        return item != null ? new ResultBean<Account>(item)
                : new ResultBean<Account>(ExceptionEnum.RESOURCE_NOT_FOUND, "找不到该记录", null);
    }

    @RequestMapping(value = "/deposit", method = RequestMethod.POST)
    public ResultBean<Integer> deposit(String projectId,ChannelEnum channel,int amount) {
        int result = service.deposit(projectId,channel,amount);
        ResultBean<Integer> resultBean = new ResultBean<Integer>(result);
        return resultBean;
    }

    @PostMapping("/sync")
    public ResultBean<String> sync(String projectId,ChannelEnum channel) {
        long afterBalance = service.sync(projectId,channel);
        String msg = StrUtil.format("同步成功，余额为{}（单位分）",afterBalance);
        ResultBean<String> resultBean = new ResultBean<String>(msg);
        return resultBean;
    }
}
