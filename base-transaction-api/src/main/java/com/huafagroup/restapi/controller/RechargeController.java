package com.huafagroup.restapi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.huafagroup.core.dto.juhe.*;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.service.recharge.JHDataRechargeService;
import com.huafagroup.core.util.ResultBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "/recharge")
public class RechargeController {
    private static final Logger LOGGER = LoggerFactory.getLogger(RechargeController.class);

    @Resource
    private JHDataRechargeService jhDataRechargeService;

    @RequestMapping(value = "/money", method = RequestMethod.POST)
    public ResultBean<RechargeMobileResp> rechargeMoney(@RequestBody @Validated RechargeMobileReq req) throws BusinessException{
        LOGGER.info("收到话费充值请求:{}", req);
        RechargeMobileResp resp = jhDataRechargeService.rechargeMoney(req);
        ResultBean<RechargeMobileResp> resultBean = new ResultBean<>(resp);
        return resultBean;
    }

    @RequestMapping(value = "/moneyCallBack", method = RequestMethod.POST)
    public ResultBean<String> rechargeMoneyBack(RechargeMoneyCallBackReq callBackReq) throws BusinessException{
        LOGGER.info("收到话费充值回调请求:{}", callBackReq);
        jhDataRechargeService.onMoneyCallBack(callBackReq);
        ResultBean<String> resultBean = new ResultBean<>("OK");
        return resultBean;
    }

    @RequestMapping(value = "/flow", method = RequestMethod.POST)
    public ResultBean<RechargeFlowResp> flowRecharge(@RequestBody @Validated RechargeFlowReq req) {
        RechargeFlowResp resp = new RechargeFlowResp();
        BeanUtil.copyProperties(req, resp);
        resp.setCardName("中国电信省内流量套餐10M");
        resp.setAmount("2.10");
        resp.setRechargeOrderId("12345678900987654321");
        return new ResultBean<>(resp);
    }

}
