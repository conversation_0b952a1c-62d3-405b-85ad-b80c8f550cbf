package com.huafagroup.restapi.controller;

import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.trans.OrderService;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.restapi.apiversion.ApiVersion;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
/**
* <p>
    * </p>
*
* <AUTHOR>
* @date 2021-04-12 19:32:58
* @version
*/
@RestController
@RequestMapping(value = "/order")
@ApiVersion(1)
public class OrderController {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private OrderService service;


    @ApiOperation(value="获取单条记录", notes="根据url的id来获取详细信息")
    @RequestMapping(value = "/get",method = RequestMethod.GET)
    public ResultBean<Order> get(String id){
        Order item=  service.selectById(id);
        if(item!=null){
            return new ResultBean<Order>(item);
        }else {
            return new ResultBean<Order>(ExceptionEnum.RESOURCE_NOT_FOUND,"找不到该记录",null);
        }
    }


    @RequestMapping(value = "/getlist",method = RequestMethod.GET)
    public ResultBean<List<Order>> getList(){
        List<Order> list=  service.selectAll();
        ResultBean<List<Order>> resultBean=new ResultBean<List<Order>>(list);
        return  resultBean;
    }
}

