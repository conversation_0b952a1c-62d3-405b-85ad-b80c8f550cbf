package com.huafagroup.restapi.controller;

import com.huafagroup.core.util.ResultBean;
import com.huafagroup.restapi.apiversion.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: wuyekai
 * @description:
 * @datetime: 2019-07-02 14:15
 */
@RestController
@RequestMapping("test")
@ApiVersion(1)
@ApiIgnore
public class TestController {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource(name = "restTemplateLB")
    private RestTemplate restTemplate;

    @ApiOperation("测试之Hello World")
    @GetMapping("hello")
    public String test() {
        logger.info("Api test! " + new Date().toString());
        ParameterizedTypeReference<ResultBean<String>> typeRef = new ParameterizedTypeReference<ResultBean<String>>() {};
        ResponseEntity<ResultBean<String>> responseEntity = restTemplate.exchange(
                "http://office-console/test/hello", HttpMethod.GET,null , typeRef);
        ResultBean<String> myModelClasses = responseEntity.getBody();
        return "Api test! " + new Date().toString() + "  " + myModelClasses.getData();
    }

}