package com.huafagroup.restapi.config;

import cn.hutool.core.util.IdUtil;
import org.slf4j.MDC;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import java.io.IOException;

@WebFilter(filterName = "traceIdFilter", urlPatterns = "/*")
public class TraceIdFilter implements Filter {

    final String traceIdName = "traceId";

    public void init(FilterConfig filterConfig) {
    }

    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        MDC.put(traceIdName, IdUtil.fastSimpleUUID());
        filterChain.doFilter(servletRequest, servletResponse);
    }

    public void destroy() {
        MDC.clear();
    }
}
