package com.huafagroup.restapi.config;

import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.util.ResultBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.util.List;

/*全局异常处理类*/
@RestControllerAdvice
public class CommonExceptionHandler {
    /**
     * logback new instance
     */
    Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 统一处理bean验证抛出的参数校验异常
     * 参数校验失败，统一采用warn记录日志
     * @see javax.validation.Valid
     * @see org.springframework.validation.Validator
     * @see org.springframework.validation.DataBinder
     */
    @ExceptionHandler(BindException.class)
    public ResultBean<List<FieldError>> validExceptionHandler(BindException e, WebRequest request, HttpServletResponse response) {
        List<FieldError> fieldErrors=e.getBindingResult().getFieldErrors();
        return  new ResultBean<>(ExceptionEnum.ARGUMENTS_INVALID, buildValidErrorMsg(fieldErrors), null);

    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultBean<List<FieldError>> validExceptionHandler(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        return new ResultBean<>(ExceptionEnum.ARGUMENTS_INVALID, buildValidErrorMsg(fieldErrors), null);

    }

    private String buildValidErrorMsg(List<FieldError> fieldErrors) {
        StringBuilder msg = new StringBuilder();
        for (FieldError fieldError : fieldErrors) {
            String defaultMessage = fieldError.getField() + fieldError.getDefaultMessage();
            if (msg.length() > 0) {
                msg.append(",");
            }
            msg.append(defaultMessage);
        }
        return msg.toString();
    }


    /**
     * 统一拦截处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResultBean<String> validExceptionHandler(BusinessException e) {
        logger.warn("业务异常：【{}】", e.getMessage());
        ResultBean<String> result= new ResultBean<>();
        result.setCode(e.getErrCode());
        result.setMsg(e.getMessage());
        return result;
    }

    /**
     * 数据库唯一性冲突
     * @param e 默认Exception异常对象
     * @return
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public ResultBean<String> keyExceptionHandler(DuplicateKeyException e) {
        logger.error("主键或业务索引冲突：【{}】", e.getMessage(),e);
        ResultBean<String> result= new ResultBean<>();
        result.setCode(ExceptionEnum.SERVER_ERROR.getCode());
        result.setMsg("业务数据重复或索引冲突，请确认是否重复录入数据。");
        return result;
    }

    /**
     * 空指针异常
     * @param e 默认Exception异常对象
     * @return
     */
    @ExceptionHandler(NullPointerException.class)
    public ResultBean<String> nullExceptionHandler(NullPointerException e) {
        logger.error("空指针异常：【{}】", e.getMessage(),e);
        ResultBean<String> result= new ResultBean<>();
        result.setCode(ExceptionEnum.SERVER_ERROR.getCode());
        result.setMsg("空指针异常，请咨询系统管理员。异常信息："+e.getStackTrace()[0].toString());
        return result;
    }

    /**
     * 默认统一异常处理方法
     * @param e 默认Exception异常对象
     * @return
     */
    @ExceptionHandler
    //@ResponseStatus
    public ResultBean<String> runtimeExceptionHandler(Exception e) {
        logger.error("运行时异常：【{}】", e.getMessage(),e);
        ResultBean<String> result= new ResultBean<>();
        result.setCode(ExceptionEnum.SERVER_ERROR.getCode());
        result.setMsg(e.getMessage());
        return result;
    }


    @ExceptionHandler(FileNotFoundException.class)
    public ResultBean<String> fileNotFoundException(Exception e) {
        logger.error("系统找不到指定的文件：【{}】", e.getMessage(),e);
        ResultBean<String> result= new ResultBean<>();
        result.setCode(ExceptionEnum.SERVER_ERROR.getCode());
        result.setMsg("系统找不到指定的文件。");
        return result;
    }

}
