server:
  port: 8000
  compression:
    enabled: true
  connection-timeout: 5s
  servlet:
    session:
      timeout: 3600s
  tomcat:
    max-threads: 2000
  undertow:
    buffer-size: 512
    direct-buffers: true
    worker-threads: 1500
spring:
  cloud:
    loadbalancer:
      retry:
        enabled: true
  aop:
    auto: true
    proxy-target-class: true
  http:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss Z
    joda-date-time-format: yyyy-MM-dd HH:mm:ss Z
    time-zone: GMT+8
  mvc:
    date-format: yyyy-MM-dd HH:mm:ss Z
  redisson:
    keyprefix: ${spring.application.name}
api:
  application:
    url: http://${api.eureka.service.id}/application/rest/getlist
  auth:
    appId: 123456
    appNo: sms-center-service
  eureka:
    service:
      id: API-GATEWAY-ADMIN
auth:
  check:
    url: http://${auth.eureka.service.id}/auth
  eureka:
    service:
      id: AUTH-CENTER-API
  menu:
    url: http://${auth.eureka.service.id}/auth/resource/getBussinessMenuListByUser
  user:
    resource:
      url: http://${auth.eureka.service.id}/auth/resource/selectResourceListByUser
    role:
      url: http://${auth.eureka.service.id}/userrole/getRoleListByAppNoUserId
com:
  zipkin:
    acks: 1
    linger:
      ms: 50
    port: ${server.port}
    serviceName: ${spring.application.name}
    topic: zipkin
juhe:
  url:
    mobile:
      telCheck: http://op.tianjurenhe.com/ofpay/mobile/telcheck
      telQuery: http://op.tianjurenhe.com/ofpay/mobile/telquery
      onlineOrder: http://op.tianjurenhe.com/ofpay/mobile/onlineorder
      orderSta:  http://op.tianjurenhe.com/ofpay/mobile/ordersta
    flow:
      list: http://v.juhe.cn/flow/list
      telCheck: http://v.juhe.cn/flow/telcheck
      recharge: http://v.juhe.cn/flow/recharge
      orderSta: http://v.juhe.cn/flow/ordersta
endpoints:
  default:
    web:
      enabled: true
management:
  endpoint:
    consul:
      enabled: false
    health:
      show-details: never
    metrics:
      enabled: true
    prometheus:
      enabled: true
  health:
    status:
      order: UP,DOWN, OUT_OF_SERVICE, UNKNOWN
  metrics:
    export:
      prometheus:
        enabled: true
mapper:
  mappers: tk.mybatis.mapper.common.Mapper
  not-empty: false
mybatis:
  configuration:
    cache-enabled: false
springfox:
  documentation:
    swagger:
      v2:
        path: /v2/swagger-api.json
identity:
  ribbon:
    retryableStatusCodes: 500
logging:
  level:
    com:
      huafagroup:
        core:
          mapper:
            framedb: debug
            test1: info
      netflix:
        eureka: info
ribbon:
  ConnectTimeout: 1000
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 2
  OkToRetryOnAllOperations: true
  ReadTimeout: 30000
  eureka:
    enabled: true
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  retryableStatusCodes: 500
threadpool:
  corepoolsize: 4
  maxpoolsize: 200
user:
  eureka:
    service:
      id: USER-CENTER-API
  org:
    account:
      url: http://${user.eureka.service.id}//org/selectUserListByOrgId
    path:
      url: http://${user.eureka.service.id}/org/selectOrgListByOrgPath
  search:
    account:
      url: http://${user.eureka.service.id}/userbasic/getUserDetailsBySearch
zuul:
  retryable: true