spring:
  application:
    name: gf-base-transaction-service-pro
  redisson:
    idleConnectionTimeout: 20000
    keyprefix: ${spring.application.name}
    masterConnectionMinimumIdleSize: 5
    maxConnectionSize: 100
    nodeAddresses:
      - redis://redis-cluster-sales-service-0:6000
      - redis://redis-cluster-sales-service-1:6000
      - redis://redis-cluster-sales-service-2:6000
      - redis://redis-cluster-sales-service-3:6000
      - redis://redis-cluster-sales-service-4:6000
      - redis://redis-cluster-sales-service-5:6000
    readMode: MASTER_SLAVE
    retryAttempts: 2
    slaveConnectionMinimumIdleSize: 5
  rocketmq:
    producer:
      compressMsgBodyOverHowmuch: 4096
      group: ${spring.application.name}
      instanceName: ${spring.cloud.client.ip-address}:${server.port}
      maxMessageSize: 4194304
      nameServer: *************:9876;*************:9876
      pullConsumerGroup: pull-consumer-group1
      pushConsumerGroup: push-consumer-group1
      retryAnotherBrokerWhenNotStoreOk: true
      retryTimesWhenSendAsyncFailed: 3
      retryTimesWhenSendFailed: 3
      sendMsgTimeout: 3000

sso:
  auth:
    currentNote:
      logoutUrl: http://************:8081/logout/sso
    loginRegisterUrl: http://************/oauth/loginRegister
    logoutUrl: http://************/logout
    redirectUrl: http://************:8081/login/callback?a=1&b=2
    requestTokenUrl: http://************/oauth/requestAccessToken
    url: http://************/oauth/authorize
first:
  spring:
    datasource:
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      driver-class-name: com.mysql.jdbc.Driver
      filters: stat,wall,log4j2
      initialSize: 5
      maxActive: 100
      maxPoolPreparedStatementPerConnectionSize: 20
      maxWait: 20000
      minEvictableIdleTimeMillis: 300000
      minIdle: 2
      poolPreparedStatements: true
      testOnBorrow: false
      testOnReturn: false
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 60000
      type: com.alibaba.druid.pool.DruidDataSource
      url: ***********************************************************************************************************************************************************************************
      username: gfat
      password: ml3iy3K60Yn1SB7I
      validationQuery: SELECT 1 FROM DUAL
eureka:
  client:
    healthcheck:
      enabled: true
    registry-fetch-interval-seconds: 5
    serviceUrl:
      defaultZone: http://eureka-service.paas-prod:8080/eureka
  instance:
    health-check-url-path: /actuator/health
    hostname: ${spring.cloud.client.ip-address}
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 8
    prefer-ip-address: true
base:
  transaction:
    payNotifyUrl: https://basepay.cnhuafas.com/v1/transaction-api/pay/notify/{}
    refundsNotifyUrl: https://basepay.cnhuafas.com/v1/transaction-api/refunds/notify/{}
    redPackClientIP: *************
    proxyIP: ************
    proxyPort: 8077
    ruthusHost: https://rs-center.huafacrm.com
  #话费充值配置
  recharge:
    url: http://test-v.juhe.cn
    signKey: 34c46f1626ed3873bcd0f97d20fa62bd
    openId: JHe670215587009a90bc93212c940a355c
  #抖音交易配置
  douyin:
    appId: 800629039354
    mchId: **********
    signKey:  gr2v6jyqh22gk73wm9n36pay19px0azvj1xca8p0

xxl:
  job:
    admin:
      addresses: http://xxl-job-service.estate-market:8000/xxl-job-admin
    accessToken:
    executor:
      port: 8886
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 1
      appname: base-transaction-executor-pro
      address:
      ip:
apache:
  rocketmq:
    group: baseServiceGroup
    namesrvAddr: rocketmq-cluster-namesrv1.huafagroup.com:9876;rocketmq-cluster-namesrv2.huafagroup.com:9876
    topic: commissionTopic
    acl.user: base-transaction
    acl.pass: hX5BUDQUx7M3
    commissionTopicSwitch: 1
    sync:
      tables: area;city;org;project