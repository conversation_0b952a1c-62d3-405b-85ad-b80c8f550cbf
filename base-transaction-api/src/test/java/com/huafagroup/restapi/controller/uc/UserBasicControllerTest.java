//package com.huafagroup.restapi.controller.uc;
//
//import base.BaseTest;
//import com.huafagroup.core.entity.uc.UserAddDTO;
//import com.huafagroup.core.entity.uc.UserBasic;
//import com.huafagroup.core.entity.uc.UserThirdPartyDTO;
//import com.huafagroup.core.entity.uc.UserUpdateDTO;
//import com.huafagroup.core.exception.ExceptionEnum;
//import com.huafagroup.core.util.ResultBean;
//import org.junit.Test;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.StringUtils;
//
//import java.util.UUID;
//
//import static org.junit.Assert.*;
//import static org.junit.Assert.assertTrue;
//
//public class UserBasicControllerTest extends BaseTest{
//    @Autowired
//    private UserBasicController controller;
//
//    /**
//     * 测试注册接口
//     * 只传入account的场景
//     */
//    @Test
//    @Transactional
//    public void testRegisterByAccount() {
//        UserAddDTO dto = new UserAddDTO();
//        dto.setAccount("account");
//        ResultBean<UserBasic>  resultBean = controller.register(dto);
//        assertTrue(resultBean.getCode() == ExceptionEnum.SUCCESS.getCode());
//        assertTrue(resultBean.getData().getStatus());
//    }
//
//    /**
//     * 测试注册接口
//     * 只传入手机号码的场景
//     */
//    @Test
//    @Transactional
//    public void testRegisterByTel() {
//        UserAddDTO dto = new UserAddDTO();
//        dto.setTel("***********");
//        ResultBean<UserBasic>  resultBean = controller.register(dto);
//        assertTrue(resultBean.getCode() == ExceptionEnum.SUCCESS.getCode());
//        assertTrue(resultBean.getData().getStatus());
//    }
//
//    /**
//     * 测试注册接口
//     * 传入所有参数
//     */
//    @Test
//    @Transactional
//    public void testRegisterNormal() {
//        UserAddDTO dto = new UserAddDTO();
//        dto.setAccount("account");
//        dto.setPassword("password");
//        dto.setTel("***********");
//        dto.setAge(20);
//        dto.setEmail("<EMAIL>");
//        dto.setIdCard("******************");
//        dto.setName("name");
//        dto.setSex("1");
//        dto.setStatus(true);
//        ResultBean<UserBasic>  resultBean = controller.register(dto);
//        assertTrue(resultBean.getCode() == ExceptionEnum.SUCCESS.getCode());
//        assertTrue(resultBean.getData().getStatus());
//        assertTrue(!"".equals(resultBean.getData().getId()));
//    }
//
//    /**
//     * 测试注册接口
//     * 传入空对象的场景
//     */
//    @Test
//    @Transactional
//    public void testRegisterMissing() {
//        UserAddDTO dto = new UserAddDTO();
//        ResultBean<UserBasic>  resultBean = controller.register(dto);
//        assertTrue(resultBean.getCode() == ExceptionEnum.PARAMS_NOT_VALID.getCode());
//        assertTrue(resultBean.getData() == null);
//    }
//
//    /**
//     * 测试注册接口
//     * 进行重复注册
//     */
//    @Test
//    @Transactional
//    public void testRegisterRepeat() {
//        testRegisterByTel();
//
//        UserAddDTO dto = new UserAddDTO();
//        dto.setTel("***********");
//        ResultBean<UserBasic>  resultBean = controller.register(dto);
//        assertTrue(resultBean.getCode() == ExceptionEnum.PARAMS_NOT_VALID.getCode());
//        assertTrue(resultBean.getData() == null);
//    }
//
//    /**
//     * 测试更新
//     * 正常场景
//     */
//    @Test
//    @Transactional
//    public void testUpdateNormal() {
//        UserAddDTO dto = new UserAddDTO();
//        dto.setTel("***********");
//        ResultBean<UserBasic>  resultBean = controller.register(dto);
//
//        UserUpdateDTO updateDTO = new UserUpdateDTO();
//        BeanUtils.copyProperties(resultBean.getData(),dto);
//        updateDTO.setId(resultBean.getData().getId());
//        updateDTO.setAge(18);
//        updateDTO.setName("test");
//        ResultBean<String> bean = controller.update(updateDTO);
//        assertTrue(bean.getCode() == ExceptionEnum.SUCCESS.getCode());
//        assertTrue(bean.getData().equalsIgnoreCase("1"));
//    }
//
//    /**
//     * 测试更新
//     *  只传入userId的情况
//     */
//    @Test
//    @Transactional
//    public void testUpdateUserId() {
//        UserAddDTO dto = new UserAddDTO();
//        dto.setTel("***********");
//        ResultBean<UserBasic>  resultBean = controller.register(dto);
//
//        UserUpdateDTO updateDTO = new UserUpdateDTO();
//        updateDTO.setId(resultBean.getData().getId());
//        ResultBean<String> bean = controller.update(updateDTO);
//        assertTrue(bean.getCode() == ExceptionEnum.SUCCESS.getCode());
//        assertTrue(bean.getData().equalsIgnoreCase("1"));
//    }
//
//    /**
//     * 测试更新
//     *  不传userId的情况
//     */
//    @Test
//    @Transactional
//    public void testUpdateNoUserId() {
//        UserUpdateDTO updateDTO = new UserUpdateDTO();
//        ResultBean<String> bean = controller.update(updateDTO);
//        assertTrue(bean.getCode() == ExceptionEnum.SUCCESS.getCode());
//        assertTrue(bean.getData().equalsIgnoreCase("0"));
//    }
//
//    /**
//     * 测试账号密码验证接口
//     * 账号不存在
//     */
//    @Test
//    public void testCheckPassNoAccount() {
//        String account = UUID.randomUUID().toString();
//        String psw = "123456";
//        ResultBean<UserBasic> resultBean = controller.checkPass(account, psw);
//        assertTrue(resultBean.getCode() == ExceptionEnum.RESOURCE_NOT_FOUND.getCode());
//    }
//
//    /**
//     * 测试账号密码验证接口
//     * 密码不正确
//     */
//    @Test
//    public void testCheckPassWithIncorrectPsw() {
//        String account = "***********";
//        String psw = "123456";
//        UserAddDTO dto = new UserAddDTO();
//        dto.setAccount(account);
//        dto.setPassword(psw);
//        controller.register(dto);
//
//        ResultBean<UserBasic> basicResultBean = controller.checkPass(account, psw + psw);
//        assertTrue(basicResultBean.getCode() == ExceptionEnum.RESOURCE_NOT_FOUND.getCode());
//    }
//
//    /**
//     * 测试账号密码验证接口
//     * 正常场景
//     */
//    @Test
//    public void testCheckPassNormal() {
//        String account = "abcdefg";
//        String psw = "abcdefg";
//        UserAddDTO dto = new UserAddDTO();
//        dto.setAccount(account);
//        dto.setPassword(psw);
//        controller.register(dto);
//
//        ResultBean<UserBasic> basicResultBean = controller.checkPass(account, psw);
//        assertTrue(basicResultBean.getCode() == ExceptionEnum.SUCCESS.getCode());
//        assertTrue(!StringUtils.isEmpty(basicResultBean.getData().getId()));
//    }
//
//    @Test
//    public void getUserById() {
//    }
//
//    @Test
//    public void getUserByTel() {
//    }
//
//    /**
//     * 第三方认证信息绑定接口
//     *  场景： 用户不存在
//     */
//    @Test
//    public void bindThirdPartyNoUser() {
//        UserThirdPartyDTO dto = new UserThirdPartyDTO();
//        dto.setUserId("Not Existed User Id");
//        dto.setAppCode("abc");
//        dto.setUnionId("union id");
//        ResultBean<String> resultBean = controller.bindThirdParty(dto);
//        assertTrue(resultBean.getCode() == ExceptionEnum.RESOURCE_NOT_FOUND.getCode());
//    }
//
//    /**
//     * 第三方认证信息绑定接口
//     *  场景： 正常场景
//     */
//    @Test
//    @Transactional
//    public void bindThirdPartyNormal() {
//        UserAddDTO dto = new UserAddDTO();
//        dto.setTel("***********");
//        ResultBean<UserBasic>  resultBean = controller.register(dto);
//
//        UserThirdPartyDTO thirdPartyDTO = new UserThirdPartyDTO();
//        thirdPartyDTO.setUserId(resultBean.getData().getId());
//        thirdPartyDTO.setAppCode("abc");
//        thirdPartyDTO.setUnionId(UUID.randomUUID().toString());
//        ResultBean<String> bean = controller.bindThirdParty(thirdPartyDTO);
//        assertTrue(bean.getCode() == ExceptionEnum.SUCCESS.getCode());
//    }
//
//    @Test
//    public void getUserByUnionId() {
//    }
//}