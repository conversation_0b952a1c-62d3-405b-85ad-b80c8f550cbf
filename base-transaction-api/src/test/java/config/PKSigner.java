package config;

import java.io.FileInputStream;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Enumeration;

public class PKSigner {

    public static void main(String[] args) throws Exception {
        InputStream inputStream = new FileInputStream("C:\\Users\\<USER>\\IdeaProjects\\base-transaction\\base-transaction-api\\src\\main\\resources\\certs\\hf_syhb\\apiclient_cert.p12");
        // 如果是http链接
        String password = "1577439771";
        char[] pem = password.toCharArray();
        KeyStore ks = KeyStore.getInstance("PKCS12");
        ks.load(inputStream, pem);
        Enumeration<String> enums = ks.aliases();
        String keyAlias = null;
        if (enums.hasMoreElements()) { // we are reading just one certificate.
            keyAlias = enums.nextElement();
        }
        PrivateKey priKey = (PrivateKey) ks.getKey(keyAlias, pem);
        X509Certificate cert = (X509Certificate) ks.getCertificate(keyAlias);

        // 打印证书详细信息
        System.out.println("Certificate Info:");
        System.out.println("  Signature Algorithm: " + cert.getSigAlgName());
        System.out.println("  Public Key Algorithm: " + cert.getPublicKey().getAlgorithm());
        System.out.println("  Serial Number: " + cert.getSerialNumber().toString(16).toUpperCase());

    }
}
