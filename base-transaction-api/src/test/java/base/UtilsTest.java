package base;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.huafagroup.core.configuration.TimeBasedUUIDGenerator;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.dto.wx.v2.TransferModel;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.enums.PayProviderEnum;
import com.huafagroup.core.util.DateUtils;
import com.huafagroup.core.util.wx.PKSigner;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;
import sun.security.util.Debug;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.cert.X509Certificate;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Enumeration;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class UtilsTest {

    @Test
    public void m1() {
        for (int i = 1;i<33;i++) {
            System.out.println("i=" + i + ">>"+Math.max((int) Math.sqrt((float) i/2), 1));
        }
    }

    @Test
    public void testStr() {
        String line = "https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb?prepay_id=wx2916263004719461949c84457c735b0000&package=**********";
        Pattern r = Pattern.compile("prepay_id=(\\w+)");
        Matcher m = r.matcher(line);
        if (m.find( )) {
            System.out.println("Found value: " + m.group(0) );
            System.out.println("Found value: " + m.group(1) );
        } else {
            System.out.println("NO MATCH");
        }
    }

    @Test
    public void test1(){
        //当时时间
        ZonedDateTime now = ZonedDateTime.now();
        //增加时间
        ZonedDateTime plusTime = now.plusMinutes(30);
        //rfc3339标准格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
        System.out.println(plusTime.format(formatter));
        System.out.println(now.format(formatter));

        System.out.println(now.toLocalDate());

        Date date = Date.from(now.toInstant());

        System.out.println(date);

        System.out.println(DigestUtil.sha1Hex("xxx"));
        System.out.println(DigestUtil.sha256Hex("xxx"));

        System.out.println(TimeBasedUUIDGenerator.generateId());
        System.out.println(TimeBasedUUIDGenerator.generateId());
        System.out.println(IdUtil.simpleUUID());

        System.out.println(IdUtil.simpleUUID());
        System.out.println(PayProviderEnum.WECHAT.toString());
        System.out.println(ZonedDateTime.parse("2020-12-01T16:18:12+08:00",formatter));
    }

    @Test
    public void dateFormate(){
        String dateStr = "2020-12-01T16:18:12+08:00";
        //rfc3339标准格式
        System.out.println(DateUtils.fromRfc3339Str(dateStr));

        System.out.println(DateUtils.toRfc3339Str(new Date()));
        long currentSeconds = DateUtil.currentSeconds();
        System.out.println(currentSeconds);

        System.out.println(DateUtil.offsetMinute(new Date(),30));

        DateTime dateTime = DateUtil.offsetMinute(new Date(), -30);
        System.out.println(dateTime);

    }

    /**
     * 拉起支付时的签名数据
     * @throws UnsupportedEncodingException
     */
    @Test
    public void testPaySign() throws UnsupportedEncodingException {
        // 加载商户私钥（privateKey：私钥字符串）
        String privateKey = "";

        String appid = "";
        long timestamp = 11L;
        String nonceStr = RandomUtil.randomString(32);
        String message = "prepay_id=wx201410272009395522657a690389285100";

        String builder = appid + "\n" + timestamp + "\n" + nonceStr + "\n" + message + "\n";
        String sign = PKSigner.sign(builder, privateKey);

    }

    @Test
    public void testXml() throws JsonProcessingException {
        ObjectMapper OBJECT_MAPPER = new ObjectMapper();
        OBJECT_MAPPER.configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        TransferModel model = new TransferModel();
        model.setAmount("100");
        model.setDesc("desc");
        model.setMchid("mchid");
        model.setNonceStr("xxxxxxxxxjyyyfdsfsdkfjs");
        String link = OBJECT_MAPPER
                .writer()
                .writeValueAsString(model)
                .replaceAll("\":\"", "=")
                .replaceAll("\",\"", "&")
                .replaceAll("\\\\\"", "\"");

        String result = link.substring(2, link.length() - 2).concat("&key=").concat("this.appSecret");
        System.out.println(result);

        String md5Hex = DigestUtil.md5Hex(result).toUpperCase();
        System.out.println(md5Hex);

        XmlMapper XML_MAPPER = new XmlMapper();
        XML_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL)
                // 属性使用 驼峰首字母小写
                .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);

        String xml = XML_MAPPER.writer()
                .withRootName("xml")
                .writeValueAsString(model);
        System.out.println(xml);


        String str1 = OBJECT_MAPPER
                .writer()
                .writeValueAsString(model);
        JSONObject jsonObject = JSONUtil.parseObj(str1);

        String xmlStr = JSONUtil.toXmlStr(jsonObject);
        System.out.println(xmlStr);
        JSONObject jsonObject1 = JSONUtil.xmlToJson(xml);
        System.out.println(jsonObject1.getJSONObject("xml"));

        System.out.println("1:" + model.toXml("appScert"));
    }
    @Test
    public void loadCerts() throws Exception {
        MerchantProperties merchantProperties = new MerchantProperties();
        merchantProperties.setCertPath("certs/hf_zhhp/apiclient_cert.p12");
        merchantProperties.setMchId("1602596453");
        ClassPathResource resource = new ClassPathResource(merchantProperties.getCertPath());

        char[] pem = merchantProperties.getMchId().toCharArray();

        KeyStore ks = KeyStore.getInstance("PKCS12");
        ks.load(resource.getInputStream(), pem);
//        store.aliases()   tenpay certificate
        Enumeration enums = ks.aliases();
        String keyAlias = null;
        if (enums.hasMoreElements()) // we are readin just one certificate.
        {
            keyAlias = (String)enums.nextElement();
            System.out.println("alias=[" + keyAlias + "]");
        }
        // Now once we know the alias, we could get the keys.
        System.out.println("is key entry=" + ks.isKeyEntry(keyAlias));
        PrivateKey prikey = (PrivateKey) ks.getKey(keyAlias, pem);
        X509Certificate cert = (X509Certificate)ks.getCertificate(keyAlias);
        PublicKey pubkey = cert.getPublicKey();

        System.out.println("cert class = " + cert.getClass().getName());

        System.out.println("cert no= " + Debug.toHexString(cert.getSerialNumber()).toUpperCase().replaceAll(" ",""));
        System.out.println("cert = " + cert);
        System.out.println("public key = " + pubkey);

        System.out.println("private key str= " + Base64.encode(prikey.getEncoded()));
    }

    @Test
    public void genCerts() throws Exception {
        Merchant merchantProperties = new Merchant();
        merchantProperties.setCertificateUrl("https://oss.huafagroup.com/promotion-pro/apiclient_cert_1744097650410.p12");
        merchantProperties.setMchId("1577444291");
        PKSigner.genPrivateKeyInfo(merchantProperties);
        System.out.println("PrivateKey=" + merchantProperties.getPrivateKey());
        System.out.println("MchSerialNo=" + merchantProperties.getMchSerialNo());
    }

    @Test
    public void testAmount() {
        double amount = BigDecimal.valueOf(10).divide(new BigDecimal(100)).doubleValue();
        System.out.println(amount);
        amount = BigDecimal.valueOf(100).divide(new BigDecimal(100)).doubleValue();
        System.out.println(amount);
        amount = BigDecimal.valueOf(10000).divide(new BigDecimal(100)).doubleValue();
        System.out.println(amount);
    }
}
