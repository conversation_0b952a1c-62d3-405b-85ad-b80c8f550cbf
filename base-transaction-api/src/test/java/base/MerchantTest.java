package base;

import com.huafagroup.core.dao.trans.MerchantMapper;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.util.wx.PKSigner;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class MerchantTest extends BaseTest{

    @Autowired
    MerchantMapper merchantMapper;

    @Test
    public void testCertExist() {
        List<Merchant> merchants = merchantMapper.selectAll();

        merchants.forEach(merchant -> {
            String url = merchant.getCertificateUrl();
            Merchant merchantProperties = new Merchant();
            merchantProperties.setCertificateUrl(url);
            merchantProperties.setMchId(merchant.getMchId());
            try {
                PKSigner.genPrivateKeyInfo(merchantProperties);
//                System.out.println("PrivateKey=" + merchantProperties.getPrivateKey());
            } catch (Exception e) {
                System.out.println("error," + merchant.getId() + "," + merchant.getMchName());
                e.printStackTrace();
            }
        });
    }
}
