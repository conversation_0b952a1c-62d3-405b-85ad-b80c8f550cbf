<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huafagroup.core.dao.trans.ProjectMapper">

    <select id="getList" resultType="com.huafagroup.core.dto.ProjectMerchantVO">
        SELECT
        a.id,
        p.id as projectId,
        p.name as projectName,
        m.mch_id,
        m.mch_name,
        m.merchant_subject,
        a.balance,
        a.channel
        FROM
        trans_project p
        LEFT JOIN
        trans_account a ON a.project_id = p.id
        LEFT JOIN
        trans_merchant m ON m.id = a.merchant_id
        WHERE
        p.type = 3
        AND p.del_flag = 0
        <if test="bindFlag != null and bindFlag == 0">
            AND m.mch_id IS NULL
        </if>
        <if test="bindFlag != null and bindFlag == 1">
            AND m.mch_id IS NOT NULL
        </if>
        <if test="keyword != null and keyword != ''">
            AND (p.id LIKE CONCAT('%', #{keyword}, '%')
                OR p.name LIKE CONCAT('%', #{keyword}, '%')
                OR m.mch_id LIKE CONCAT('%', #{keyword}, '%')
                OR m.mch_name LIKE CONCAT('%', #{keyword}, '%')
                OR m.merchant_subject LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
    </select>
</mapper>
