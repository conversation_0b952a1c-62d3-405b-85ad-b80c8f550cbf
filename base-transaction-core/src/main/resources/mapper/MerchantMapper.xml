<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huafagroup.core.dao.trans.MerchantMapper">
 <select id="getList" resultType="com.huafagroup.core.entity.trans.Merchant">
  SELECT * from trans_merchant m
      where m.del_flag = 0
        <if test="keyword != null and keyword != ''">
            and (m.mch_id like concat('%',#{keyword},'%') or m.mch_name like concat('%',#{keyword},'%') or m.merchant_subject like concat('%',#{keyword},'%') or m.manager_name like concat('%',#{keyword},'%') or m.manager_phone like concat('%',#{keyword},'%'))
        </if>
        <if test="payFlag != null">
            and m.pay_flag = #{payFlag}
        </if>
    order by m.id desc
 </select>
</mapper>