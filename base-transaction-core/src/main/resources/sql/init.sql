CREATE TABLE `trans_order`
(
    `id`                           bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `order_no`                     varchar(64)  NOT NULL COMMENT '订单号，UUID生成',
    `project_id`                   varchar(64)  NOT NULL COMMENT '项目ID',
    `account_id`                   int(11)      DEFAULT NULL COMMENT '账号ID',
    `mch_id`                       varchar(64)  DEFAULT NULL COMMENT '直连商户的商户号',
    `app_id`                       varchar(64)  NOT NULL COMMENT '直连商户申请的公众号或移动应用appid',
    `desc`                         varchar(128) DEFAULT NULL COMMENT '订单描述',
    `expire_time`                  datetime     NOT NULL,
    `pay_provider`                 varchar(64)  NOT NULL COMMENT '支付平台，WECHAT:微信',
    `openid`                       varchar(64)  NOT NULL COMMENT '用户在直连商户appid下的唯一标识',
    `amount`                       int(11)      NOT NULL COMMENT '订单金额，单位分',
    `prepay_id`                    varchar(64)  DEFAULT NULL COMMENT '预支付交易会话标识',
    `status`                       varchar(64)  DEFAULT NULL COMMENT '订单状态，NOTPAY：未支付，SUCCESS：支付成功，PAYERROR：支付失败，CLOSED：已关闭',
    `notify_id`                    varchar(64)  DEFAULT NULL COMMENT '通知的唯一ID',
    `notify_time`                  datetime     DEFAULT NULL COMMENT '通知时间',
    `notify_summary`               varchar(128) DEFAULT NULL COMMENT '通知摘要',
    `payer_total`                  int(11)      DEFAULT NULL COMMENT '用户支付金额，单位分',
    `transaction_id`               varchar(64)  DEFAULT NULL COMMENT '微信支付订单号',
    `success_time`                 datetime     DEFAULT NULL COMMENT '支付完成时间',
    `biz_order_no`                 varchar(64)  NOT NULL COMMENT '业务系统内部订单号',
    `biz_notify_status`            varchar(64)  DEFAULT NULL COMMENT '业务系统通知，状态NOTNOTIFY：未通知SUCCESS：通知成功',
    `biz_notify_time`              datetime     DEFAULT NULL COMMENT '业务系统通知成功时间',
    `biz_notify_url`               varchar(256) NOT NULL COMMENT '业务系统回调通知地址',
    `refund_reason`                varchar(128) DEFAULT NULL COMMENT '退款原因 ',
    `refund_biz_notify_url`        varchar(256) DEFAULT NULL COMMENT '退款业务系统回调通知地址',
    `refund_id`                    varchar(64)  DEFAULT NULL COMMENT '支付退款号',
    `refund_status`                varchar(64)  DEFAULT NULL COMMENT '退款状态， SUCCESS：退款成功CLOSED：退款关闭PROCESSING：退款处理中ABNORMAL：退款异常',
    `refund_channel`               varchar(64)  DEFAULT NULL COMMENT '退款渠道，ORIGINAL：原路退款',
    `refund_user_received_account` varchar(64)  DEFAULT NULL COMMENT '退款入账账户，如：招商银行信用卡0403',
    `refund_notify_id`             varchar(64)  DEFAULT NULL COMMENT '退款通知的唯一ID',
    `refund_notify_time`           datetime     DEFAULT NULL COMMENT '退款通知时间',
    `refund_notify_summary`        varchar(128) DEFAULT NULL COMMENT '退款通知摘要',
    `refund_payer_refund`          int(11)      DEFAULT NULL COMMENT '实际退款金额',
    `refund_success_time`          datetime     DEFAULT NULL COMMENT '退款成功时间 ',
    `refund_biz_notify_status`     varchar(64)  DEFAULT NULL COMMENT '退款业务系统通知状态，NOTNOTIFY：未通知SUCCESS：通知成功',
    `refund_biz_notify_time`       datetime     DEFAULT NULL COMMENT '业务系统通知退款成功时间',
    `create_date`                  datetime     DEFAULT NULL,
    `create_by`                    varchar(64)  DEFAULT NULL,
    `update_date`                  datetime     DEFAULT NULL,
    `update_by`                    varchar(64)  DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `order_no_UNIQUE` (`order_no`),
    UNIQUE KEY `transaction_id_UNIQUE` (`transaction_id`),
    UNIQUE KEY `refund_id_UNIQUE` (`refund_id`),
    KEY `idx_update_date` (`update_date`) USING BTREE
) ENGINE = InnoDB COMMENT ='交易订单表';

CREATE TABLE `trans_hb_order`
(
    `id`             BIGINT       NOT NULL AUTO_INCREMENT COMMENT '主键自增ID',
    `nonce_str`      VARCHAR(45)  NOT NULL COMMENT '随机字符串',
    `order_no`       VARCHAR(64)  NOT NULL COMMENT '订单号，UUID生成',
    `mch_id`         VARCHAR(45)  NULL COMMENT '商户号',
    `account_id`     int(11)           DEFAULT NULL COMMENT '账号ID',
    `app_id`         VARCHAR(64)  NULL COMMENT '公众账号appid',
    `openid`         VARCHAR(45)  NULL COMMENT '用户openid',
    `amount`         INT          NOT NULL COMMENT '红包金额，单位分',
    `check_name`     VARCHAR(45)  NULL DEFAULT 'NO_CHECK' COMMENT '校验用户姓名选项 NO_CHECK：不校验真实姓名，FORCE_CHECK：强校验真实姓名',
    `re_user_name`   VARCHAR(45)  NULL COMMENT '收款用户姓名，如果check_name设置为FORCE_CHECK，则必填用户真实姓名',
    `desc`           VARCHAR(128) NULL COMMENT '企业付款备注',
    `biz_order_no`   VARCHAR(45)  NULL COMMENT '业务订单号',
    `project_id`     VARCHAR(45)  NOT NULL COMMENT '项目id',
    `send_name`      VARCHAR(64)  NOT NULL COMMENT '商户名称',
    `wishing`        VARCHAR(45)  NOT NULL COMMENT '红包祝福语',
    `act_name`       VARCHAR(45)  NOT NULL COMMENT '活动名称',
    `scene_id`       VARCHAR(45)  NOT NULL COMMENT '发放红包使用场景',
    `status`         VARCHAR(45)  NOT NULL COMMENT 'NOTPAY(初始状态)/SUCCESS（成功）/FAIL（失败）',
    `success_time`   DATETIME     NULL COMMENT '付款成功时间 ',
    `transaction_id` VARCHAR(64)  NULL COMMENT '微信付款单号 ',
    `create_date`    DATETIME     NULL COMMENT '创建时间',
    `create_by`      VARCHAR(45)  NULL COMMENT '创建人',
    `update_date`    DATETIME     NULL COMMENT '更新时间',
    `update_by`      VARCHAR(45)  NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `order_no_UNIQUE` (`order_no` ASC),
    UNIQUE INDEX `transaction_id_UNIQUE` (`transaction_id` ASC)
)
    ENGINE = InnoDB
    COMMENT = '激励订单表';

CREATE TABLE `trans_transaction`
(
    `id`          BIGINT       NOT NULL AUTO_INCREMENT,
    `account_id`  INT          NOT NULL COMMENT '账号id',
    `amount`      INT          NOT NULL COMMENT '交易金额，单位分',
    `type`        VARCHAR(45)  NOT NULL COMMENT '交易类型，PAY：支付订单，REFUND：退款订单，TRANSFER：激励订单 DEPOSIT：充值订单',
    `order_id`    BIGINT       NOT NULL COMMENT '关联的订单id，可能是支付订单，退款订单，激励订单，充值订单',
    `desc`        VARCHAR(128) NULL COMMENT '描述',
    `create_date` DATETIME     NULL COMMENT '创建时间',
    `update_date` DATETIME     NULL COMMENT '更新时间',
    `create_by`   VARCHAR(45)  NULL COMMENT '创建人',
    `update_by`   VARCHAR(45)  NULL COMMENT '更新人',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uq_type_orderid` (`type` ASC, `order_id` ASC)
)
    ENGINE = InnoDB
    COMMENT = '交易流水表';

CREATE TABLE `trans_account`
(
    `id`          bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `project_id`  varchar(32) NOT NULL COMMENT '营销系统同步过来的项目id',
    `merchant_id` bigint      DEFAULT NULL COMMENT '商户表对应id',
    `name`        varchar(50) DEFAULT NULL COMMENT '账户名称',
    `balance`     bigint      DEFAULT NULL COMMENT '余额',
    `del_flag`    int         NOT NULL COMMENT '0-默认，1-删除',
    `create_by`   varchar(32) NOT NULL COMMENT '创建人',
    `create_date` datetime    NOT NULL COMMENT '创建时间',
    `update_by`   varchar(32) DEFAULT NULL COMMENT '最后更新人',
    `update_date` datetime    DEFAULT NULL COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT = '账号表';

CREATE TABLE `trans_project`
(
    `id`          varchar(32) NOT NULL COMMENT '主键',
    `parent_id`   varchar(32) DEFAULT NULL COMMENT '父级',
    `name`        varchar(64) NOT NULL COMMENT '名称',
    `type`        int(4)      NOT NULL COMMENT '0-area;1-city;2-org;3-project',
    `create_type` int(4)      NOT NULL COMMENT '1-同步，2-手动',
    `del_flag`    int(4)      NOT NULL COMMENT '0-默认，1-删除',
    `create_by`   varchar(32) NOT NULL COMMENT '创建人',
    `create_date` datetime    NOT NULL COMMENT '创建时间',
    `update_by`   varchar(32) DEFAULT NULL COMMENT '最后更新人',
    `update_date` datetime    DEFAULT NULL COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT = '项目表';


CREATE TABLE `trans_merchant`
(
    `id`              bigint      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `mch_id`          varchar(32)   DEFAULT NULL COMMENT '微信商户号',
    `apiv3_key`       varchar(50)   DEFAULT NULL COMMENT 'Api V3秘钥',
    `api_key`         varchar(64)   DEFAULT NULL COMMENT 'Api秘钥',
    `certificate_url` varchar(500)  DEFAULT NULL COMMENT '支付证书url',
    `mch_serial_no`   varchar(500)  DEFAULT NULL COMMENT '商户证书序列号',
    `private_key`     VARCHAR(3096) DEFAULT NULL COMMENT '商户私钥',
    `del_flag`        int         NOT NULL COMMENT '0-默认，1-删除',
    `create_by`       varchar(32) NOT NULL COMMENT '创建人',
    `create_date`     datetime    NOT NULL COMMENT '创建时间',
    `update_by`       varchar(32)   DEFAULT NULL COMMENT '最后更新人',
    `update_date`     datetime      DEFAULT NULL COMMENT '最后更新时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT = '商户表';
