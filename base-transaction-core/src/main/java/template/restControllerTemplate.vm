package ${restControllerPackage};
import ${modelPackage}.${beanName};
import ${servicePackage}.${beanName}Service;
import com.huafagroup.core.util.ResultBean;
import com.huafagroup.core.exception.ExceptionEnum;
import org.springframework.validation.annotation.Validated;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.Page;
/**
* <p>
    * </p>
*
* <AUTHOR>
* @date ${dateTime}
* @version
*/
@RestController
@RequestMapping(value = "/${lowerBeanName}")
public class ${beanName}Controller {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private HttpServletResponse response;
    @Autowired
    private ${beanName}Service service;


    @ApiOperation(value="获取单条记录", notes="根据url的id来获取详细信息")
    @RequestMapping(value = "/get",method = RequestMethod.GET)
    public ResultBean<${beanName}> get(String id){
        ${beanName} item=  service.selectById(id);
        if(item!=null){
            return new ResultBean<${beanName}>(item);
        }else {
            return new ResultBean<${beanName}>(ExceptionEnum.RESOURCE_NOT_FOUND,"找不到该记录",null);
        }
    }


    @RequestMapping(value = "/getlist",method = RequestMethod.GET)
    public ResultBean<List<${beanName}>> getList(){
        List<${beanName}> list=  service.selectAll();
        ResultBean<List<${beanName}>> resultBean=new ResultBean<List<${beanName}>>(list);
        return  resultBean;
    }

    @RequestMapping(value = "/create",method = RequestMethod.POST)
    public ResultBean<String> create(@RequestBody @Validated ${beanName} item){
        int  result= service.insert(item);
        ResultBean<String> resultBean=new ResultBean<String>("");
        return  resultBean;
    }

    @RequestMapping(value = "/update",method = RequestMethod.POST)
    public ResultBean<String> update(@RequestBody @Validated ${beanName} item){
        int  result=  service.update(item);
        ResultBean<String> resultBean=new ResultBean<String>("");
        return  resultBean;
    }

    @RequestMapping(value = "/deleteByID",method = RequestMethod.POST)
    public ResultBean<Integer> delete(String id){
        int  result=  service.deleteById(id);
        ResultBean<Integer> resultBean=new ResultBean<Integer>(result);
        return  resultBean;
    }

    @RequestMapping(value = "/delete",method = RequestMethod.POST)
    public ResultBean<Integer> delete(@RequestBody @Validated ${beanName} item){
        int  result=  service.delete(item);
        ResultBean<Integer> resultBean=new ResultBean<Integer>(result);
        return  resultBean;
    }

}

