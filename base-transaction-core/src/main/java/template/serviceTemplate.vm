package ${servicePackage};


import ${modelPackage}.${beanName};
import ${mapperPackage}.${beanName}Mapper;
import com.huafagroup.core.service.system.BaseServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
/**
* <p>
    * </p>
*
* <AUTHOR>
* @date ${dateTime}
* @version
*/

@Service
public class ${beanName}Service extends BaseServiceImpl<${beanName}>{

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ${beanName}Mapper mapper;

  }