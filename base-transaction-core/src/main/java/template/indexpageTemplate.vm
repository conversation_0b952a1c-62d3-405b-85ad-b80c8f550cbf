<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>${remark}信息维护界面</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="/root/layui/css/layui.css"  media="all">
    <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
<body>
<table class="layui-hide" id="${lowerBeanName}_table" lay-filter="${lowerBeanName}_table"></table>

<script id="${lowerBeanName}toolbar" type="text/html">
    <div class="">
        <!-- -->
        <button class="layui-btn" onclick="addShow('新增页面','./add',600,400)"><i class="layui-icon">&#xe654;</i>新增</button>
        应用模块：
        <div class="layui-inline">
            <input name="id" class="layui-input" id="appModule" autocomplete="off">
        </div>
        创建时间：
        <div class="layui-input-inline">
            <input class="layui-input" id="dateRangePick" type="text" placeholder=" - ">
        </div>
        <button class="layui-btn" data-type="reload" onclick="reloadTable()"><i class="layui-icon layui-icon-search"></i>搜索</button>
    </div>
</script>
    #foreach($item in $columns)
        #if( ${item.jdbcType}  =='BIT')
        <script type="text/html" id="${item.lowerProperty}Tpl">

            <input type="checkbox" disabled="disabled" value="{{d.${item.lowerProperty}}}"  name="${item.lowerProperty}"  lay-skin="switch" lay-text="ON|OFF" {{ d.${item.lowerProperty} == 1 ? 'checked' : '' }}>
        </script>

        #end
    #end

<script id="${lowerBeanName}bar" type="text/html">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
</script>
<script src="/root/layui/layui.js" charset="utf-8"></script>
<!-- 注意：如果你直接复制所有代码到本地，上述js路径需要改成你本地的 -->
<script src="/root/js/server-api.js" charset="utf-8"></script>
<script  charset="utf-8">
    var getListUrl;
    var deleteUrl;
        #if ( ${modulePrefix}== true )
        getListUrl='/${module}/${lowerBeanName}/getlist';
        deleteUrl='/${module}/${lowerBeanName}/delete';
        #else
        getListUrl='/${lowerBeanName}/getlist';
        deleteUrl='/${lowerBeanName}/delete';
        #end

    layui.use(['table','layer', 'form', 'util','laydate'] ,function(){
        var $ = layui.jquery;
        var table = layui.table;
        var layer = layui.layer;
        var form = layui.form;
        var util = layui.util;
        var laydate = layui.laydate;
        laydate.render({
            elem: '#dateRangePick'
            ,type: 'date'
            ,range: true //或 range: '~' 来自定义分割字符
        });
        table.render({limit:30,
            elem: '#${lowerBeanName}_table',
            url:getListUrl,
            toolbar: '#${lowerBeanName}toolbar',
            title: '信息',
            cellMinWidth: 80,
            cols: [[
                {type: 'checkbox', fixed: 'left'}
                #foreach($item in $columns)
                    #if ( ${item.lowerProperty} == 'createBy' )
                    #elseif( ${item.lowerProperty} == 'createDate')
                    #elseif( ${item.lowerProperty} == 'updateDate')
                    #elseif( ${item.lowerProperty} == 'updateBy')
                    #elseif(${item.jdbcType}  =='BIT')
                        ,{field:'${item.lowerProperty}', templet: '#${item.lowerProperty}Tpl', title:'${item.remark}', width:80}

                    #else
                        ,{field:'${item.lowerProperty}', title:'${item.remark}', width:120}
                    #end
                #end
                ,{field:'createBy', title:'创建者', width:80}
                ,{field:'createDate', title:'创建时间', width:100}
                ,{field:'updateBy', title:'修改者', width:80}
                ,{field:'updateDate', title:'修改时间', width:100}
                ,{fixed: 'right', title:'操作', toolbar: '#${lowerBeanName}bar', width:200}
            ]]
            ,response: {
                statusCode: 200 //重新规定成功的状态码为 200，table 组件默认为 0
            }
            ,parseData: function(res){ //将原始数据解析成 table 组件所规定的数据
                return {
                    "code": res.code, //解析接口状态
                    "msg": res.msg, //解析提示文本
                    "count": res.total, //解析数据长度
                    "data": res.data //解析数据列表
                };
            }
            ,page: true
        });
        Callback=function(result,obj){
            if(result.code==200){
                //
                obj.del();
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">删除成功！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        //   window.parent.location.reload();
                    }
                });
            }else{
                layer.open({
                    type: 1
                    , content: '<div style="padding: 20px 100px;">'+result.message+'删除失败！</div>'
                    , btn: '关闭'
                    , btnAlign: 'c' //按钮居中
                    , shade: 0 //不显示遮罩
                    , yes: function () {
                        layer.closeAll();
                        //  window.parent.location.reload();
                    }
                });
            }
        }
        //头工具栏事件
        table.on('toolbar(${lowerBeanName}_table)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'ApplicationAdd':
                    addShow('添加','./add',600,400);
                    var data = checkStatus.data;
                    layer.alert(JSON.stringify(data));
                    break;
            };
        });
        //监听行工具事件
        table.on('tool(${lowerBeanName}_table)', function(obj){
            var f = obj.data;

            var param={
            #foreach($item in $columns)
                #if($velocityCount == 1)

                    #if(${item.jdbcType}  =='BIT')
                        ${item.lowerProperty}:f.${item.lowerProperty}==undefined?0:1

                    #else
                        ${item.lowerProperty}:f.${item.lowerProperty}
                    #end
                #else
                    #if(${item.jdbcType}  =='BIT')
                            ,${item.lowerProperty}:f.${item.lowerProperty}==undefined?0:1

                    #else
                            ,${item.lowerProperty}:f.${item.lowerProperty}
                    #end

                #end
            #end

        };
            if(obj.event === 'del'){
                layer.confirm('是否确认删除？', function(index){
                    api(param,'POST',deleteUrl,Callback,obj);
                    // obj.del();
                    layer.close(index);
                });
            } else if(obj.event === 'edit'){

                addShow('编辑','./edit?id='+f.id,600,400);

            }
        });

        reloadTable= function  (){
            var appm=  $('#appModule').val();
            var daterange=  $('#dateRangePick').val();
            table.reload('${lowerBeanName}_table', {
                page: {
                    curr: 1 //重新从第 1 页开始
                }
                ,where: {
                    appname: appm,
                    begindate: daterange.substr(0, 10),
                    enddate: daterange.substr(13, 10)
                }
            });
            laydate.render({
                elem: '#dateRangePick'
                ,type: 'date'
                ,range: true //或 range: '~' 来自定义分割字符
            });
        }

        addShow = function(title, url, w, h) {
            if(w == null || w == '') {
                w = ($(window).width() * 0.9);
            };
            if(h == null || h == '') {
                h = ($(window).height() - 50);
            };
            var index= layer.open({
                type: 2,
                area: [w + 'px', h + 'px'],
                fix: false, //不固定
                maxmin: true,
                shadeClose: true,
                shade: 0.4,
                title: title,
                content: url
            });
            // $(window).resize(function () {
            //     layer.full(index);
            // });
            layer.full(index);
        }

    });



</script>
</body>
</html>