package ${examplePackage};

import java.util.ArrayList;
import java.util.List;
#if (${hasDate})
import java.util.Date;
#end
#if (${hasBigdecimal})
import java.math.BigDecimal;
#end

/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date ${dateTime}
 * @version
 */
public class ${beanName}Example {

    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ${beanName}Example() {
		super();
		oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
		this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
		return orderByClause;
    }

    public void setDistinct(boolean distinct) {
		this.distinct = distinct;
    }

    public boolean isDistinct() {
		return distinct;
    }

    public List<Criteria> getOredCriteria() {
		return oredCriteria;
    }

    public void or(Criteria criteria) {
		oredCriteria.add(criteria);
    }

  

    public Criteria or() {
		Criteria criteria = createCriteriaInternal();
		oredCriteria.add(criteria);
		return criteria;
    }

    public Criteria createCriteria() {
		Criteria criteria = createCriteriaInternal();
		if (oredCriteria.size() == 0) {
		    oredCriteria.add(criteria);
		}
		return criteria;
    }

    protected Criteria createCriteriaInternal() {
		Criteria criteria = new Criteria();
		return criteria;
    }

    public void clear() {
		oredCriteria.clear();
		orderByClause = null;
		distinct = false;
    }

    protected abstract static class GeneratedCriteria {
 		protected List<Criterion> criteria;
		protected GeneratedCriteria() {
	   		 criteria = new ArrayList<Criterion>();
		}

		public boolean isValid() {
		    return criteria.size() > 0;
		}
	
		public List<Criterion> getAllCriteria() {
		    return criteria;
		}
	
		public List<Criterion> getCriteria() {
		    return criteria;
		}
	

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }
	
		public Criteria join(String condition) {
		    addCriterion(condition);
		    return (Criteria) this;
		}
		#foreach ( $item in $columns)
	
		public Criteria and${item.property}IsNull() {
		    addCriterion("${item.column} is null");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}IsNotNull() {
		    addCriterion("${item.column} is not null");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}EqualTo(${item.type} value) {
		    addCriterion("${item.column} =", value, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}NotEqualTo(${item.type} value) {
		    addCriterion("${item.column} <>", value, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}GreaterThan(${item.type} value) {
		    addCriterion("${item.column} >", value, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}GreaterThanOrEqualTo(${item.type} value) {
		    addCriterion("${item.column} >=", value, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}LessThan(${item.type} value) {
		    addCriterion("${item.column} <", value, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}LessThanOrEqualTo(${item.type} value) {
		    addCriterion("${item.column} <=", value, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}In(List<${item.type}> values) {
		    addCriterion("${item.column} in", values, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}NotIn(List<${item.type}> values) {
		    addCriterion("${item.column} not in", values, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}Between(${item.type} value1, ${item.type} value2) {
		    addCriterion("${item.column} between", value1, value2, "${item.lowerProperty}");
		    return (Criteria) this;
		}
	
		public Criteria and${item.property}NotBetween(${item.type} value1, ${item.type} value2) {
		    addCriterion("${item.column} not between", value1, value2, "${item.lowerProperty}");
		    return (Criteria) this;
		}
		
		#end
    }

    public static class Criteria extends GeneratedCriteria {

		protected Criteria() {
		    super();
		}
    }
    
     /**
     * <AUTHOR>
	 * @date ${dateTime}
     * @version 
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

}