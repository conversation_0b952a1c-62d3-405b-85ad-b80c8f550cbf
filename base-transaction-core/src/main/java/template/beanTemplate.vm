package ${modelPackage};
import javax.persistence.*;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import org.hibernate.validator.constraints.Length;
import com.huafagroup.core.entity.BaseEntity;
#if (${hasDate})
import java.util.Date;
#end

#if (${hasBigdecimal})

import java.math.BigDecimal;
#end

/**
* <p>
    *
    *${remark}
    *
    * </p>
*
* <AUTHOR>
* @date ${dateTime}
* @version
*/
@Table(name = "${tableName}")
public class ${beanName} extends BaseEntity implements Serializable {

#foreach ($item in $columns)
    #if (!${baseColumns.contains($item.lowerProperty)})
        #if (${item.remark} && ${item.remark}!='' )
        /**
        * ${item.remark}
        */
        #end
        #if ( ${item.type} == "String" )
        @Length(max=${item.maxLength},message="${item.remark} 长度不能超过${item.maxLength}")
        #end
        #if ( ${item.allowNull} == false )
            #if ( ${item.type} == "String" )
            @NotBlank(message = "${item.column} not allow null")
            #else
            @NotNull(message = "${item.column} not allow null")
            #end
        #end
    @Column(name = "${item.column}")
    private ${item.type} ${item.lowerProperty};
    #end
#end

#foreach ($item in $columns)
    #if (!${baseColumns.contains($item.lowerProperty)})

    public ${item.type} get${item.property}() {
    return ${item.lowerProperty};
    }

    public void set${item.property}(${item.type} ${item.lowerProperty}) {
    this.${item.lowerProperty} = ${item.lowerProperty};
    }

    #end
#end
}