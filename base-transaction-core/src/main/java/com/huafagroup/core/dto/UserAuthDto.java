package com.huafagroup.core.dto;


public class UserAuthDto {
    private String section;
    private String appId;
    private String userId;
    private String resourceId;
    private Integer level;
    private Integer sort;
    private String isFuncResource;
    private String parentResourceId;
    private String resourceType;
    private String url;
    private Boolean isResourceEnabled;
    private String resourceName;
    private String oprCode;
    private String roleId;
    private String roleName;
    private String parentRoleId;
    private int roleType;

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getIsFuncResource() {
        return isFuncResource;
    }

    public void setIsFuncResource(String isFuncResource) {
        this.isFuncResource = isFuncResource;
    }

    public String getParentResourceId() {
        return parentResourceId;
    }

    public void setParentResourceId(String parentResourceId) {
        this.parentResourceId = parentResourceId;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Boolean getResourceEnabled() {
        return isResourceEnabled;
    }

    public void setResourceEnabled(Boolean resourceEnabled) {
        isResourceEnabled = resourceEnabled;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getOprCode() {
        return oprCode;
    }

    public void setOprCode(String oprCode) {
        this.oprCode = oprCode;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getParentRoleId() {
        return parentRoleId;
    }

    public void setParentRoleId(String parentRoleId) {
        this.parentRoleId = parentRoleId;
    }

    public int getRoleType() {
        return roleType;
    }

    public void setRoleType(int roleType) {
        this.roleType = roleType;
    }

    @Override
    public String toString() {
        return "UserAuthDto{" +
                "section='" + section + '\'' +
                ", appId='" + appId + '\'' +
                ", userId='" + userId + '\'' +
                ", resourceId='" + resourceId + '\'' +
                ", level=" + level +
                ", sort=" + sort +
                ", isFuncResource='" + isFuncResource + '\'' +
                ", parentResourceId='" + parentResourceId + '\'' +
                ", resourceType='" + resourceType + '\'' +
                ", url='" + url + '\'' +
                ", isResourceEnabled=" + isResourceEnabled +
                ", resourceName='" + resourceName + '\'' +
                ", oprCode='" + oprCode + '\'' +
                ", roleId='" + roleId + '\'' +
                ", roleName='" + roleName + '\'' +
                ", parentRoleId='" + parentRoleId + '\'' +
                ", roleType=" + roleType +
                '}';
    }
}
