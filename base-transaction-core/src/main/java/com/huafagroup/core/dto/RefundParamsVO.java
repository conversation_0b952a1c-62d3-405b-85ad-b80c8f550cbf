package com.huafagroup.core.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
public class RefundParamsVO {

    /**
     * 退款原因
     */
    @Length(max = 128, message = "长度不能超过128")
    private String reason;
    /**
     * 支付平台订单号
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "支付平台订单号不允许为空")
    private String orderNo;
    /**
     * 业务系统回调通知地址
     */
    @Length(max = 256, message = "长度不能超过256")
    @NotBlank(message = "回调通知地址不允许为空")
    private String bizNotifyUrl;

}
