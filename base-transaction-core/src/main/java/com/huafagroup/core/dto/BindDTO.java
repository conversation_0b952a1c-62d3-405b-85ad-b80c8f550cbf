package com.huafagroup.core.dto;

import com.huafagroup.core.enums.ChannelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class BindDTO {
    @NotBlank
    private String projectId;
    @NotBlank
    private String mchId;
    @ApiModelProperty("1-收款，2：付款（激励）")
    private int bindType = 1;
    @NotBlank
    private String operator;
    /**
     * 渠道（如：置业通、社群）
     */
    private ChannelEnum channel;
}
