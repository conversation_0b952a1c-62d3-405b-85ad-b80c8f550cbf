package com.huafagroup.core.dto.juhe;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

@Data
public class RechargeMobileReq {
    /**
     * 	请求流水号
     */
    @NotEmpty(message = "请求流水号不能为空")
    private String reqNo;
    /**
     * 	手机号码
     */
    @NotEmpty(message = "手机号码不能为空")
    private String telNo;
    /**
     * 充值面额
     */
    @Min(value = 0)
    private int amount;
    /**
     * 	项目Id
     */
    private String projectId;
    /**
     * 活动id
     */
    private String actId;
    /**
     * 充值回调地址
     */
    @NotEmpty(message = "充值回调地址不能为空")
    private String callBackUrl;
    /***
     *  状态码
     */
    @NotEmpty(message = "状态码不能为空")
    private String state;
}
