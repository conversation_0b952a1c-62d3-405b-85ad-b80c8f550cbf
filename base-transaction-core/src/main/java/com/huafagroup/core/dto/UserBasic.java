package com.huafagroup.core.dto;

import java.util.Date;
import java.util.List;

public class UserBasic {

    private String id;
    private String createBy;
    private Date createDate;
    private Date updateDate;
    private String updateBy;
    /**
     * 性别 1: 男  2: 女
     */
    private String sex;
    /**
     * 身份证
     */
    private String idCard;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号码
     */
    private String tel;
    /**
     * 账号
     */
    private String account;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 用户状态 0: 禁用 1: 启用
     */
    private Boolean status;
    private String userId;

    private List<String> roleIds;

    private String orgId;

    public String getId() {
        return id;
    }

    public UserBasic setId(String id) {
        this.id = id;
        return this;
    }

    public String getCreateBy() {
        return createBy;
    }

    public UserBasic setCreateBy(String createBy) {
        this.createBy = createBy;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public UserBasic setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public UserBasic setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public UserBasic setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public String getSex() {
        return sex;
    }

    public UserBasic setSex(String sex) {
        this.sex = sex;
        return this;
    }

    public String getIdCard() {
        return idCard;
    }

    public UserBasic setIdCard(String idCard) {
        this.idCard = idCard;
        return this;
    }

    public String getName() {
        return name;
    }

    public UserBasic setName(String name) {
        this.name = name;
        return this;
    }

    public String getTel() {
        return tel;
    }

    public UserBasic setTel(String tel) {
        this.tel = tel;
        return this;
    }

    public String getAccount() {
        return account;
    }

    public UserBasic setAccount(String account) {
        this.account = account;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public UserBasic setEmail(String email) {
        this.email = email;
        return this;
    }

    public Integer getAge() {
        return age;
    }

    public UserBasic setAge(Integer age) {
        this.age = age;
        return this;
    }

    public Boolean getStatus() {
        return status;
    }

    public UserBasic setStatus(Boolean status) {
        this.status = status;
        return this;
    }

    public String getUserId() {
        return userId;
    }

    public UserBasic setUserId(String userId) {
        this.userId = userId;
        return this;
    }

    public List<String> getRoleIds() {
        return roleIds;
    }

    public UserBasic setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
        return this;
    }

    public String getOrgId() {
        return orgId;
    }

    public UserBasic setOrgId(String orgId) {
        this.orgId = orgId;
        return this;
    }
}