package com.huafagroup.core.dto.wx;

import com.huafagroup.core.dto.PayOrderResultVO;
import lombok.Data;

/**
 * 支付请求返回的结果
 */
@Data
public class WxAppPayOrderResultVO extends PayOrderResultVO {
    /**
     * 公众号ID，由商户传入
     */
    private String appId;
    /**
     * 商户号
     */
    private String partnerId;
    /**
     * 时间戳，自1970年以来的秒数
     */
    private String timeStamp;
    /**
     * 随机串
     */
    private String nonceStr;

    /**
     * 预支付交易会话ID
     */
    private String prepayId;

    private String packageValue = "Sign=WXPay";
    /**
     * 签名
     */
    private String sign;

    public WxAppPayOrderResultVO(String appId, String partnerId, String timeStamp, String nonceStr, String prepayId, String sign) {
        this.appId = appId;
        this.partnerId = partnerId;
        this.timeStamp = timeStamp;
        this.nonceStr = nonceStr;
        this.prepayId = prepayId;
        this.sign = sign;
    }

}
