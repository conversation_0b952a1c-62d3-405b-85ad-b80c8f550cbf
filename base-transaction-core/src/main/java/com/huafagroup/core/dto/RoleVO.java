package com.huafagroup.core.dto;

import java.util.Date;

/**
 * @author: admin
 * @dateTime: 2020/1/15 16:10
 **/
public class RoleVO {
    /**
     * 角色类型
     * 1: 授权角色
     * 2: 业务角色
     */

    private String roleType;
    /**
     * 角色名称
     */
    private String name;
    /**
     * 所属版块
     */
    private String section;
    /**
     * 父角色Id
     */
    private String parentRoleId;
    /**
     * 所属系统/应用
     */
    private String appId;

    private Boolean enabled;

    private String rolePath;
    private String roleStanding;
    private String roleId;
    private String subRoleId;
    private String appName;
    private String id;
    private String createBy;
    private Date createDate;
    private Date updateDate;

    public String getRoleType() {
        return roleType;
    }

    public RoleVO setRoleType(String roleType) {
        this.roleType = roleType;
        return this;
    }

    public String getName() {
        return name;
    }

    public RoleVO setName(String name) {
        this.name = name;
        return this;
    }

    public String getSection() {
        return section;
    }

    public RoleVO setSection(String section) {
        this.section = section;
        return this;
    }

    public String getParentRoleId() {
        return parentRoleId;
    }

    public RoleVO setParentRoleId(String parentRoleId) {
        this.parentRoleId = parentRoleId;
        return this;
    }

    public String getAppId() {
        return appId;
    }

    public RoleVO setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public RoleVO setEnabled(Boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public String getRolePath() {
        return rolePath;
    }

    public RoleVO setRolePath(String rolePath) {
        this.rolePath = rolePath;
        return this;
    }

    public String getRoleStanding() {
        return roleStanding;
    }

    public RoleVO setRoleStanding(String roleStanding) {
        this.roleStanding = roleStanding;
        return this;
    }

    public String getRoleId() {
        return roleId;
    }

    public RoleVO setRoleId(String roleId) {
        this.roleId = roleId;
        return this;
    }

    public String getSubRoleId() {
        return subRoleId;
    }

    public RoleVO setSubRoleId(String subRoleId) {
        this.subRoleId = subRoleId;
        return this;
    }

    public String getAppName() {
        return appName;
    }

    public RoleVO setAppName(String appName) {
        this.appName = appName;
        return this;
    }

    public String getId() {
        return id;
    }

    public RoleVO setId(String id) {
        this.id = id;
        return this;
    }

    public String getCreateBy() {
        return createBy;
    }

    public RoleVO setCreateBy(String createBy) {
        this.createBy = createBy;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public RoleVO setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public RoleVO setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }
}
