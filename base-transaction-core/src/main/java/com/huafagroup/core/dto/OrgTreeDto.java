package com.huafagroup.core.dto;

import java.util.List;

public class OrgTreeDto {

    /**
     * id
     */
    private String id;

    /**
     * 1-同步，2-手动
     */
    private Integer createType;

    /**
     * 父级
     */
    private String parentId;

    /**
     * 名称
     */
    private String name;

    /**
     * 0-area;1-city;2-org;3-project
     */
    private Integer type;


    private List<OrgTreeDto> children;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getCreateType() {
        return createType;
    }

    public void setCreateType(Integer createType) {
        this.createType = createType;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<OrgTreeDto> getChildren() {
        return children;
    }

    public void setChildren(List<OrgTreeDto> children) {
        this.children = children;
    }
}
