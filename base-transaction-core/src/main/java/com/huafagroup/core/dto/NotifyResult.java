package com.huafagroup.core.dto;

import lombok.Data;

@Data
public class NotifyResult {



    private String code;

    private String message;

    public NotifyResult(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static NotifyResult success(){
        return new NotifyResult("SUCCESS","成功");
    }

    public boolean isSuccess(){
        return "SUCCESS".equals(code);
    }

}
