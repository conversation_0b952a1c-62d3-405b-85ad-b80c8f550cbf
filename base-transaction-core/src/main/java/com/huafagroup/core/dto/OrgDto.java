package com.huafagroup.core.dto;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class OrgDto extends BaseDto implements Serializable {
    private String parentOrgId;
    @NotNull(message="组织类型不能为空")
    private Integer orgCategory;
    private Boolean enabled;
    private String orgSection;
    @NotNull(message="排序不能为空")
    private Integer orgOrder;
    private String fullName;
    private Integer orgLevel;
    private String orgPath;
    @NotNull(message="组织编码不能为空")
    private String orgNo;
    @NotNull(message="组织名称不能为空")
    private String orgName;
    private String orgCategoryValue;
    private String orgSectionValue;

    public String getParentOrgId() {
        return parentOrgId;
    }

    public void setParentOrgId(String parentOrgId) {
        this.parentOrgId = parentOrgId;
    }

    public Integer getOrgCategory() {
        return orgCategory;
    }

    public void setOrgCategory(Integer orgCategory) {
        this.orgCategory = orgCategory;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getOrgSection() {
        return orgSection;
    }

    public void setOrgSection(String orgSection) {
        this.orgSection = orgSection;
    }

    public Integer getOrgOrder() {
        return orgOrder;
    }

    public void setOrgOrder(Integer orgOrder) {
        this.orgOrder = orgOrder;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Integer getOrgLevel() {
        return orgLevel;
    }

    public void setOrgLevel(Integer orgLevel) {
        this.orgLevel = orgLevel;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public String getOrgNo() {
        return orgNo;
    }

    public void setOrgNo(String orgNo) {
        this.orgNo = orgNo;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCategoryValue() {
        return orgCategoryValue;
    }

    public void setOrgCategoryValue(String orgCategoryValue) {
        this.orgCategoryValue = orgCategoryValue;
    }

    public String getOrgSectionValue() {
        return orgSectionValue;
    }

    public void setOrgSectionValue(String orgSectionValue) {
        this.orgSectionValue = orgSectionValue;
    }

    @Override
    public int hashCode() {
        return getId() != null ? getId().hashCode() : 0;
    }

    @Override
    public boolean equals(Object obj) {
        if ((obj instanceof OrgDto) == false)
            return false;

        OrgDto dto = (OrgDto) obj;
        return this.getId().equals(dto.getId());
    }

    @Override
    public String toString() {
        return "OrgDto{" +
                "parentOrgId='" + parentOrgId + '\'' +
                ", orgCategory=" + orgCategory +
                ", enabled=" + enabled +
                ", orgSection='" + orgSection + '\'' +
                ", orgOrder=" + orgOrder +
                ", fullName='" + fullName + '\'' +
                ", orgLevel=" + orgLevel +
                ", orgPath='" + orgPath + '\'' +
                ", orgNo='" + orgNo + '\'' +
                ", orgName='" + orgName + '\'' +
                ", orgCategoryValue='" + orgCategoryValue + '\'' +
                ", orgSectionValue='" + orgSectionValue + '\'' +
                '}';
    }
}
