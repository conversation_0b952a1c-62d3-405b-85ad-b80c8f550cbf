package com.huafagroup.core.dto.wx;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.huafagroup.core.dto.TransferResultVO;
import lombok.Data;

/**
 * 发送小程序红包返回的结果
 */
@Data
public class MiniProHbResultVO extends TransferResultVO {
    /**
     * 时间戳，自1970年以来的秒数
     */
    private String timeStamp;
    /**
     * 随机串
     */
    private String nonceStr;

    @JsonProperty("package")
    private String packageStr;
    /**
     * 微信签名方式
     */
    private String signType = "MD5";
    /**
     * 签名
     */
    private String paySign;

    public MiniProHbResultVO(String timeStamp, String nonceStr, String packageStr, String paySign) {
        this.timeStamp = timeStamp;
        this.nonceStr = nonceStr;
        this.packageStr = packageStr;
        this.paySign = paySign;
    }
}
