package com.huafagroup.core.dto;

import com.huafagroup.core.enums.PayProviderEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.enums.ChannelEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class PayParamsVO {

    /**
     * 项目号
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "项目号不允许为空")
    private String projectId;
    /**
     * 公众号或小程序id
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "公众号或小程序id不能为空")
    private String appId;
    /**
     * 描述
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "描述不允许为空")
    private String description;
    /**
     * 用户在直连商户appid下的唯一标识
     */
    private String openid;
    /**
     * 用户的客户端IP
     */
    private String clientIp;

    /**
     * 业务系统内部订单号
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "业务系统内部订单号不允许为空")
    private String bizOrderNo;
    /**
     * 业务系统回调通知地址
     */
    @Length(max = 256, message = "长度不能超过256")
    @NotBlank(message = "回调通知地址不允许为空")
    private String bizNotifyUrl;
    /**
     * 订单金额，单位分
     */
    @Digits(integer = 10,fraction = 0)
    @Min(1)
    private int amount;
    /**
     * 过期的分钟数
     */
    @Digits(integer = 10,fraction = 0)
    @Min(1)
    private int expireMinutes;

    /**
     * 渠道（如：置业通、社群）
     */
    @NotNull
    private ChannelEnum channel;

    private String providerEnum = PayProviderEnum.WECHAT.name();

    private ApiTypeEnum apiTypeEnum = ApiTypeEnum.JS_API_PAY;
}
