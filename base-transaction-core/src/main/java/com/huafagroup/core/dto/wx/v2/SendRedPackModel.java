package com.huafagroup.core.dto.wx.v2;

import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.wx.BaseModel;
import com.huafagroup.core.entity.trans.HbOrder;
import lombok.Data;

/**
 * 微信企业零钱参数模型
 */
@Data
public class SendRedPackModel extends BaseModel {
	private String wxappid;
	private String mchId;
	private String nonceStr;
	private String mchBillno;
	private String reOpenid;
	private String sendName;
	private String wishing;
	private String totalAmount;
	private String totalNum = "1";
	private String clientIp;
	private String actName;
	private String remark;
	private String sceneId;
	private String notifyWay;

	public void toRedPack(HbOrder hbOrder, TransactionConfig transactionConfig) {
		baseRedPack(hbOrder);
		setClientIp(transactionConfig.getRedPackClientIP());
	}


	public void toMiniRedPack(HbOrder hbOrder) {
		baseRedPack(hbOrder);
		setNotifyWay("MINI_PROGRAM_JSAPI");
	}

	private void baseRedPack(HbOrder hbOrder) {
		setNonceStr(hbOrder.getNonceStr());
		setRemark(hbOrder.getDesc());
		setTotalAmount(hbOrder.getAmount().toString());
		setMchId(hbOrder.getMchId());
		setWxappid(hbOrder.getAppId());
		setMchBillno(hbOrder.getOrderNo());
		setReOpenid(hbOrder.getOpenid());
		setActName(hbOrder.getActName());
		//发放红包使用场景，红包金额大于200时必传或者小于1元时必传
		if (hbOrder.getAmount() < 100 || hbOrder.getAmount() > 20000) {
			setSceneId(hbOrder.getSceneId());
		}
		setSendName(hbOrder.getSendName());
		setWishing(hbOrder.getWishing());
	}
}