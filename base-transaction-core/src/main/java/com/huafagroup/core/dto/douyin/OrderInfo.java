package com.huafagroup.core.dto.douyin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/***
 * 抖音支付封装
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderInfo {

    /**
     * 商户号
     */
    @JsonProperty("merchant_id")
    private String merchantId;

    /**
     * app_id
     */
    @JsonProperty("app_id")
    private String appId;

    /**
     * 固定值：MD5。商户生成签名的算法类型
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * 发送请求的时间戳，精确到秒
     */
    private String timestamp;

    /**
     * 固定值：2.0
     */
    private String version;

    /**
     * 固定值：H5
     */
    @JsonProperty("trade_type")
    private String tradeType;

    /**
     * 签名
     */
    private String sign;

    /**
     * 固定值：pay
     */
    @JsonProperty("product_code")
    private String productCode;

    /**
     * 固定值：direct
     */
    @JsonProperty("payment_type")
    private String paymentType;

    /**
     * 商户订单号
     */
    @JsonProperty("out_order_no")
    private String outOrderNo;

    /**
     * 固定值：同 app_id
     */
    private String uid;

    /**
     * 金额，整型，单位：分（不能有小数）
     */
    @JsonProperty("total_amount")
    private Integer totalAmount;

    /**
     * 固定值: CNY。币种
     */
    private String currency;

    /**
     * 商户订单名称
     */
    private String subject;

    /**
     * 商户订单详情
     */
    private String body;

    /**
     * 下单时间戳，精确到秒。如果两次支付(调用 tt.pay)传入的订单号(out_order_no)相同，则必须保证 trade_time 也相同。
     */
    @JsonProperty("trade_time")
    private String tradeTime;

    /**
     * 订单有效时间（单位 秒）
     */
    @JsonProperty("valid_time")
    private String validTime;

    /**
     * 固定值：https://tp-pay.snssdk.com/paycallback
     */
    @JsonProperty("notify_url")
    private String notifyUrl;

    /**
     * 调用微信 H5 支付统一下单接口 返回的 mweb_url 字段值（请注意不要进行 urlencode）。
     * service=1 时，如不传则不展示微信支付；
     * service=3 时必传。
     */
    @JsonProperty("wx_url")
    private String wxUrl;

    /**
     * wx_url 非空时传 'MWEB'。wx_url 为空时，该字段不传
     */
    @JsonProperty("wx_type")
    private String wxType;

    /**
     * 支付风控参数。序列化后的 JSON 结构字符串，JSON 结构如下：{ip: '用户外网IP'}
     */
    @JsonProperty("risk_info")
    private String riskInfo;

    @JsonProperty("un_sign_str")
    private String unSignStr;
}
