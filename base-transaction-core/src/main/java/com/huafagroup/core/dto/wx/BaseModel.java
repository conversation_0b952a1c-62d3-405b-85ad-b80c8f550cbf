package com.huafagroup.core.dto.wx;

import cn.hutool.crypto.digest.DigestUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import lombok.Getter;

@Getter
public class BaseModel {
    private static final XmlMapper XML_MAPPER = new XmlMapper();
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private String sign;

    static {
        // 忽略null
        XML_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL)
                // 属性使用 驼峰首字母小写
                .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        OBJECT_MAPPER.configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
    }

    public String toXml(String appSecret) {
        try {
            this.sign = doSign(appSecret);
            return XML_MAPPER.writer()
                    .withRootName("xml")
                    .writeValueAsString(this);
        } catch (Exception e) {
            throw new BusinessException(ExceptionEnum.PARAMS_NOT_VALID.getCode(),"构建激励参数的xml异常：" + e.getMessage());
        }
    }

    private String doSign(String appSecret) throws JsonProcessingException {
        String link = OBJECT_MAPPER
                .writer()
                .writeValueAsString(this)
                .replaceAll("\":\"", "=")
                .replaceAll("\",\"", "&")
                .replaceAll("\\\\\"", "\"");
        String result = link.substring(2, link.length() - 2).concat("&key=").concat(appSecret);
        return DigestUtil.md5Hex(result).toUpperCase();
    }

}
