package com.huafagroup.core.dto;

import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.enums.ChannelEnum;
import com.huafagroup.core.enums.PayProviderEnum;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Digits;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
public class TransferParamsVO {

    /**
     * 项目号
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "项目号不允许为空")
    private String projectId;
    /**
     * 公众号或小程序id
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "公众号或小程序id不能为空")
    private String appId;
    /**
     * 描述
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "描述不允许为空")
    private String description;
    /**
     * 用户在直连商户appid下的唯一标识
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "openid不允许为空")
    private String openid;
    /**
     * 业务系统内部订单号
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "业务系统内部订单号不允许为空")
    private String bizOrderNo;
    /**
     * 订单金额，单位分
     */
    @Digits(integer = 10,fraction = 0)
    @Min(1)
    private int amount;

    /**
     * 红包发送者名称
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "红包发送者名称不允许为空")
    private String sendName;

    /**
     * 业务系统内部订单号
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "红包祝福语不允许为空")
    private String wishing;

    /**
     * 活动名称
     */
    @Length(max = 64, message = "长度不能超过64")
    @NotBlank(message = "活动名称不允许为空")
    private String actName;


    private String providerEnum = PayProviderEnum.WECHAT.name();

    private ChannelEnum channel = ChannelEnum.ZYT;

    private ApiTypeEnum apiTypeEnum = ApiTypeEnum.PROMOTION_SENDREDPACK_V2;
}
