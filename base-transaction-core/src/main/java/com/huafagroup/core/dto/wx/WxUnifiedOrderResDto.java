package com.huafagroup.core.dto.wx;

import com.huafagroup.core.dto.PayOrderResultVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *  微信订单返回信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020/8/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxUnifiedOrderResDto extends PayOrderResultVO {

    public static final String RETURN_CODE_SUCCESS = "SUCCESS";

    public static final String RETURN_CODE_FAIL = "FAIL";

    /**
     * 返回状态码
     */
    private String returnCode;

    /**
     * 返回信息
     */
    private String returnMsg;
    /**
     * 公众账号ID
     */
    private String appid;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 设备号
     */
    private String deviceInfo;

    /**
     * 随机字符串
     */
    private String nonceStr;

    /**
     * 签名
     */
    private String sign;

    /**
     * 业务结果
     */
    private String resultCode;

    /**
     * 错误代码
     */
    private String errCode;

    /**
     * 错误代码描述
     */
    private String errCodeDes;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 预支付交易会话标识
     */
    private String prepayId;

    /**
     * 支付跳转链接
     */
    private String mwebUrl;

    /**
     * 回调退款状态
     */
    private String refundStatus;

    /**
     * 第一笔退款状态
     */
    private String refundStatus0;

    /**
     * 第二笔退款状态
     */
    private String refundStatus1;

    /**
     * 加密信息
     */
    private String reqInfo;

    /**
     * 微信订单号
     */
    private String transactionId;

    /**
     * 退款成功时间
     */
    private String successTime;
}
