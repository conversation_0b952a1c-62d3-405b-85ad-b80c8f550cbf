package com.huafagroup.core.dto;

import java.util.Date;

/**
 * @author: admin
 * @dateTime: 2020/6/22 10:24
 **/
public class AuthApplicationDetail {
    private String appid;
    private String authType;
    private String createBy;
    private String description;
    private String name;
    private String owner;
    private String section;
    private String sectionText;
    private String updateBy;
    private String id;
    private Date createDate;
    private Date updateDate;

    public String getAppid() {
        return appid;
    }

    public AuthApplicationDetail setAppid(String appid) {
        this.appid = appid;
        return this;
    }

    public String getAuthType() {
        return authType;
    }

    public AuthApplicationDetail setAuthType(String authType) {
        this.authType = authType;
        return this;
    }

    public String getCreateBy() {
        return createBy;
    }

    public AuthApplicationDetail setCreateBy(String createBy) {
        this.createBy = createBy;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public AuthApplicationDetail setDescription(String description) {
        this.description = description;
        return this;
    }

    public String getName() {
        return name;
    }

    public AuthApplicationDetail setName(String name) {
        this.name = name;
        return this;
    }

    public String getOwner() {
        return owner;
    }

    public AuthApplicationDetail setOwner(String owner) {
        this.owner = owner;
        return this;
    }

    public String getSection() {
        return section;
    }

    public AuthApplicationDetail setSection(String section) {
        this.section = section;
        return this;
    }

    public String getSectionText() {
        return sectionText;
    }

    public AuthApplicationDetail setSectionText(String sectionText) {
        this.sectionText = sectionText;
        return this;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public AuthApplicationDetail setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public String getId() {
        return id;
    }

    public AuthApplicationDetail setId(String id) {
        this.id = id;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public AuthApplicationDetail setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public AuthApplicationDetail setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }
}
