package com.huafagroup.core.dto;

import cn.hutool.core.bean.BeanUtil;
import com.huafagroup.core.entity.trans.Merchant;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2024/12/19 14:43
 */
@Data
public class CreateOrUpdateMerchantDTO {
    private Integer id;
    /**
     * Api V3秘钥
     */
    @NotBlank
    private String apiv3Key;
    /**
     * Api 秘钥
     */
    @NotBlank
    private String apiKey;
    /**
     * 支持付款标识：1：支持，0：不支持
     */
    private Integer payFlag;
    /**
     * 支付证书url
     */
    @NotBlank
    private String certificateUrl;
    /**
     * 微信商户号
     */
    @NotBlank
    private String mchId;

    /**
     * 微信商户名称
     */
    private String mchName;
    @NotBlank
    private String merchantSubject;
    @NotBlank
    private String managerName;
    @NotBlank
    private String managerPhone;
    //微信支付公钥路径
    private String wechatPayPublicKeyUrl;
    //微信支付公钥ID
    private String wechatPayPublicKeyId;
    @NotBlank
    private String operator;

    public Merchant toEntity() {
        return BeanUtil.copyProperties(this, Merchant.class);
    }
}
