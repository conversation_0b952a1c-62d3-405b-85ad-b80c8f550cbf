package com.huafagroup.core.dto;

import java.util.Date;

/**
 * @author: admin
 * @dateTime: 2020/6/23 17:03
 **/
public class AuthResourceDTO {
    private String id;
    private String type;//资源类型 1 代表 菜单，2代表URL，3代表文件，4代表或字段
    private String appId;//应用ID
    private String url;//数据
    private String name;//名称
    private String oprCode;//操作权限
    private Date createDate;
    private Date updateDate;
    private String updateBy;
    private String createBy;

    public String getId() {
        return id;
    }

    public AuthResourceDTO setId(String id) {
        this.id = id;
        return this;
    }

    public String getType() {
        return type;
    }

    public AuthResourceDTO setType(String type) {
        this.type = type;
        return this;
    }

    public String getAppId() {
        return appId;
    }

    public AuthResourceDTO setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public String getUrl() {
        return url;
    }

    public AuthResourceDTO setUrl(String url) {
        this.url = url;
        return this;
    }

    public String getName() {
        return name;
    }

    public AuthResourceDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getOprCode() {
        return oprCode;
    }

    public AuthResourceDTO setOprCode(String oprCode) {
        this.oprCode = oprCode;
        return this;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public AuthResourceDTO setCreateDate(Date createDate) {
        this.createDate = createDate;
        return this;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public AuthResourceDTO setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
        return this;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public AuthResourceDTO setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public String getCreateBy() {
        return createBy;
    }

    public AuthResourceDTO setCreateBy(String createBy) {
        this.createBy = createBy;
        return this;
    }
}
