package com.huafagroup.core.dto.wx;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.huafagroup.core.dto.PayOrderResultVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 支付请求返回的结果
 */
@Data
public class WxPayOrderResultVO extends PayOrderResultVO {
    /**
     * 公众号ID，由商户传入
     */
    private String appId;
    /**
     * 时间戳，自1970年以来的秒数
     */
    private String timeStamp;
    /**
     * 随机串
     */
    private String nonceStr;

    @JsonProperty("package")
    private String packageStr;
    /**
     * 微信签名方式
     */
    private String signType = "RSA";
    /**
     * 签名
     */
    private String paySign;

    public WxPayOrderResultVO(String appId, String timeStamp, String nonceStr, String packageStr, String paySign) {
        this.appId = appId;
        this.timeStamp = timeStamp;
        this.nonceStr = nonceStr;
        this.packageStr = packageStr;
        this.paySign = paySign;
    }
}
