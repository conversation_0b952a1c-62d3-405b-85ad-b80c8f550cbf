package com.huafagroup.core.service.assist;

import cn.hutool.json.JSONObject;
import com.huafagroup.core.dto.wx.MerchantProperties;
import org.springframework.http.ResponseEntity;

/**
 * 执行
 */
public interface HttpBaseCommand<R> {

    String CONTENT_TYPE = "application/json";

    /**
     * 获取主域名地址
     * @return
     */
    String getDomain();

    /**
     * 获取Api地址
     * @return
     */
    String getApi();

    /**
     * 准备请求参数
     * @return
     */
    String prepareParams();

    /**
     * 处理请求结果
     * @param responseEntity
     * @return
     */
    R processResult(ResponseEntity<JSONObject> responseEntity);

    MerchantProperties getMerchantProperties();

}
