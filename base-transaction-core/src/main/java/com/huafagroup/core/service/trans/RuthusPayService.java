package com.huafagroup.core.service.trans;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.circle.trading.CircleTradingClient;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.TransferResultVO;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.IPayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 原圈科技支付服务类，目前主要是发激励（企业付款方式）
 */
@Service
public class RuthusPayService implements IPayService {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private TransactionConfig transactionConfig;

    @Autowired
    private MerchantService merchantService;

    @Override
    public TransferResultVO transfer(HbOrder order, ApiTypeEnum apiTypeEnum) throws Exception {

        Merchant merchant = merchantService.getByMchId(order.getMchId());
        CircleTradingClient tradingClient = new CircleTradingClient(
                merchant.getApiKey(),merchant.getPrivateKey(),
                merchant.getCertificateUrl(),merchant.getMchSerialNo()
        );
        logger.info("调用原圈转账接口，订单号为{}，金额为{}",order.getOrderNo(),order.getAmount());
        String result = tradingClient.transfer(merchant.getApiV3Key(),order.getAmount(),
                order.getOrderNo(),"wxpay","WX_CASH",order.getAppId(),order.getOpenid(),
                order.getReUserName(),order.getDesc(),null,null);
        //{"code":0,"data":{"accountNo":"osIFt5fumMguHIIwUTA7LgsH5xiY","amount":30,"channelOrderNo":"10100138058532109079501717723839","mchOrderNo":"NHPmPC4clyz5xyfhLFAU1G1KifRq","state":2,"transferId":"T202109071050228160040"},"msg":"SUCCESS"}
        logger.info("返回结果为：{}",result);
        JSONObject jsonResult = JSONUtil.parseObj(result);
        Integer code = jsonResult.getInt("code");
        String errorMsg = jsonResult.getStr("msg");
        if (code == 0) {
            JSONObject dataJson = jsonResult.getJSONObject("data");
            Integer status = dataJson.getInt("state");
            if (status == 2) {//成功
                TransferResultVO vo = new TransferResultVO();
                vo.setStatus(OrderStatusEnum.SUCCESS.name());
                vo.setOrderNo(order.getOrderNo());
                Date now = new Date();
                vo.setSuccessTime(DateUtil.formatDateTime(now));
                order.setStatus(OrderStatusEnum.SUCCESS.name());
                order.setTransactionId(dataJson.getStr("transferId"));
                order.setSuccessTime(now);
                return vo;
            } else {
                errorMsg = dataJson.getStr("errMsg");
            }
        }
        throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), errorMsg);
    }
}
