package com.huafagroup.core.service.assist;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.RefundsResultVO;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.service.trans.WechatPayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * 微信付款执行命令
 */
public class WechatRefundCommand implements HttpBaseCommand<RefundsResultVO>{

    Logger logger = LoggerFactory.getLogger(this.getClass());


    private Order order;
    private MerchantProperties payProperties;
    private TransactionConfig transactionConfig;



    public WechatRefundCommand(Order order, MerchantProperties payProperties, TransactionConfig transactionConfig) {
        this.order = order;
        this.payProperties = payProperties;
        this.transactionConfig = transactionConfig;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return ApiTypeEnum.DOMESTIC_REFUNDS.toString();
    }

    @Override
    public String prepareParams() {

        JSONObject amount = new JSONObject();
        amount.put("total", order.getAmount());
        amount.put("refund", order.getAmount());
        amount.put("currency", "CNY");

        JSONObject reqJson = new JSONObject();
        reqJson.put("amount", amount);
        reqJson.put("out_trade_no", order.getOrderNo());
        reqJson.put("out_refund_no", order.getOrderNo());
        reqJson.put("reason", order.getRefundReason());
        reqJson.put("notify_url", StrUtil.format(transactionConfig.getRefundsNotifyUrl(),order.getOrderNo()));

        return reqJson.toString();
    }

    @Override
    public RefundsResultVO processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        if (statusCode == HttpStatus.OK) {
            WechatPayService.processRefundResult(order,resultJson);

            RefundsResultVO resultVO = new RefundsResultVO(order.getOrderNo(),
                    order.getBizOrderNo(),order.getRefundStatus(),order.getRefundPayerRefund());
            return resultVO;
        } else {
            logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("code"), resultJson.getStr("message"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return payProperties;
    }
}
