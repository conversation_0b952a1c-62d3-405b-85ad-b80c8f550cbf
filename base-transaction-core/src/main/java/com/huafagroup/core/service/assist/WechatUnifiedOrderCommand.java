package com.huafagroup.core.service.assist;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.PayOrderResultVO;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.dto.wx.WxUnifiedOrderResDto;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.util.wx.WeiXinPayCommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.text.SimpleDateFormat;
import java.util.TreeMap;

/**
 * 统一下单执行命令
 */
public class WechatUnifiedOrderCommand implements HttpBaseCommand<PayOrderResultVO>{
    private static final Logger LOGGER = LoggerFactory.getLogger(WechatUnifiedOrderCommand.class);
    private Order order;
    private MerchantProperties payProperties;
    private TransactionConfig transactionConfig;

    public WechatUnifiedOrderCommand(Order order, MerchantProperties payProperties, TransactionConfig transactionConfig) {
        this.order = order;
        this.payProperties = payProperties;
        this.transactionConfig = transactionConfig;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return ApiTypeEnum.PAY_UNIFIEDORDER.toString();
    }

    @Override
    public String prepareParams() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        TreeMap<String, Object> treeMap = new TreeMap<>();
        treeMap.put("appid", order.getAppId());
        treeMap.put("mch_id", payProperties.getMchId());
        treeMap.put("body", order.getDesc());
        treeMap.put("nonce_str", IdUtil.simpleUUID());
        treeMap.put("notify_url",  StrUtil.format(transactionConfig.getPayNotifyUrl(),order.getOrderNo()));
        treeMap.put("out_trade_no", order.getOrderNo());
        treeMap.put("spbill_create_ip", order.getClientIp());
        treeMap.put("total_fee", order.getAmount());
        treeMap.put("trade_type", order.getTradeType());
        treeMap.put("scene_info", "{\"h5_info\": {\"type\":\"MWEB\",\"app_name\": \"基础交易服务\",\"package_name\": \"basepay.cnhuafas.com\"}}");
        treeMap.put("time_start", dateFormat.format(order.getCreateDate()));
        treeMap.put("time_expire", dateFormat.format(order.getExpireTime()));
        String sign = WeiXinPayCommonUtil.createSign(payProperties.getApiKey(), "", treeMap);
        LOGGER.info("签名结果：{}", sign);
        treeMap.put("sign", sign);
        return WeiXinPayCommonUtil.getRequestXml(treeMap);
    }

    @Override
    public PayOrderResultVO processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        if (statusCode == HttpStatus.OK) {
            WxUnifiedOrderResDto wxOrderResDto = JSONUtil.toBean(resultJson, WxUnifiedOrderResDto.class);
            if (!WxUnifiedOrderResDto.RETURN_CODE_SUCCESS.equals(wxOrderResDto.getReturnCode())
                    || !WxUnifiedOrderResDto.RETURN_CODE_SUCCESS.equals(wxOrderResDto.getResultCode())){
                LOGGER.error("wechat unified order failed, body: {}", resultJson);
                throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), resultJson.toString());
            }
            return wxOrderResDto;
        }else {
            LOGGER.error("http request failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("code"), resultJson.getStr("message"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return payProperties;
    }
}
