package com.huafagroup.core.service.recharge;

import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.huafagroup.core.dto.juhe.RechargeMobileReq;
import com.huafagroup.core.dto.juhe.RechargeMobileResp;
import com.huafagroup.core.dto.juhe.RechargeMoneyCallBackReq;
import com.huafagroup.core.dto.juhe.RechargeMoneyResult;
import com.huafagroup.core.enums.JuHeErrorCodeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.util.MD5Util;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * 聚合数据，手机充值
 */
@Service
public class JHDataRechargeService {
    Logger logger = LoggerFactory.getLogger(JHDataRechargeService.class);
    private static final String RECHARGE_MONEY_ORDER_NO = "RECHARGE_MONEY_ORDER_NO:";
    @Value("${base.recharge.url}")
    private String rechargeUrl;
    private String telCheck = "/ofpay/mobile/telcheck";
    private String telQuery = "/ofpay/mobile/telquery";
    private String onlineOrder = "/ofpay/mobile/onlineorder";
    private String orderSta = "/ofpay/mobile/ordersta";

    private String flowList = "/flow/list";
    private String flowTelCheck = "/flow/telcheck";
    private String flowRecharge = "/flow/recharge";
    private String flowOrderSta = "/flow/ordersta";
    @Value("${base.recharge.signKey}")
    private String signKey;
    @Value("${base.recharge.openId}")
    private String openId;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 话费接口
     * 检测手机号码及金额是否能充值
     */
    public boolean telCheck(String mobile, String cardNum) {

        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("phoneno", mobile);
        paramMap.put("cardnum", cardNum);
        paramMap.put("key", signKey);

        logger.info("检测手机号码及金额是否能充值[{}]请求入参[{}]", telCheck, JSONObject.toJSONString(paramMap));
        String s = HttpUtil.get(rechargeUrl + telCheck, paramMap);
        logger.info("检测手机号码及金额是否能充值返回参数[{}]", s);
        // 检查接口返回异常信息
        checkError(s);
        return true;
    }

    /**
     * 话费接口
     * 根据手机号和面值查询商品信息
     */
    public RechargeMobileResp telQuery(String mobile, String cardNum) {

        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("phoneno", mobile);
        paramMap.put("cardnum", cardNum);
        paramMap.put("key", signKey);

        logger.info("根据手机号和面值查询商品信息[{}]请求入参[{}]", telQuery, JSONObject.toJSONString(paramMap));
        String s = HttpUtil.get(rechargeUrl + telQuery, paramMap);
        logger.info("根据手机号和面值查询商品信息返回参数[{}]", s);
        // 检查接口返回异常信息
        checkError(s);
        return null; //result2RechargeMobileDto(s);
    }

    /***
     *  话费充值接口
     * @param rechargeReq
     * @return
     */
    public RechargeMobileResp rechargeMoney(RechargeMobileReq rechargeReq) {
        String orderNo = IdUtil.simpleUUID();
        String sign = MD5Util.strToMD5(openId + signKey + rechargeReq.getTelNo() + rechargeReq.getAmount() + orderNo);
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("phoneno", rechargeReq.getTelNo());
        paramMap.put("cardnum", rechargeReq.getAmount());
        paramMap.put("orderid", orderNo);
        paramMap.put("key", signKey);
        paramMap.put("sign", sign);
        logger.info("发起聚合话费充值接口请求:[url:{}, param:{}]", rechargeUrl + onlineOrder, JSONObject.toJSONString(paramMap));
        String s = HttpUtil.get(rechargeUrl + onlineOrder, paramMap);
        logger.info("聚合量充值接口返回参数[{}]", s);
        RechargeMoneyResult moneyResult = genRechargeMoneyResult(s);
        RechargeMobileResp resp = new RechargeMobileResp();
        resp.setReqNo(rechargeReq.getReqNo());
        resp.setResult(moneyResult.getGameState());
        resp.setOrderNo(orderNo);
        redissonClient.getBucket(RECHARGE_MONEY_ORDER_NO + orderNo).set(rechargeReq);
        return resp;
    }

    /**
     * 话费接口
     * 查询订单的最新状态，请确认订单成功提交后，再查询哦，
     * 本接口暂只支持查询近60天订单； [***如果查询过程中，遇到http网络状态异常或错误码返回系统异常10014，或联系客服，不要直接做失败处理，避免造成不必要的损失！！
     */
    public RechargeMobileResp orderSta(String orderId) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderid", orderId);
        paramMap.put("key", signKey);

        logger.info("查询订单的最新状态[{}]请求入参[{}]", orderSta, JSONObject.toJSONString(paramMap));
        String s = HttpUtil.get(orderSta, paramMap);
        logger.info("查询订单的最新状态返回参数[{}]", s);
        JSONObject jsonObject = JSONObject.parseObject(s);
        // 检查接口返回异常信息
        checkError(s);
        return null; //result2RechargeMobileDto(s);

    }
    /**
     * 流量接口
     * 获取支持的流量套餐列表，如果缓存需要定期更新
     */
    public String flowList() {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("key", signKey);
        logger.info("获取支持的流量套餐列表[{}]请求入参[{}]", flowList, JSONObject.toJSONString(paramMap));
        // todo : 缓存
        String s = HttpUtil.get(flowList, paramMap);
        logger.info("获取支持的流量套餐列表返回参数[{}]", s);
        JSONObject jsonObject = JSONObject.parseObject(s);
        // 检查接口返回异常信息
        checkError(s);
        return s;
    }

    /**
     * 流量接口
     * 根据手机号码获取支持的流量套餐
     */
    public String telCheck(String mobile) {

        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("phone", mobile);
        paramMap.put("key", signKey);
        logger.info("根据手机号码获取支持的流量套餐[{}]请求入参[{}]", flowTelCheck, JSONObject.toJSONString(paramMap));
        String s = HttpUtil.get(flowTelCheck, paramMap);
        logger.info("根据手机号码获取支持的流量套餐返回参数[{}]", s);
        // 检查接口返回异常信息
        checkError(s);
        return s;
    }

    /**
     * 流量接口
     * 查询订单的最新状态，请确认订单成功提交后，再查询哦～
     */
    public String flowOrderSta(String orderId) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderid", orderId);
        paramMap.put("key", signKey);
        logger.info("查询订单的最新状态接口[{}]请求入参[{}]", flowRecharge, JSONObject.toJSONString(paramMap));
        String s = HttpUtil.get(flowOrderSta, paramMap);
        logger.info("查询订单的最新状态接口返回参数[{}]", s);
        // 检查接口返回异常信息
        checkError(s);
        return s;
    }

    private void checkError(String s) {
        JSONObject jsonObject = JSONObject.parseObject(s);
        Integer errorCode = jsonObject.getInteger("error_code");
        if (errorCode != 0) {
            throw new BusinessException(errorCode.toString(), JuHeErrorCodeEnum.getMsg(errorCode));
        }
    }

    /***
     *  解析话费充值结果
     * @param s
     * @return
     */
    private RechargeMoneyResult genRechargeMoneyResult(String s) {
        JSONObject jsonObject = JSONObject.parseObject(s);
        Integer errorCode = jsonObject.getInteger("error_code");
        if (errorCode != 0) {
            throw new BusinessException(errorCode.toString(), JuHeErrorCodeEnum.getMsg(errorCode));
        }
        JSONObject detail = JSONObject.parseObject(jsonObject.getString("result"));
        RechargeMoneyResult result = new RechargeMoneyResult();
        result.setCardId(detail.getString("cardid"));
        result.setCardNum(detail.getString("cardnum"));
        result.setCardName(detail.getString("cardname"));
        result.setGameState(detail.getString("game_state"));
        result.setGameUserId(detail.getString("game_userid"));
        result.setOrderCash(detail.getFloat("ordercash"));
        result.setSpOrderId(detail.getString("sporder_id"));
        result.setUOrderId(detail.getString("uorderid"));
        return result;
    }

    public void onMoneyCallBack(RechargeMoneyCallBackReq callBackReq) {
        //验签
        String localSign = MD5Util.strToMD5(signKey + callBackReq.getSporder_id() + callBackReq.getOrderid()).toLowerCase();
        if (!callBackReq.getSign().equalsIgnoreCase(localSign)){
            throw new BusinessException(ExceptionEnum.PARAMS_NOT_VALID.getCode(),"话费充值回调验签失败");
        }
        if(callBackReq.getSta() == 1|| callBackReq.getSta() == 9){
            RechargeMobileReq req = (RechargeMobileReq)redissonClient.getBucket(RECHARGE_MONEY_ORDER_NO + callBackReq.getOrderid()).get();
            String notifyUrl = req.getCallBackUrl() + "?reqNo=" + req.getReqNo() + "&result=" + callBackReq.getSta() + "&state=" + req.getState();
            logger.info("话费充值回调:{}", notifyUrl);
            try {
                HttpUtil.get(notifyUrl);
                redissonClient.getBucket(RECHARGE_MONEY_ORDER_NO + callBackReq.getOrderid()).delete();
            }catch (Exception ex){
                logger.info("话费充值回调报错:{}", ex);
            }
        }
    }
}
