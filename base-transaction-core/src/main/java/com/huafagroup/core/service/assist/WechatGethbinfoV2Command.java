package com.huafagroup.core.service.assist;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.dto.TransferResultVO;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.dto.wx.v2.GethbinfoModel;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public class WechatGethbinfoV2Command implements HttpBaseCommand<TransferResultVO>{

    Logger logger = LoggerFactory.getLogger(this.getClass());

    private HbOrder hbOrder;
    private MerchantProperties merchantProps;

    public WechatGethbinfoV2Command(HbOrder hbOrder, MerchantProperties merchantProps) {
        this.hbOrder = hbOrder;
        this.merchantProps = merchantProps;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return ApiTypeEnum.PROMOTION_GETHBINFO_V2.toString();
    }

    @Override
    public String prepareParams() {
        GethbinfoModel model = new GethbinfoModel();
        model.setMchBillno(hbOrder.getOrderNo());
        model.setNonceStr(hbOrder.getNonceStr());
        model.setAppid(hbOrder.getAppId());
        model.setMchId(merchantProps.getMchId());
        return model.toXml(merchantProps.getApiKey());
    }

    @Override
    public TransferResultVO processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        String return_code = resultJson.getStr("return_code");
        //如果请求成功
        if (OrderStatusEnum.SUCCESS.name().equals(return_code)) {
            String result_code = resultJson.getStr("result_code");
            if (OrderStatusEnum.SUCCESS.name().equals(result_code)) {
                //业务结果成功
                String status = resultJson.getStr("status");
                String successTime = "";
                if (OrderStatusEnum.RECEIVED.name().equals(status)) {
                    //已领取
                    JSONObject hbinfo = resultJson.getJSONObject("hblist").getJSONObject("hbinfo");
                    successTime = hbinfo.getStr("rcv_time");
                    hbOrder.setSuccessTime(DateUtil.parseDateTime(successTime));
                } else if (OrderStatusEnum.REFUND.name().equals(status)) {
                    //已退款
                    successTime = resultJson.getStr("refund_time");
                    hbOrder.setSuccessTime(DateUtil.parseDateTime(successTime));
                }
                hbOrder.setStatus(status);
                TransferResultVO resultVO = new TransferResultVO(hbOrder.getOrderNo(),
                        status,successTime);
                return resultVO;
            } else {
                //业务结果失败
                logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
                throw new BusinessException(resultJson.getStr("err_code"), resultJson.getStr("err_code_des"));
            }
        } else {
            //如果请求失败
            logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("return_code"), resultJson.getStr("return_msg"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return merchantProps;
    }
}
