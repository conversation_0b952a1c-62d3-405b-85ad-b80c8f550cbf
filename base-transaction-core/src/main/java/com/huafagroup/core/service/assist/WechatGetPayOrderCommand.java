package com.huafagroup.core.service.assist;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.service.trans.WechatPayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * 微信付款执行命令
 */
public class WechatGetPayOrderCommand implements HttpBaseCommand<Boolean>{

    Logger logger = LoggerFactory.getLogger(this.getClass());


    private Order order;
    private MerchantProperties payProperties;

    public WechatGetPayOrderCommand(Order order, MerchantProperties payProperties) {
        this.order = order;
        this.payProperties = payProperties;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return StrUtil.format(ApiTypeEnum.ORDER_QUERY_BY_ID.toString(),
                order.getOrderNo(),order.getMchId());
    }

    @Override
    public String prepareParams() {
        JSONObject reqJson = new JSONObject();
        reqJson.put("mchid", payProperties.getMchId());
        reqJson.put("out-trade-no", order.getOrderNo());
        return reqJson.toString();
    }

    @Override
    public Boolean processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        if (statusCode == HttpStatus.OK) {
            WechatPayService.processOrderResult(order,resultJson);
            return true;
        } else {
            logger.error("process result failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("code"), resultJson.getStr("message"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return payProperties;
    }
}
