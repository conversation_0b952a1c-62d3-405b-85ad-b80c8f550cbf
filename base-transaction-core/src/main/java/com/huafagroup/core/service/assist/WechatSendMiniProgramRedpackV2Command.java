package com.huafagroup.core.service.assist;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.dto.wx.MiniProHbResultVO;
import com.huafagroup.core.dto.wx.v2.SendRedPackModel;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.util.SerialUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;

public class WechatSendMiniProgramRedpackV2Command implements HttpBaseCommand<MiniProHbResultVO> , ProxyHost{

    Logger logger = LoggerFactory.getLogger(this.getClass());

    private HbOrder hbOrder;
    private MerchantProperties merchantProps;
    private TransactionConfig transactionConfig;

    public WechatSendMiniProgramRedpackV2Command(HbOrder hbOrder, MerchantProperties merchantProps, TransactionConfig transactionConfig) {
        this.hbOrder = hbOrder;
        this.merchantProps = merchantProps;
        this.transactionConfig = transactionConfig;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return ApiTypeEnum.PROMOTION_SENDMINIPROGRAMHB_V2.toString();
    }

    @Override
    public String prepareParams() {
        SendRedPackModel model = new SendRedPackModel();
        model.toMiniRedPack(hbOrder);
        return model.toXml(merchantProps.getApiKey());
    }

    @Override
    public MiniProHbResultVO processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        String return_code = resultJson.getStr("return_code");
        //如果请求成功
        if (OrderStatusEnum.SUCCESS.name().equals(return_code)) {
            String result_code = resultJson.getStr("result_code");
            if (OrderStatusEnum.SUCCESS.name().equals(result_code)) {
                //业务结果成功
                String send_listid = resultJson.getStr("send_listid");
                String packageStr = resultJson.getStr("package");
                Date now = new Date();
                hbOrder.setStatus(OrderStatusEnum.SENT.name());//已发放
                hbOrder.setTransactionId(send_listid);
                //返回支付信息，供客户端拉起
                String appId = hbOrder.getAppId();
                // 加载商户私钥（privateKey：私钥字符串）
                String privateKey = merchantProps.getPrivateKey();
                String timestamp = String.valueOf(DateUtil.currentSeconds());
                String nonceStr = SerialUtil.getNonceStr(32);
                String message = null;
                try {
                    message = URLEncoder.encode(packageStr, "utf-8");//package需要进行urlencode
                } catch (UnsupportedEncodingException e) {
                    logger.error("URLEncoder.encode error",e);
                    throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), "URLEncoder 出错: " + e.getMessage());
                }
                String builder = StrUtil.format("appId={}&nonceStr={}&package={}&timeStamp={}&key={}",
                        appId,nonceStr,message,timestamp,privateKey);

                String paySign = DigestUtil.md5Hex(builder).toUpperCase();
                MiniProHbResultVO resultVO = new MiniProHbResultVO(timestamp,nonceStr,message,paySign);
                resultVO.setOrderNo(hbOrder.getOrderNo());
                return resultVO;
            } else {
                //业务结果失败
                logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
                throw new BusinessException(resultJson.getStr("err_code"), resultJson.getStr("err_code_des"));
            }
        } else {
            //如果请求失败
            logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("return_code"), resultJson.getStr("return_msg"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return merchantProps;
    }

    @Override
    public TransactionConfig getTransactionConfig() {
        return transactionConfig;
    }
}
