package com.huafagroup.core.service.trans;

import cn.hutool.core.date.DateUtil;
import com.huafagroup.core.dto.BizNotifyResult;
import com.huafagroup.core.dto.NotifyResult;
import com.huafagroup.core.entity.trans.Order;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.concurrent.Callable;

/**
 * 通知业务系统服务类
 */
@Service
public class NotifyBizService {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OrderService orderService;

    @Autowired
    @Qualifier("signleTemplate")
    private RestTemplate restTemplate;

    private void notify(Order order,boolean isPayNotify) {
        BizNotifyTask notify = new BizNotifyTask(orderService,order,restTemplate,isPayNotify);
        //改成同步调用
        try {
            Boolean result = notify.call();
            logger.info("通知完成，是否支付通知：{}，平台订单号：{},结果为：{}", isPayNotify, order.getOrderNo(),result);
        } catch (Exception e) {
            logger.error("BizNotify error,",e);
        }
//        executorService.execute(() -> {
//            try {
//                Boolean result = retryer.call(notify);
//                logger.info("通知完成，是否支付通知：{}，平台订单号：{},结果为：{}", isPayNotify, order.getOrderNo(),result);
//            } catch (Exception e) {
//                logger.error("BizNotify error,",e);
//            }
//        });
    }

    public void payNotify(Order order) {
        logger.info("准备给业务系统发送支付通知，平台订单号：{}，业务系统订单号：{}，状态：{}",
                order.getOrderNo(),order.getBizOrderNo(),order.getStatus());
        notify(order,true);
    }

    public void refundNotify(Order order) {
        logger.info("准备给业务系统发送退款通知，平台订单号：{}，业务系统订单号：{}，状态：{}",
                order.getOrderNo(),order.getBizOrderNo(),order.getRefundStatus());
        notify(order,false);
    }

    static class BizNotifyTask implements Callable<Boolean> {
        Logger logger = LoggerFactory.getLogger(this.getClass());

        private OrderService orderService;
        private Order order;
        private RestTemplate restTemplate;
        private int count = 0;
        private boolean isPayNotify = true;

        public BizNotifyTask(OrderService orderService, Order order, RestTemplate restTemplate,boolean isPayNotify) {
            this.orderService = orderService;
            this.order = order;
            this.restTemplate = restTemplate;
            this.isPayNotify = isPayNotify;
        }


        @Override
        public Boolean call() throws Exception {
            logger.info("当前通知平台订单号：{},通知次数：{}", order.getOrderNo(),++count);

            String bizNotifyUrl = order.getBizNotifyUrl();
            String successTime = DateUtil.formatDateTime(order.getSuccessTime());
            Integer total = order.getPayerTotal();
            String status = order.getStatus();
            //如果是退款通知
            if (!isPayNotify) {
                bizNotifyUrl = order.getRefundBizNotifyUrl();
                successTime = DateUtil.formatDateTime(order.getRefundSuccessTime());
                total = order.getRefundPayerRefund();
                status = order.getRefundStatus();
            }

            BizNotifyResult notifyResult = new BizNotifyResult(status,order.getOrderNo(),order.getBizOrderNo(),
                    order.getOpenid(),total,successTime);

            HttpEntity<BizNotifyResult> httpEntity = new HttpEntity<>(notifyResult);
            ResponseEntity<NotifyResult> responseEntity = restTemplate.postForEntity(bizNotifyUrl, httpEntity, NotifyResult.class);
            HttpStatus statusCode = responseEntity.getStatusCode();
            NotifyResult result = responseEntity.getBody();
            if (statusCode == HttpStatus.OK && result.isSuccess()) {
                //通知成功，更新订单状态
                Order temp = new Order();
                temp.setId(order.getId());

                if (isPayNotify) {//如果是支付通知
                    temp.setBizNotifyStatus(result.getCode());
                    temp.setBizNotifyTime(new Date());
                } else {//如果是退款通知
                    temp.setRefundBizNotifyTime(new Date());
                    temp.setRefundBizNotifyStatus(result.getCode());
                }

                orderService.update(temp);
                return true;
            }
            return false;
        }
    }
}
