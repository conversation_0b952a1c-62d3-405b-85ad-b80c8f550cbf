package com.huafagroup.core.service.trans;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.BizNotifyResult;
import com.huafagroup.core.dto.PayOrderResultVO;
import com.huafagroup.core.dto.RefundsResultVO;
import com.huafagroup.core.dto.TransferResultVO;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.enums.NotifyEventType;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.IPayService;
import com.huafagroup.core.service.assist.*;
import com.huafagroup.core.util.DateUtils;
import com.huafagroup.core.util.wx.PKSigner;
import com.huafagroup.core.util.wx.RequestValidator;
import com.huafagroup.core.util.wx.WechatPayClient;
import com.wechat.pay.contrib.apache.httpclient.notification.Notification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.RequestEntity;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class WechatPayService implements IPayService {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OrderService service;

    @Autowired
    private TransactionConfig transactionConfig;

    @Autowired
    private AccountService accountService;

    @Autowired
    private MerchantService merchantService;


    /**
     * 支付下单
     *
     * @param order
     * @param apiTypeEnum
     * @return
     * @throws Exception
     */
    public PayOrderResultVO pay(Order order, ApiTypeEnum apiTypeEnum) throws Exception {
        // 获取商户属性，默认不区分渠道（如需区分由调用方传递channel并调用重载方法）
        MerchantProperties merchantProps = getMerchantProperties(accountService.selectById(order.getAccountId()).getMerchantId());
        order.setMchId(merchantProps.getMchId());
        //构建请求参数
        HttpBaseCommand command;
        switch (apiTypeEnum){
            case H5_API_PAY:
                command = new WechatH5PayCommand(order,merchantProps, transactionConfig);
                return WechatPayClient.doPost(command);
            case PAY_UNIFIEDORDER:
                command = new WechatUnifiedOrderCommand(order, merchantProps, transactionConfig);
                return WechatPayClient.doPostV2(command);
            default:
                command = new WechatPayCommand(order,merchantProps, transactionConfig);
                return WechatPayClient.doPost(command);
        }
    }

    /**
     * 获取发红包的商户号
     * @return
     */
    private MerchantProperties getHbMerchantProperties(Integer accountId) {
        return getMerchantProperties(accountService.selectById(accountId).getPayMerchantId());
    }

    private MerchantProperties getMerchantProperties(Integer merchantId) {
        Merchant merchant = merchantService.selectById(merchantId);
        if (merchant == null) {
            throw new BusinessException(ExceptionEnum.PARAMS_NOT_VALID.getCode(), "数据异常，merchant不能为空");
        }
        //检查微信支付公钥
        if (StrUtil.isBlank(merchant.getWechatPayPublicKeyUrl())) {
            throw new BusinessException(ExceptionEnum.PARAMS_NOT_VALID.getCode(), "请先配置微信支付公钥");
        }
        boolean isUpdate = false;
        if (StrUtil.isBlank(merchant.getPrivateKey())) {
            //如果更新了证书或没有生成privateKey，则根据证书重新生成
            PKSigner.genPrivateKeyInfo(merchant);
            isUpdate = true;
        }
        //如果微信支付公钥为空
        if (StrUtil.isBlank(merchant.getWechatPayPublicKey())) {
            PKSigner.genWechatPayPublicKeyInfo(merchant);
            isUpdate = true;
        }
        if (isUpdate) {
            merchantService.update(merchant);
        }
        return BeanUtil.copyProperties(merchant,MerchantProperties.class);
    }

    /**
     * 退款下单
     *
     * @param order
     * @return
     * @throws Exception
     */
    public RefundsResultVO refund(Order order) throws Exception {
        // 获取商户属性
        MerchantProperties merchantProps = getMerchantProperties(accountService.selectById(order.getAccountId()).getMerchantId());
        //构建请求参数
        WechatRefundCommand payCommand = new WechatRefundCommand(order,merchantProps, transactionConfig);

        return WechatPayClient.doPost(payCommand);
    }

    @Override
    public boolean payNotify(RequestEntity<ObjectNode> requestEntity, Order order) throws Exception{
        //获取商户属性
        MerchantProperties merchantProps = getMerchantProperties(accountService.selectById(order.getAccountId()).getMerchantId());
        //使用微信支付平台证书中的公钥验签,先检查序列号是否跟商户当前所持有的 微信支付平台证书的序列号一致Wechatpay-Serial
        Notification notification = RequestValidator.validate(requestEntity, merchantProps);
        //成功交易
        if (NotifyEventType.TRANSACTION_SUCCESS.getType().equals(notification.getEventType())) {
            processOrderResult(order,JSONUtil.parseObj(notification.getDecryptData()));
            order.setNotifyTime(new Date());
        }
        order.setNotifyId(notification.getId());
        order.setNotifySummary(notification.getSummary());
        return true;
    }

    @Override
    public boolean refundNotify(RequestEntity<ObjectNode> requestEntity, Order order) throws Exception{
        // 获取商户属性
        MerchantProperties merchantProps = getMerchantProperties(accountService.selectById(order.getAccountId()).getMerchantId());
        //使用微信支付平台证书中的公钥验签,先检查序列号是否跟商户当前所持有的 微信支付平台证书的序列号一致Wechatpay-Serial
        Notification notification = RequestValidator.validate(requestEntity, merchantProps);
        //成功退款
        if (NotifyEventType.REFUND_SUCCESS.getType().equals(notification.getEventType())) {
            JSONObject resourceJson = JSONUtil.parseObj(notification.getDecryptData());
            int refund= resourceJson.getJSONObject("amount").getInt("refund");
            String success_time = resourceJson.getStr("success_time");
            String refund_status = resourceJson.getStr("refund_status");
            order.setRefundPayerRefund(refund);
            order.setRefundSuccessTime(DateUtils.fromRfc3339Str(success_time));
            order.setRefundStatus(refund_status);
        }
        order.setRefundNotifyId(notification.getId());
        order.setRefundNotifySummary(notification.getSummary());
        order.setRefundNotifyTime(new Date());
        return true;
    }

    @Override
    public TransferResultVO transfer(HbOrder hbOrder, ApiTypeEnum apiTypeEnum) throws Exception{
        //获取商户属性
        MerchantProperties merchantProps = getHbMerchantProperties(hbOrder.getAccountId());
        //构建请求参数，根据不同的情况，发送不同的激励
        HttpBaseCommand command = null;
        if (apiTypeEnum == ApiTypeEnum.PROMOTION_SENDMINIPROGRAMHB_V2) {
            //小程序红包
            command = new WechatSendMiniProgramRedpackV2Command(hbOrder,merchantProps,transactionConfig);
        } else if (apiTypeEnum == ApiTypeEnum.PROMOTION_SENDREDPACK_V2) {
            //公众号红包
            command = new WechatSendRedpackV2Command(hbOrder,merchantProps,transactionConfig);
        } else {
            command = new WechatTransferV2Command(hbOrder,merchantProps);
        }

        return WechatPayClient.doPostV2(command);
    }

    @Override
    public BizNotifyResult getRefundResult(Order order) throws Exception{
        if (order.getRefundStatus() == null) {
            throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), "该订单非退款中");
        }
        //如果订单未支付状态，则先去支付平台查询订单状态
        if (OrderStatusEnum.PROCESSING.name().equals(order.getRefundStatus())) {
            // 获取商户属性
            MerchantProperties merchantProps = getMerchantProperties(accountService.selectById(order.getAccountId()).getMerchantId());
            //构建请求参数
            WechatGetRefundCommand command = new WechatGetRefundCommand(order,merchantProps);
            WechatPayClient.doGet(command);
            //如果订单状态不是未支付，则说明有状态更新,需要保存到数据库
            if (!OrderStatusEnum.PROCESSING.name().equals(order.getRefundStatus())) {
                service.updateOrder(order);
            }
        }
        return new BizNotifyResult(order.getRefundStatus(),order.getOrderNo(),order.getBizOrderNo(),
                order.getOpenid(),order.getAmount(), DateUtil.formatDateTime(order.getRefundSuccessTime()));
    }


    @Override
    public BizNotifyResult getPayResult(Order order) throws Exception{
        //如果订单未支付状态，则先去支付平台查询订单状态
        if (OrderStatusEnum.NOTPAY.name().equals(order.getStatus())) {
            // 获取商户属性
            MerchantProperties merchantProps = getMerchantProperties(accountService.selectById(order.getAccountId()).getMerchantId());
            //构建请求参数
            WechatGetPayOrderCommand command = new WechatGetPayOrderCommand(order,merchantProps);
            WechatPayClient.doGet(command);
            //如果订单状态不是未支付，则说明有状态更新,需要保存到数据库
            if (!OrderStatusEnum.NOTPAY.name().equals(order.getStatus())) {
                service.updateOrder(order);
            }
        }
        BizNotifyResult result = new BizNotifyResult(order.getStatus(),order.getOrderNo(),order.getBizOrderNo(),
                order.getOpenid(),order.getAmount(), DateUtil.formatDateTime(order.getSuccessTime()));
        return result;
    }

    @Override
    public void close(Order order) throws Exception{
        //关闭前先查一下状态
        BizNotifyResult payResult = getPayResult(order);
        //只有未支付订单才能关闭
        if (!OrderStatusEnum.NOTPAY.name().equals(payResult.getStatus())) {
            throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), "订单状态不允许关闭");
        }
        // 获取商户属性
        MerchantProperties merchantProps = getMerchantProperties(accountService.selectById(order.getAccountId()).getMerchantId());
        //构建请求参数
        HttpBaseCommand command = new WechatCloseCommand(order,merchantProps);

        WechatPayClient.doPost(command);
    }

    @Override
    public TransferResultVO syncHbOrderStatus(HbOrder order) throws Exception {
        // 获取商户属性
        MerchantProperties merchantProps = getMerchantProperties(accountService.selectById(order.getAccountId()).getMerchantId());
        //构建请求参数
        HttpBaseCommand command = new WechatGethbinfoV2Command(order,merchantProps);

        TransferResultVO resultVO = WechatPayClient.doPostV2(command);
        return resultVO;
    }

    /**
     * 获取到订单支付结果时，设置订单相关信息
     * @param order
     * @param resultJson
     */
    public static void processOrderResult(Order order,JSONObject resultJson) {
        //如果原支付状态是未支付，则更新
        if (OrderStatusEnum.NOTPAY.name().equals(order.getStatus())) {
            String trade_state = resultJson.getStr("trade_state");
            order.setStatus(trade_state);
            if (order.isSuccess()) {
                //如果是成功的状态
                int payer_total= resultJson.getJSONObject("amount").getInt("payer_total");
                String success_time = resultJson.getStr("success_time");
                String transaction_id = resultJson.getStr("transaction_id");

                order.setPayerTotal(payer_total);
                order.setSuccessTime(DateUtils.fromRfc3339Str(success_time));
                order.setTransactionId(transaction_id);
            }
        }
    }
    /**
     * 获取到订单退款结果时，设置订单相关信息
     * @param order
     * @param resultJson
     */
    public static void processRefundResult(Order order, JSONObject resultJson) {
        //如果原退款状态是处理中，则更新
        if (OrderStatusEnum.PROCESSING.name().equals(order.getRefundStatus())) {
            String refundId = resultJson.getStr("refund_id");
            String status = resultJson.getStr("status");
            String successTime = resultJson.getStr("success_time");
            String userReceivedAccount = resultJson.getStr("user_received_account");
            String channel = resultJson.getStr("channel");
            JSONObject amount = resultJson.getJSONObject("amount");
            int refund = amount.getInt("refund");
            //更新订单信息
            order.setRefundId(refundId);
            order.setRefundStatus(status);
            if (successTime != null) {
                //说明是成功退款的
                order.setSuccessTime(DateUtils.fromRfc3339Str(successTime));
            }

            order.setRefundUserReceivedAccount(userReceivedAccount);
            order.setRefundChannel(channel);
            order.setRefundPayerRefund(refund);
        }

    }
}
