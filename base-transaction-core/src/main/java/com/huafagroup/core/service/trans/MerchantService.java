package com.huafagroup.core.service.trans;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huafagroup.core.dao.trans.MerchantMapper;
import com.huafagroup.core.dto.CreateOrUpdateMerchantDTO;
import com.huafagroup.core.dto.QueryMerchantDTO;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.enums.ChannelEnum;
import com.huafagroup.core.service.system.BaseServiceImpl;
import com.huafagroup.core.util.PageBean;
import com.huafagroup.core.util.wx.PKSigner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-16 16:49:29
 */

@Service
public class MerchantService extends BaseServiceImpl<Merchant> {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private MerchantMapper mapper;
    @Autowired
    private AccountService accountService;

    public Merchant getByProjectId(String projectId, ChannelEnum channel) {
        return mapper.selectByPrimaryKey(accountService.getForPay(projectId,channel).getMerchantId());
    }

    public Merchant getByMchId(String mchId) {
        Merchant exp = new Merchant();
        exp.setMchId(mchId);
        Merchant merchant = mapper.selectOne(exp);
        Assert.notNull(merchant, "商户不存在");
        return merchant;
    }

    public PageBean<Merchant> selectAll(QueryMerchantDTO dto) {
        Page<Merchant> page = PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        List<Merchant> list = mapper.getList(dto);
        list.forEach(merchant -> {
            merchant.setMchSerialNo(null);
            merchant.setPrivateKey(null);
        });
        return new PageBean<>(page.getPageNum(), page.getPageSize(), page.getTotal(), list);
    }

    public int deleteLogicById(Integer id) {
        Merchant m = new Merchant();
        m.setId(id);
        m.setDelFlag(1);
        return update(m);
    }

    public int create(CreateOrUpdateMerchantDTO item) {
        Merchant merchant = item.toEntity();
        merchant.setCreateBy(item.getOperator());
        merchant.setUpdateBy(item.getOperator());
        merchant.setCreateDate(new Date());
        merchant.setUpdateDate(merchant.getCreateDate());
        if (StrUtil.isNotBlank(merchant.getCertificateUrl())) {
            PKSigner.genPrivateKeyInfo(merchant);
        }
        if (StrUtil.isNotBlank(merchant.getWechatPayPublicKeyUrl())) {
            PKSigner.genWechatPayPublicKeyInfo(merchant);
        }
        mapper.insert(merchant);
        return merchant.getId();
    }

    public Boolean update(CreateOrUpdateMerchantDTO item) {
        Assert.notNull(item.getId(), "id不能为空");
        Merchant merchant = selectById(item.getId());
        Assert.notNull(merchant, "商户不存在");
        CharSequence certificateUrl = merchant.getCertificateUrl();
        CharSequence wechatPayPublicKeyUrl = merchant.getWechatPayPublicKeyUrl();
        BeanUtil.copyProperties(item, merchant);
        merchant.setUpdateBy(item.getOperator());
        merchant.setUpdateDate(new Date());
        //如果证书有更新，清空相关字段
        if (StrUtil.isNotBlank(merchant.getCertificateUrl()) && !Objects.equals(certificateUrl, item.getCertificateUrl())) {
            PKSigner.genPrivateKeyInfo(merchant);
        }
        if (StrUtil.isNotBlank(merchant.getWechatPayPublicKeyUrl()) && !Objects.equals(wechatPayPublicKeyUrl, item.getWechatPayPublicKeyUrl())) {
            PKSigner.genWechatPayPublicKeyInfo(merchant);
        }
        return mapper.updateByPrimaryKey(merchant) > 0;
    }
}

