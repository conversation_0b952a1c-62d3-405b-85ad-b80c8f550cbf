package com.huafagroup.core.service.assist;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.dto.TransferResultVO;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.dto.wx.v2.TransferModel;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public class WechatTransferV2Command implements HttpBaseCommand<TransferResultVO> {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    private HbOrder hbOrder;
    private MerchantProperties merchantProps;

    public WechatTransferV2Command(HbOrder hbOrder, MerchantProperties merchantProps) {
        this.hbOrder = hbOrder;
        this.merchantProps = merchantProps;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return ApiTypeEnum.PROMOTION_TRANSFERS_V2.toString();
    }

    @Override
    public String prepareParams() {
        TransferModel model = new TransferModel();
        model.setNonceStr(hbOrder.getNonceStr());
        model.setDesc(hbOrder.getDesc());
        model.setAmount(hbOrder.getAmount().toString());
        model.setMchid(hbOrder.getMchId());
        model.setMchAppid(hbOrder.getAppId());
        model.setPartnerTradeNo(hbOrder.getOrderNo());
        model.setCheckName(hbOrder.getCheckName());
        model.setReUserName(hbOrder.getReUserName());
        model.setOpenid(hbOrder.getOpenid());
        return model.toXml(merchantProps.getApiKey());
    }

    @Override
    public TransferResultVO processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();

        String return_code = resultJson.getStr("return_code");
        //如果请求成功
        if (OrderStatusEnum.SUCCESS.name().equals(return_code)) {
            String result_code = resultJson.getStr("result_code");
            if (OrderStatusEnum.SUCCESS.name().equals(result_code)) {
                //业务结果成功
                String payment_no = resultJson.getStr("payment_no");
                String payment_time = resultJson.getStr("payment_time");
                hbOrder.setStatus(result_code);
                hbOrder.setTransactionId(payment_no);
                hbOrder.setSuccessTime(DateUtil.parseDateTime(payment_time));
                TransferResultVO resultVO = new TransferResultVO(hbOrder.getOrderNo(),
                        result_code,payment_time);
                return resultVO;
            } else {
                //业务结果失败
                logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
                throw new BusinessException(resultJson.getStr("err_code"), resultJson.getStr("err_code_des"));
            }
        } else {
            //如果请求失败
            logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("return_code"), resultJson.getStr("return_msg"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return merchantProps;
    }
}
