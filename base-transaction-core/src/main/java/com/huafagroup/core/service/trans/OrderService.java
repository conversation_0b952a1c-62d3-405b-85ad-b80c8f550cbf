package com.huafagroup.core.service.trans;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.huafagroup.core.dao.trans.OrderMapper;
import com.huafagroup.core.dto.PayParamsVO;
import com.huafagroup.core.dto.RefundParamsVO;
import com.huafagroup.core.entity.IntegerBaseEntity;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.system.BaseServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
* <p>
    * </p>
*
* <AUTHOR>
* @date 2021-04-12 19:32:58
* @version
*/

@Service
public class OrderService extends BaseServiceImpl<Order>{

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OrderMapper mapper;

//    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Order createOrder(PayParamsVO orderParamsVO) {
        //检查业务订单是否已存在?

        Order order = new Order();
        order.setOrderNo(IdUtil.simpleUUID());
        order.setStatus(OrderStatusEnum.NOTPAY.name());
        order.setBizNotifyStatus(OrderStatusEnum.NOTNOTIFY.name());
        //如果Openid为空，则说明是h5支付，使用客户端ip
        if (StrUtil.isBlank(orderParamsVO.getOpenid())) {
            orderParamsVO.setOpenid(orderParamsVO.getClientIp());
        }
        //复制同请请求属性
        BeanUtil.copyProperties(orderParamsVO,order);
        order.setDesc(orderParamsVO.getDescription());
        order.setPayProvider(orderParamsVO.getProviderEnum());
        //设置过期时间
        Date now = new Date();
        order.setExpireTime(DateUtil.offsetMinute(now,orderParamsVO.getExpireMinutes()));
        if (order.getCreateDate() == null) {
            order.setCreateDate(now);
        }
        order.setUpdateDate(now);

        //保存
        this.insert(order);
        return order;
    }

    public Order getRefundOrder(RefundParamsVO paramsVO) {
        String orderNo = paramsVO.getOrderNo();
        Order order = selectByNo(orderNo);

        //支付成功且未申请退款的订单才可以申请退款
        if (OrderStatusEnum.SUCCESS.toString().equals(order.getStatus())
                && StrUtil.isEmpty(order.getRefundStatus())){
            return order;
        }
        throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), "订单状态不允许退款");
    }

    public Order selectByNo(String orderNo){
        Example example = new Example(Order.class);
        example.createCriteria().andEqualTo("orderNo", orderNo);
        Order order = mapper.selectOneByExample(example);

        if (order == null) {
            throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), "无法找到订单");
        }
        return order;
    }

    public void updateOrder(Order order) {
        order.setUpdateDate(new Date());
        this.update(order);
    }

    /**
     * 查询需要同步状态的订单
     * @param expireMinutes 超过的分钟数
     * @param isForPay 是否查询支付订单
     * @return
     */
    public List<Order> selectForSysnStatus(int expireMinutes, boolean isForPay){
        Example example = new Example(Order.class);
        DateTime dateTime = DateUtil.offsetMinute(new Date(), -expireMinutes);
        //查找创建时间是在30分钟前的订单
        Example.Criteria criteria = example.createCriteria().andLessThanOrEqualTo(IntegerBaseEntity.FIELD_UPDATE_DATE, dateTime);
        if (isForPay) {
            //查询超时未支付的支付订单
            criteria.andEqualTo("status",OrderStatusEnum.NOTPAY.name());
        } else {
            //查询超时未完成的退款订单
            criteria.andEqualTo("refundStatus", OrderStatusEnum.PROCESSING.name());
        }

        List<Order> orders = mapper.selectByExample(example);
        return orders;
    }

    /**
     * 查询需要同步通知状态的订单
     * @param expireDays 超过天数
     * @param isForPay 是否查询支付订单
     * @return
     */
    public List<Order> selectForSysnNotify(int expireDays, boolean isForPay) {
        Example example = new Example(Order.class);
        DateTime dateTime = DateUtil.offsetDay(new Date(), -expireDays);
        //查找指定天数内的订单
        Example.Criteria criteria = example.createCriteria().andGreaterThan(IntegerBaseEntity.FIELD_UPDATE_DATE, dateTime);
        List<String> finishedStatus = Arrays.asList(OrderStatusEnum.SUCCESS.name(),OrderStatusEnum.CLOSED.name(),
                OrderStatusEnum.PAYERROR.name(),OrderStatusEnum.ABNORMAL.name());
        if (isForPay) {
            //查询已完成的支付订单，且未完成通知的
            criteria.andIn("status", finishedStatus)
                    .andEqualTo("bizNotifyStatus",OrderStatusEnum.NOTNOTIFY.name());
        } else {
            //查询已完成的退款订单
            criteria.andIn("refundStatus", finishedStatus)
                    .andEqualTo("refundBizNotifyStatus",OrderStatusEnum.NOTNOTIFY.name());;
        }

        List<Order> orders = mapper.selectByExample(example);
        return orders;
    }
}