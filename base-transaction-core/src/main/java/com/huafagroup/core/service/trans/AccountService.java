package com.huafagroup.core.service.trans;


import cn.hutool.core.lang.Assert;
import com.huafagroup.core.dao.trans.AccountMapper;
import com.huafagroup.core.dto.BindDTO;
import com.huafagroup.core.entity.trans.Account;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.entity.trans.Project;
import com.huafagroup.core.enums.ChannelEnum;
import com.huafagroup.core.service.system.BaseServiceImpl;
import com.huafagroup.core.util.Constants;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-16 16:42:15
 */

@Service
public class AccountService extends BaseServiceImpl<Account> {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private AccountMapper mapper;

    @Autowired
    RedissonClient redissonClient;

    @Resource
    private MerchantService merchantService;
    @Resource
    private ProjectService projectService;


    public Account getByProjectId(String projectId) {
        Example example = new Example(Account.class);
        example.createCriteria().andEqualTo("projectId", projectId).andIsNull("channel").andEqualTo("delFlag", 0);
        Account account = mapper.selectOneByExample(example);
        Assert.notNull(account, "根据传入的项目ID={}无法找到账号",projectId);
        return account;
    }

    public Account getByProjectIdAndChannel(String projectId, ChannelEnum channel) {
        Example example = new Example(Account.class);
        example.createCriteria().andEqualTo("projectId", projectId).andEqualTo("channel", channel).andEqualTo("delFlag", 0);
        Account account = mapper.selectOneByExample(example);
        Assert.notNull(account, "根据传入的项目ID={}和渠道={}无法找到账号",projectId,channel);
        return account;
    }
    public Account getForPay(String projectId, ChannelEnum channel) {
        try {
            return getByProjectIdAndChannel(projectId, channel);
        } catch (Exception e) {
            return getByProjectId(projectId);
        }
    }

    public void decAndUpdateBalance(Account account, int amount) {
        int i = mapper.updateBalance(account.getId(),-amount);
        logger.info("扣减账号ID为{}的余额{}{}",account.getId(),amount,i == 1 ? "成功":"失败");
    }


    public int deposit(String projectId, ChannelEnum channel, int amount){
        Account account = getForPay(projectId,channel);
        return deposit(account,amount);
    }

    public int deposit(Account account, int amount){
        int i = mapper.updateBalance(account.getId(),amount);;
        logger.info("给项目{}充值{}，结果：{}",account.getProjectId(),amount,i == 1 ? "成功":"失败");
        if (i == 1) {
            //充值成功，需要给Redis增加相应金额
            RAtomicLong atomicLong = redissonClient.getAtomicLong(Constants.ACCOUNT_BALANCE_KEY_PREFIX + account.getId());
            long afterAdd = atomicLong.addAndGet(amount);
            logger.info("Redis中给项目{}充值后余额为{}，",account.getProjectId(),afterAdd);
        }
        return i;
    }

    /**
     * 账户同步（把账户余额同步到Redis中）
     *
     * @param projectId
     * @param channel
     * @return
     */
    public long sync(String projectId, ChannelEnum channel) {
        Account account = getForPay(projectId,channel);
        RAtomicLong atomicLong = redissonClient.getAtomicLong(Constants.ACCOUNT_BALANCE_KEY_PREFIX + account.getId());
        atomicLong.set(account.getBalance());
        long afterBalance = atomicLong.get();
        logger.info("同步项目{}的账户的余额同步到Redis中,同步后Redis余额为{}.",projectId,afterBalance);
        return afterBalance;
    }

    public Boolean unbind(Integer id) {
        return mapper.deleteByPrimaryKey(id) == 1;
    }

    public Integer bind(BindDTO dto) {
        Project project = projectService.getByProjectId(dto.getProjectId());
        Assert.isTrue(project != null, "项目不存在");
        Merchant merchant = merchantService.getByMchId(dto.getMchId());
        Account account = null;
        try {
            account = getByProjectIdAndChannel(project.getId(), dto.getChannel());
        } catch (Exception e) {
            account = new Account();
            account.setProjectId(project.getId());
            account.setName(project.getName());
            account.setCreateBy(dto.getOperator());
            account.setCreateDate(new Date());
            account.setBalance(0L);
            account.setChannel(dto.getChannel());
        }
        //只能是新增，不能修改
        Assert.isTrue(account.getId() == null, "此项目已绑定过商户号，不能重复绑定");
        if (dto.getBindType() == 1) {
            account.setMerchantId(merchant.getId());
        } else {
            Assert.isTrue(merchant.getPayFlag() == 1, "商户未开启支付功能");
            account.setPayMerchantId(merchant.getId());
        }
        account.setUpdateBy(dto.getOperator());
        account.setUpdateDate(new Date());
        //保存
        mapper.insert(account);
        return account.getId();
    }
}
