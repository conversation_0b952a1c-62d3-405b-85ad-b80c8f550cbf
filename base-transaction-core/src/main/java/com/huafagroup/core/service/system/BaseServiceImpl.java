package com.huafagroup.core.service.system;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <p>
 * </p>
 *
 * 通用SQL泛型接口
 * <AUTHOR>
 * @date 2019-02-21 15:12:07
 * @version
 */
public class BaseServiceImpl<T> {
    @Autowired
    public Mapper<T> mapper;

    public T selectById(String id){
        return mapper.selectByPrimaryKey(id);
    }

    public T selectById(Integer id){
        return mapper.selectByPrimaryKey(id);
    }

    public List<T> selectAll(){
        return mapper.selectAll();
    }

    @Transactional(rollbackFor = Exception.class)
    public int deleteById(String id){
        return mapper.deleteByPrimaryKey(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public int delete(T entity){
        return mapper.delete(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public int insert(T entity){
        return mapper.insertSelective(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    public int update(T entity){
        return mapper.updateByPrimaryKeySelective(entity);
    }



}
