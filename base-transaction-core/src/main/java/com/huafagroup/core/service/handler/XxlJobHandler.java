package com.huafagroup.core.service.handler;

import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.service.trans.HbOrderService;
import com.huafagroup.core.service.trans.NotifyBizService;
import com.huafagroup.core.service.trans.OrderService;
import com.huafagroup.core.service.trans.TransService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class XxlJobHandler {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OrderService orderService;

    @Autowired
    private HbOrderService hbOrderService;

    @Autowired
    private TransService service;

    @Autowired
    private NotifyBizService notifyBizService;

    /**
     * 同步订单状态
     * @param param
     * @return
     */
    @XxlJob("syncOrderStatus")
    public ReturnT<String> syncOrderStatus (String param) {
        int expireMinutes = 30;
        try {
            expireMinutes = Integer.parseInt(param);
        } catch (Exception e) {
        }
        logger.info("syncOrderStatus job start run,and the expireMinutes is {}", expireMinutes);
        //1.同步支付状态为【未支付】，且时间超过30分钟的订单
        List<Order> orders = orderService.selectForSysnStatus(expireMinutes, true);
        logger.info("get pay order list for sync size is {}", orders.size());
        orders.forEach(order -> {
            try {
                service.getPayResult(order.getOrderNo(),true);
            } catch (Exception e) {
                logger.error("sync pay order status error , ",e);
            }
        });

        //2.同步退款状态为【未处理中或未支付】，且时间超过30分钟的订单
        orders = orderService.selectForSysnStatus(expireMinutes, false);
        logger.info("get refund order list for sync size is {}", orders.size());
        orders.forEach(order -> {
            try {
                service.getRefundResult(order.getOrderNo(), false);
            } catch (Exception e) {
                logger.error("sync refund order status error , ",e);
            }
        });

        return ReturnT.SUCCESS;
    }

    /**
     * 更新未完成通知业务系统的订单，默认只查询3天内订单
     * @param param
     * @return
     */
    @XxlJob("syncNotifyStatus")
    public ReturnT<String> syncNotifyStatus (String param) {
        int expireDays = 3;
        try {
            expireDays = Integer.parseInt(param);
        } catch (Exception e) {
        }
        logger.info("syncNotifyStatus job start run,and the expireDays is {}", expireDays);
        //1.同步支付状态为【未支付】，且时间超过30分钟的订单
        List<Order> orders = orderService.selectForSysnNotify(expireDays, true);
        logger.info("get pending notify pay order list size is {}", orders.size());
        orders.forEach(order -> {
            try {
                notifyBizService.payNotify(order);
            } catch (Exception e) {
                logger.error("sync pay order status error , ",e);
            }
        });

        //2.同步退款状态为【未处理中或未支付】，且时间超过30分钟的订单
        orders = orderService.selectForSysnNotify(expireDays, false);
        logger.info("get pending notify refund order list size is {}", orders.size());
        orders.forEach(order -> {
            try {
                notifyBizService.refundNotify(order);
            } catch (Exception e) {
                logger.error("sync refund order status error , ",e);
            }
        });

        return ReturnT.SUCCESS;
    }

    /**
     * 同步3天内未领取或退款中的红包状态
     * @param param
     * @return
     */
    @XxlJob("syncHbOrderStatus")
    public ReturnT<String> syncHbOrderStatus(String param) {
        int expireDays = 3;
        try {
            expireDays = Integer.parseInt(param);
        } catch (Exception e) {
        }
        logger.info("syncHbOrderStatus job start run,and the expireDays is {}", expireDays);
        //1.同步状态为【已发送或退款中】，3天内的订单
        List<HbOrder> orders = hbOrderService.selectForSyncStatus(expireDays);
        logger.info("get syncHbOrderStatus list size is {}", orders.size());
        orders.forEach(order -> {
            try {
                service.syncHbOrderStatus(order);
            } catch (Exception e) {
                logger.error("syncHbOrderStatus error , ",e);
            }
        });

        return ReturnT.SUCCESS;
    }


}
