package com.huafagroup.core.service.assist;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.PayOrderResultVO;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.dto.wx.WxPayOrderResultVO;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.util.DateUtils;
import com.huafagroup.core.util.SerialUtil;
import com.huafagroup.core.util.wx.PKSigner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * 微信付款执行命令
 */
public class WechatPayCommand implements HttpBaseCommand<PayOrderResultVO>{

    Logger logger = LoggerFactory.getLogger(this.getClass());

    private Order order;
    private MerchantProperties payProperties;

    private TransactionConfig transactionConfig;

    public WechatPayCommand(Order order, MerchantProperties payProperties, TransactionConfig transactionConfig) {
        this.order = order;
        this.payProperties = payProperties;
        this.transactionConfig = transactionConfig;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return ApiTypeEnum.JS_API_PAY.toString();
    }

    @Override
    public String prepareParams() {

        int total = order.getAmount();//订单总金额，单位为分

        JSONObject amount = new JSONObject();
        amount.put("total", total);
        amount.put("currency", "CNY");

        JSONObject payer = new JSONObject();
        payer.put("openid", order.getOpenid());

        JSONObject reqJson = new JSONObject();
        reqJson.put("amount", amount);
        reqJson.put("payer", payer);
        reqJson.put("mchid", payProperties.getMchId());
        reqJson.put("appid", order.getAppId());
        reqJson.put("description", order.getDesc());
        reqJson.put("notify_url", StrUtil.format(transactionConfig.getPayNotifyUrl(),order.getOrderNo()));//todo notify_url
        reqJson.put("out_trade_no", order.getOrderNo());
        reqJson.put("time_expire", DateUtils.toRfc3339Str(order.getExpireTime()));
        return reqJson.toString();
    }

    @Override
    public PayOrderResultVO processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        if (statusCode == HttpStatus.OK) {
            String prepayId = resultJson.getStr("prepay_id");
            //更新订单信息
            order.setPrepayId(prepayId);
            // 加载商户私钥（privateKey：私钥字符串）
            String privateKey = payProperties.getPrivateKey();

            String appId = order.getAppId();
            String timestamp = String.valueOf(DateUtil.currentSeconds());
            String nonceStr = SerialUtil.getNonceStr(32);
            String message = StrUtil.format("prepay_id={}",prepayId);

            String builder = appId + "\n" + timestamp + "\n"
                    + nonceStr + "\n" + message + "\n";

            String paySign = PKSigner.sign(builder, privateKey);

            //返回支付信息，供客户端拉起支付
            WxPayOrderResultVO resultVO = new WxPayOrderResultVO(appId,timestamp,nonceStr,message,paySign);
            resultVO.setOrderNo(order.getOrderNo());
            return resultVO;
        } else {
            logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("code"), resultJson.getStr("message"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return payProperties;
    }
}
