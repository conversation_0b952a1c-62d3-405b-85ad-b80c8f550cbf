package com.huafagroup.core.service.assist;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.util.RestResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

/**
 * 微信付款执行命令
 */
public class WechatCloseCommand implements HttpBaseCommand<RestResponse>{

    Logger logger = LoggerFactory.getLogger(this.getClass());


    private Order order;
    private MerchantProperties payProperties;

    public WechatCloseCommand(Order order, MerchantProperties payProperties) {
        this.order = order;
        this.payProperties = payProperties;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return StrUtil.format(ApiTypeEnum.CLOSE_ORDER.toString(),order.getOrderNo());
    }

    @Override
    public String prepareParams() {
        JSONObject reqJson = new JSONObject();
        reqJson.put("mchid", order.getMchId());
        return reqJson.toString();
    }

    @Override
    public RestResponse processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        if (statusCode == HttpStatus.NO_CONTENT) {
            //成功关闭
            order.setStatus(OrderStatusEnum.CLOSED.name());
            return RestResponse.success();
        } else {
            logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("code"), resultJson.getStr("message"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return payProperties;
    }
}
