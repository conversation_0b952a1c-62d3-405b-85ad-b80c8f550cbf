package com.huafagroup.core.service.trans;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.huafagroup.core.dao.trans.HbOrderMapper;
import com.huafagroup.core.dto.TransferParamsVO;
import com.huafagroup.core.entity.IntegerBaseEntity;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.system.BaseServiceImpl;
import com.huafagroup.core.util.SerialUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
* <p>
    * </p>
*
* <AUTHOR>
* @date 2021-04-22 16:33:50
* @version
*/

@Service
public class HbOrderService extends BaseServiceImpl<HbOrder>{

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private HbOrderMapper mapper;

    /**
     * 创建订单，但是未做保存，需要在调用方保存
     * @param paramsVO
     * @return
     */
    public HbOrder createOrder(TransferParamsVO paramsVO) {
        HbOrder hbOrder = new HbOrder();
        hbOrder.setOrderNo(SerialUtil.getNonceStr(28));
        hbOrder.setBizOrderNo(paramsVO.getBizOrderNo());
        hbOrder.setStatus(OrderStatusEnum.NOTPAY.name());
        hbOrder.setAmount(paramsVO.getAmount());
        hbOrder.setAppId(paramsVO.getAppId());
        hbOrder.setCheckName("NO_CHECK");
        hbOrder.setDesc(paramsVO.getDescription());
        hbOrder.setNonceStr(SerialUtil.getNonceStr(32));
        hbOrder.setOpenid(paramsVO.getOpenid());
        hbOrder.setProjectId(paramsVO.getProjectId());
        //todo 发红包参数设置
        hbOrder.setActName(paramsVO.getActName());
        hbOrder.setSceneId("PRODUCT_2");//抽奖
        hbOrder.setSendName(paramsVO.getSendName());
        hbOrder.setWishing(paramsVO.getWishing());
        hbOrder.setCreateDate(new Date());
        hbOrder.setUpdateDate(hbOrder.getCreateDate());
        hbOrder.setCreateBy("");
        hbOrder.setUpdateBy("");
        insert(hbOrder);
        return hbOrder;
    }

    public List<HbOrder> selectForSyncStatus(int expireDays) {
        Example example = new Example(HbOrder.class);
        DateTime dateTime = DateUtil.offsetDay(new Date(), -expireDays);
        //查找指定天数内的订单,退款中或未领取
        Example.Criteria criteria = example.createCriteria().andGreaterThan(IntegerBaseEntity.FIELD_UPDATE_DATE, dateTime);
        List<String> finishedStatus = Arrays.asList(OrderStatusEnum.SENT.name(),OrderStatusEnum.RFUND_ING.name());
        criteria.andIn("status", finishedStatus);
        List<HbOrder> orders = mapper.selectByExample(example);
        return orders;
    }

    public HbOrder selectByNo(String orderNo){
        Example example = new Example(Order.class);
        example.createCriteria().andEqualTo("orderNo", orderNo);
        HbOrder order = mapper.selectOneByExample(example);

        if (order == null) {
            throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), "无法找到订单");
        }
        return order;
    }
}