package com.huafagroup.core.service.assist;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.PayOrderResultVO;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.dto.wx.WxH5PayOrderResultVO;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.util.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 微信付款执行命令
 */
public class WechatH5PayCommand implements HttpBaseCommand<PayOrderResultVO>{

    Logger logger = LoggerFactory.getLogger(this.getClass());

    private Order order;
    private MerchantProperties payProperties;

    private TransactionConfig transactionConfig;

    public WechatH5PayCommand(Order order, MerchantProperties payProperties, TransactionConfig transactionConfig) {
        this.order = order;
        this.payProperties = payProperties;
        this.transactionConfig = transactionConfig;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return ApiTypeEnum.H5_API_PAY.toString();
    }

    @Override
    public String prepareParams() {

        int total = order.getAmount();//订单总金额，单位为分

        JSONObject amount = new JSONObject();
        amount.put("total", total);
        amount.put("currency", "CNY");

        JSONObject sceneInfo = new JSONObject();
        JSONObject h5Info = new JSONObject();
        sceneInfo.put("payer_client_ip", order.getOpenid());
        sceneInfo.put("h5_info", h5Info);
        h5Info.put("type","Wap");

        JSONObject reqJson = new JSONObject();
        reqJson.put("mchid", payProperties.getMchId());
        reqJson.put("out_trade_no", order.getOrderNo());
        reqJson.put("appid", order.getAppId());
        reqJson.put("description", order.getDesc());
        reqJson.put("notify_url", StrUtil.format(transactionConfig.getPayNotifyUrl(),order.getOrderNo()));//todo notify_url
        reqJson.put("amount", amount);
        reqJson.put("scene_info", sceneInfo);
        reqJson.put("time_expire", DateUtils.toRfc3339Str(order.getExpireTime()));
        return reqJson.toString();
    }

    @Override
    public PayOrderResultVO processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        if (statusCode == HttpStatus.OK) {
            String h5Url = resultJson.getStr("h5_url");
            Pattern r = Pattern.compile("prepay_id=(\\w+)");
            Matcher m = r.matcher(h5Url);
            if (m.find( )) {
                //更新订单信息
                order.setPrepayId(m.group(1));
            }
            //返回支付信息，供客户端拉起支付
            WxH5PayOrderResultVO resultVO = new WxH5PayOrderResultVO(h5Url,order.getOrderNo());
            return resultVO;
        } else {
            logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("code"), resultJson.getStr("message"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return payProperties;
    }
}
