package com.huafagroup.core.service.trans;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huafagroup.core.dao.trans.ProjectMapper;
import com.huafagroup.core.dto.OrgTreeDto;
import com.huafagroup.core.dto.ProjectMerchantVO;
import com.huafagroup.core.dto.QueryProjectDTO;
import com.huafagroup.core.entity.trans.Project;
import com.huafagroup.core.enums.ProjectCreateTypeEnum;
import com.huafagroup.core.enums.ProjectTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.system.BaseServiceImpl;
import com.huafagroup.core.util.PageBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;


/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-16 16:50:39
 */

@Service
public class ProjectService extends BaseServiceImpl<Project> {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ProjectMapper mapper;

    public Project getByProjectId(String projectId) {

        Example example = new Example(Project.class);

        example.createCriteria().andEqualTo("id", projectId).andEqualTo("delFlag", 0);

        List<Project> projects = mapper.selectByExample(example);

        if (projects.size() > 1) {
            throw new BusinessException(ExceptionEnum.PARAMS_NOT_VALID.getCode(), "数据异常，projectId重复");
        }
        return projects.size() == 0 ? null : projects.get(0);
    }

    public List<Project> getArea() {

        Example example = new Example(Project.class);

        example.createCriteria().andEqualTo("type", ProjectTypeEnum.AREA.getCode()).andEqualTo("delFlag", 0);

        return mapper.selectByExample(example);
    }

    public List<Project> getCityOrOrgOrProject(String parentId, Integer type) {

        Example example = new Example(Project.class);

        example.createCriteria().andEqualTo("parentId", parentId)
                .andEqualTo("type", type).andEqualTo("delFlag", 0);

        return mapper.selectByExample(example);
    }


    public void dataSync(String msg, String tag) {
        String idSuffix = "Id";
        String nameSuffix = "Name";

        // {"areaId":"1099004","cityName":"广州","updateTime":"1565942404773","cityId":"1098004"}
        JSONObject json = JSON.parseObject(msg);
        String id = json.getString(tag + idSuffix);
        if (mapper.selectByPrimaryKey(id) != null) {
            return;
        }

        Project project = new Project();

        project.setId(id);
        project.setName(json.getString(tag + nameSuffix));

        Integer type = ProjectTypeEnum.getCodeByTag(tag);
        project.setType(type);
        project.setDelFlag(0);
        project.setCreateType(ProjectCreateTypeEnum.SYNC.getCode());
        if (type > 0) {
            String parentTag = ProjectTypeEnum.getTagByCode(type - 1);
            project.setParentId(json.getString(parentTag + idSuffix));
        }
        project.setCreateBy("market");
        project.setCreateDate(new Date());
        mapper.insert(project);
    }


    public List<OrgTreeDto> getOrgTree(List<String> orgIds) {
        List<OrgTreeDto> list = new ArrayList<>();
        for (String orgId : orgIds) {
            Project org = getByProjectId(orgId);
            if (org == null || org.getType() == null) {
               continue;
            }
            OrgTreeDto dto = new OrgTreeDto();
            BeanUtil.copyProperties(org, dto);
            // 设置子节点
            setOrgChildren(dto);
            // 设置父节点
            list.add(dto);
//            list.add(getOrgParents(dto));
        }
        return getOrgParentsList(list);
    }

    private void setOrgChildren(OrgTreeDto org) {

        String code = ProjectTypeEnum.getTagByCode(org.getType() + 1);
        if (code == null) {
            return;
        }
        List<Project> projects = getCityOrOrgOrProject(org.getId(), org.getType() + 1);
        List<OrgTreeDto> list = new ArrayList<>();
        for (Project project : projects) {
            OrgTreeDto treeDto = new OrgTreeDto();
            BeanUtil.copyProperties(project, treeDto);
            list.add(treeDto);
            setOrgChildren(treeDto);
        }
        org.setChildren(list);
    }

    private OrgTreeDto getOrgParents(OrgTreeDto org) {
        if (org.getType() <= 0) {
            return org;
        }
        Project project = getByProjectId(org.getParentId());
        if (project == null) {
            return org;
        }
        OrgTreeDto dto = new OrgTreeDto();
        BeanUtil.copyProperties(project, dto);
        List<OrgTreeDto> list = new ArrayList<>();
        list.add(org);
        dto.setChildren(list);
        return getOrgParents(dto);
    }

    private List<OrgTreeDto> getOrgParentsList(List<OrgTreeDto> list) {
        Map<String, List<OrgTreeDto>> map = new HashMap<>();
        for (OrgTreeDto dto : list) {
            if (StrUtil.isBlank(dto.getParentId())) {
               continue;
            }
            List<OrgTreeDto> dtos = map.containsKey(dto.getParentId()) ? map.get(dto.getParentId()) : new ArrayList<>();
            dtos.add(dto);
            map.put(dto.getParentId(), dtos);
        }
        if (map.keySet().size() == 0) {
            return list;
        }
        List<OrgTreeDto> parentsList = new ArrayList<>();
        for (String s : map.keySet()) {
            Project parents = getByProjectId(s);
            OrgTreeDto dto = new OrgTreeDto();
            BeanUtil.copyProperties(parents, dto);
            dto.setChildren(map.get(s));
            parentsList.add(dto);
        }
        return getOrgParentsList(parentsList);
    }

    public PageBean<ProjectMerchantVO> selectAll(QueryProjectDTO dto) {
        Page<ProjectMerchantVO> page = PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        List<ProjectMerchantVO> list = mapper.getList(dto);
        return new PageBean<>(page.getPageNum(), page.getPageSize(), page.getTotal(), list);
    }
}
