package com.huafagroup.core.service;

import com.huafagroup.core.enums.PayProviderEnum;
import com.huafagroup.core.service.trans.RuthusPayService;
import com.huafagroup.core.service.trans.WechatPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PayServiceProvider {
    @Autowired
    private WechatPayService wxPayService;

    @Autowired
    private RuthusPayService ruthusPayService;

    public IPayService getPayService(PayProviderEnum payProviderEnum) {
        if (payProviderEnum == PayProviderEnum.RUTHUS_TECH) {
            return ruthusPayService;
        }
        return wxPayService;
    }
}
