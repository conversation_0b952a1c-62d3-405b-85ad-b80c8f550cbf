package com.huafagroup.core.service.assist;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.TransferResultVO;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.dto.wx.v2.SendRedPackModel;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.enums.DomainEnum;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import com.huafagroup.core.exception.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Date;

public class WechatSendRedpackV2Command implements HttpBaseCommand<TransferResultVO> , ProxyHost{

    Logger logger = LoggerFactory.getLogger(this.getClass());

    private HbOrder hbOrder;
    private MerchantProperties merchantProps;
    private TransactionConfig transactionConfig;

    public WechatSendRedpackV2Command(HbOrder hbOrder, MerchantProperties merchantProps,TransactionConfig transactionConfig) {
        this.hbOrder = hbOrder;
        this.merchantProps = merchantProps;
        this.transactionConfig = transactionConfig;
    }

    @Override
    public String getDomain() {
        return DomainEnum.WX_CHINA.toString();
    }

    @Override
    public String getApi() {
        return ApiTypeEnum.PROMOTION_SENDREDPACK_V2.toString();
    }

    @Override
    public String prepareParams() {
        SendRedPackModel model = new SendRedPackModel();
        model.toRedPack(hbOrder,transactionConfig);
        return model.toXml(merchantProps.getApiKey());
    }

    @Override
    public TransferResultVO processResult(ResponseEntity<JSONObject> responseEntity) {
        HttpStatus statusCode = responseEntity.getStatusCode();
        JSONObject resultJson = responseEntity.getBody();
        String return_code = resultJson.getStr("return_code");
        //如果请求成功
        if (OrderStatusEnum.SUCCESS.name().equals(return_code)) {
            String result_code = resultJson.getStr("result_code");
            if (OrderStatusEnum.SUCCESS.name().equals(result_code)) {
                //业务结果成功
                String send_listid = resultJson.getStr("send_listid");
                Date now = new Date();
                hbOrder.setStatus(OrderStatusEnum.SENT.name());//已发放
                hbOrder.setTransactionId(send_listid);
                TransferResultVO resultVO = new TransferResultVO(hbOrder.getOrderNo(),
                        result_code,DateUtil.formatDateTime(now));
                return resultVO;
            } else {
                //业务结果失败
                logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
                throw new BusinessException(resultJson.getStr("err_code"), resultJson.getStr("err_code_des"));
            }
        } else {
            //如果请求失败
            logger.error("failed,resp code = " + statusCode + ",return body = " + resultJson);
            throw new BusinessException(resultJson.getStr("return_code"), resultJson.getStr("return_msg"));
        }
    }

    @Override
    public MerchantProperties getMerchantProperties() {
        return merchantProps;
    }

    @Override
    public TransactionConfig getTransactionConfig() {
        return transactionConfig;
    }
}
