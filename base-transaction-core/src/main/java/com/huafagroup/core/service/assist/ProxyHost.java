package com.huafagroup.core.service.assist;

import cn.hutool.core.util.StrUtil;
import com.huafagroup.core.configuration.TransactionConfig;
import org.apache.http.HttpHost;

public interface ProxyHost {

    default HttpHost getProxyHost() {
        TransactionConfig transactionConfig = getTransactionConfig();
        //需要使用代理,否则调用接口失败
        if (StrUtil.isNotBlank(transactionConfig.getProxyIP()) && transactionConfig.getProxyPort() != null) {
            return new HttpHost(transactionConfig.getProxyIP(),transactionConfig.getProxyPort());
        }
        return null;
    }
    TransactionConfig getTransactionConfig();
}
