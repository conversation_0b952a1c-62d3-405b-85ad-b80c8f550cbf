package com.huafagroup.core.service.trans;


import com.huafagroup.core.dao.trans.TransactionMapper;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.entity.trans.Transaction;
import com.huafagroup.core.enums.TransactionEnum;
import com.huafagroup.core.service.system.BaseServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-19 09:50:25
 */

@Service
public class TransactionService extends BaseServiceImpl<Transaction> {

    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private TransactionMapper mapper;

    public void addTransaction(Order order, boolean isRefund) {
        Transaction transaction = new Transaction(order.getAmount(), order.getAccountId(),
                order.getDesc(), isRefund ? TransactionEnum.REFUND.name() : TransactionEnum.PAY.name(),
                order.getId());
        logger.info("增加流水记录，订单ID：{}，类型：{}，金额分：{}", transaction.getOrderId(), transaction.getType(), transaction.getAmount());
        insert(transaction);
    }

    public void addTransaction(HbOrder order, boolean isRefund) {
        Transaction transaction = new Transaction(order.getAmount(), order.getAccountId(),
                order.getDesc(), isRefund ? TransactionEnum.TRANSFER_REFUND.name() : TransactionEnum.TRANSFER.name(),order.getId());
        logger.info("增加流水记录，订单ID：{}，类型：{}，金额分：{}", transaction.getOrderId(), transaction.getType(), transaction.getAmount());
        insert(transaction);
    }

}