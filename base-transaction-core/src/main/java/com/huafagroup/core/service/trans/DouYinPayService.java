package com.huafagroup.core.service.trans;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import com.huafagroup.core.configuration.TransactionConfig;
import com.huafagroup.core.dto.douyin.DouyinPayParamsVO;
import com.huafagroup.core.dto.douyin.OrderInfo;
import com.huafagroup.core.dto.wx.WxUnifiedOrderResDto;
import com.huafagroup.core.enums.PayProviderEnum;
import com.huafagroup.core.enums.ApiTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/***
 * 抖音支付
 */
@Service
public class DouYinPayService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DouYinPayService.class);
    @Value("${base.douyin.appId}")
    private String douYinAppId;
    @Value("${base.douyin.mchId}")
    private String douYinMchId;
    @Value("${base.douyin.signKey}")
    private String douYinSecret;
    @Autowired
    private TransService wxTransService;
    @Autowired
    private TransactionConfig transactionConfig;
    /***
     * 抖音支付 (目前只支持微信支付)
     *  包装微信支付 MWEB
     * @param orderParamsVO
             * @return
             */
    public OrderInfo pay(DouyinPayParamsVO orderParamsVO) throws Exception {
        orderParamsVO.setProviderEnum(PayProviderEnum.DOUYIN.name());
        orderParamsVO.setApiTypeEnum(ApiTypeEnum.PAY_UNIFIEDORDER);
        orderParamsVO.setCreateDate(new Date());
        WxUnifiedOrderResDto orderResultVO = (WxUnifiedOrderResDto)wxTransService.pay(orderParamsVO);
        return genOrderInfo(orderParamsVO, orderResultVO);
    }

    public OrderInfo genOrderInfo(DouyinPayParamsVO reqDto, WxUnifiedOrderResDto wxOrderResDto) {
        JSONObject riskInfo = new JSONObject();
        riskInfo.putOnce("ip", reqDto.getClientIp());

        OrderInfo orderInfo = new OrderInfo();
                orderInfo.setMerchantId(douYinMchId);
                orderInfo.setAppId(douYinAppId);
                orderInfo.setSignType("MD5");
                orderInfo.setTimestamp(StrUtil.toString(reqDto.getCreateDate().getTime() / 1000));
                orderInfo.setVersion("2.0");
                orderInfo.setTradeType("H5");
                orderInfo.setProductCode("pay");
                orderInfo.setPaymentType("direct");
                orderInfo.setOutOrderNo(wxOrderResDto.getOrderNo());
                orderInfo.setUid(IdUtil.simpleUUID());
                orderInfo.setTotalAmount(reqDto.getAmount());
                orderInfo.setCurrency("CNY");
                orderInfo.setSubject(reqDto.getDescription());
                orderInfo.setBody(reqDto.getDescription());
                orderInfo.setTradeTime(StrUtil.toString(reqDto.getCreateDate().getTime() / 1000));
                orderInfo.setValidTime(StrUtil.toString(300));
                orderInfo.setNotifyUrl(StrUtil.format(transactionConfig.getPayNotifyUrl(),wxOrderResDto.getOrderNo()));
                orderInfo.setWxUrl(wxOrderResDto.getMwebUrl());
                orderInfo.setWxType("MWEB");
        Map<String, Object> map = BeanUtil.beanToMap(orderInfo, true, true);
        String sign = getSignDy(map, douYinSecret);
        orderInfo.setSign(sign);
        orderInfo.setRiskInfo(riskInfo.toString());
        orderInfo.setUnSignStr(getUnSignStr(map, douYinSecret));
        return orderInfo;
    }

    public static String getSignDy(Map<String, Object> map, String appSecret){
        StringBuffer s = new StringBuffer();

        CollUtil.sortByEntry(map, Map.Entry.comparingByKey()).keySet().forEach(k -> s.append(k).append("=").append(map.get(k)).append("&"));

        String ss = StrUtil.removeSuffix(s.toString(),"&") + appSecret;

        return SecureUtil.md5(ss);
    }

    public static String getUnSignStr(Map<String, Object> map, String appSecret){
        StringBuffer s = new StringBuffer();

        CollUtil.sortByEntry(map, Map.Entry.comparingByKey()).keySet().forEach(k -> s.append(k).append("=").append(map.get(k)).append("&"));

        String ss = StrUtil.removeSuffix(s.toString(),"&") + appSecret;

        return ss;
    }
}