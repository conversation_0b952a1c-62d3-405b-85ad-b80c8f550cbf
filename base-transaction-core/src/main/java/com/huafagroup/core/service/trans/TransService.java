package com.huafagroup.core.service.trans;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huafagroup.core.dto.*;
import com.huafagroup.core.entity.trans.Account;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.enums.PayProviderEnum;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.PayServiceProvider;
import com.huafagroup.core.util.Constants;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.RequestEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class TransService{

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private OrderService orderService;

    @Autowired
    private PayServiceProvider payServiceProvider;

    @Autowired
    private NotifyBizService notifyBizService;

    @Autowired
    private HbOrderService hbOrderService;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    RedissonClient redissonClient;

    /**
     * 支付下单
     *
     * @param paramsVO
     * @return PayOrderResultVO
     * @throws Exception
     */
    @Transactional
    public PayOrderResultVO pay(PayParamsVO paramsVO) throws Exception {
        logger.info("发起支付请求:{}", JSONUtil.toJsonStr(paramsVO));
        //创建订单
        Order order = orderService.createOrder(paramsVO);
        // 通过projectId和channel获取Account
        Account account = accountService.getForPay(paramsVO.getProjectId(), paramsVO.getChannel());
        order.setAccountId(account.getId());
        //发起支付，并返回支付结果
        PayProviderEnum payProviderEnum = PayProviderEnum.valueOf(paramsVO.getProviderEnum());
        PayOrderResultVO resultVO = payServiceProvider.getPayService(payProviderEnum).pay(order,paramsVO.getApiTypeEnum());
        //支付过程中，会更新订单信息，需要这边统一保存
        orderService.updateOrder(order);
        resultVO.setOrderNo(order.getOrderNo());
        logger.info("返回支付响应:{}", JSONUtil.toJsonStr(resultVO));
        return resultVO;
    }



    /**
     * 退款下单
     *
     * @param paramsVO
     * @return
     * @throws Exception
     */
    @Transactional
    public RefundsResultVO refund(RefundParamsVO paramsVO) throws Exception {
        logger.info("发起退款请求:{}", JSONUtil.toJsonStr(paramsVO));
        //获取退款的订单
        Order order = orderService.getRefundOrder(paramsVO);
        order.setRefundReason(paramsVO.getReason());
        order.setRefundBizNotifyUrl(paramsVO.getBizNotifyUrl());
        order.setRefundBizNotifyStatus(OrderStatusEnum.NOTNOTIFY.name());
        order.setRefundStatus(OrderStatusEnum.PROCESSING.name());
        //发起退款，并返回退款结果
        PayProviderEnum payProviderEnum = PayProviderEnum.valueOf(order.getPayProvider());

        RefundsResultVO resultVO = payServiceProvider.getPayService(payProviderEnum).refund(order);

        if (resultVO.getStatus().equals(OrderStatusEnum.SUCCESS.name())) {
            //如果退款成功，则更新业务系统通知状态
            order.setRefundBizNotifyStatus(OrderStatusEnum.SUCCESS.name());
            order.setRefundBizNotifyTime(new Date());
        }

        //退款过程中，会更新订单信息，需要这边统一保存
        orderService.updateOrder(order);

        logger.info("返回响应:{}", JSONUtil.toJsonStr(resultVO));
        return resultVO;
    }

    /**
     * 支付通知
     * @param requestEntity
     * @param orderNo
     * @return
     * @throws Exception
     */
    @Transactional
    public NotifyResult payNotify(RequestEntity<ObjectNode> requestEntity, String orderNo) throws Exception {
        logger.info("发起支付通知请求:{}-{}", orderNo,requestEntity.getBody().toString());
        Order order = orderService.selectByNo(orderNo);
        //如果不是初始值NOTPAY,则说明已经更新状态了，不需要再处理了。
        if (OrderStatusEnum.NOTPAY.name().equals(order.getStatus())){

            PayProviderEnum payProviderEnum = PayProviderEnum.valueOf(order.getPayProvider());
            payServiceProvider.getPayService(payProviderEnum).payNotify(requestEntity,order);

            orderService.updateOrder(order);
            //如果状态是成功，则增加流水
            if (OrderStatusEnum.SUCCESS.name().equals(order.getStatus())) {
                transactionService.addTransaction(order,false);
            }
        }
        //通知业务系统
        notifyBizService.payNotify(order);

        return NotifyResult.success();
    }

    /**
     * 退款通知
     * @param requestEntity
     * @param orderNo
     * @return
     * @throws Exception
     */
    @Transactional
    public NotifyResult refundNotify(RequestEntity<ObjectNode> requestEntity, String orderNo) throws Exception{
        logger.info("发起退款通知请求:{}-{}", orderNo,requestEntity.getBody().toString());
        Order order = orderService.selectByNo(orderNo);
        PayProviderEnum payProviderEnum = PayProviderEnum.valueOf(order.getPayProvider());
        payServiceProvider.getPayService(payProviderEnum).refundNotify(requestEntity,order);

        orderService.updateOrder(order);
        //如果状态是成功，则增加流水
        if (OrderStatusEnum.SUCCESS.name().equals(order.getRefundStatus())) {
            transactionService.addTransaction(order,true);
        }

        //通知业务系统
        notifyBizService.refundNotify(order);

        return NotifyResult.success();
    }

    /**
     * 企业支付到零钱
     * @param paramsVO
     * @return
     * @throws Exception
     */
    @Transactional
    public TransferResultVO transfer(TransferParamsVO paramsVO) throws Exception {
        Account account = accountService.getForPay(paramsVO.getProjectId(),paramsVO.getChannel());
        TransferResultVO resultVO = null;
        //先看支付的商户是不是第三方，比如原圈科技
        Merchant merchant = merchantService.selectById(account.getPayMerchantId());
        Assert.notNull(merchant, "该项目未配置付款商户号");
        PayProviderEnum payProviderEnum = getPayProviderEnum(merchant);
        //创建激励订单，但不会保存先
        HbOrder hbOrder = hbOrderService.createOrder(paramsVO);
        hbOrder.setAccountId(account.getId());
        hbOrder.setMchId(merchant.getMchId());
        int amount = paramsVO.getAmount();
        //判断激励余额是否足够，先扣去激励金额，如果小于0就返回失败，同时需要增加回刚才扣减的
        RAtomicLong atomicLong = redissonClient.getAtomicLong(Constants.ACCOUNT_BALANCE_KEY_PREFIX + account.getId());
        //如果数据库账号余额不为0，但是Redis中没有记录，有可能是没有充值过，或者缓存过期被清理了，这里重新设置下
        if (account.getBalance() > 0 && !atomicLong.isExists()) {
            logger.info("账号{}在Redis中没有余额，有可能是没有充值过，或者缓存过期被清理了，这里重新设置下余额为{}",account.getId(),account.getBalance());
            atomicLong.addAndGet(account.getBalance());
        }
        long afterAdd = atomicLong.addAndGet(-amount);
        logger.info("发送激励后的账户余额为【Redis】：{}",afterAdd);
        try{
            if (afterAdd < 0) {
                throw new BusinessException(ExceptionEnum.BUSINESS_ERROR.getCode(), "账户余额不足");
            }
            //发起支付，并返回支付结果
            resultVO = payServiceProvider.getPayService(payProviderEnum).transfer(hbOrder,paramsVO.getApiTypeEnum());
        } catch (Exception e) {
            //发送失败，需要退回金额
            long l = atomicLong.addAndGet(amount);
            logger.info("激励发送失败，回退【Redis】后的余额为{}",l);
            throw e;
        }
        logger.info("发送激励后的账户余额为【Redis】：{}",afterAdd);
        //支付过程中，会更新订单信息，需要这边统一保存
        hbOrderService.update(hbOrder);

        //增加流水
        transactionService.addTransaction(hbOrder,false);
        //更新账户余额
        accountService.decAndUpdateBalance(account,amount);

        logger.info("返回响应:{}", JSONUtil.toJsonStr(resultVO));
        return resultVO;
    }

    /**
     * 根据商户信息返回PayProviderEnum
     * @param merchant
     * @return
     */
    private PayProviderEnum getPayProviderEnum(Merchant merchant) {
        switch (merchant.getMchId()) {
            case "RUTHUS_TECH":
                return PayProviderEnum.RUTHUS_TECH;
            default:
                return PayProviderEnum.WECHAT;
        }
    }

    /**
     * 获取退款结果
     * @param orderNo
     * @param isFromSync
     * @return
     * @throws Exception
     */
    public BizNotifyResult getRefundResult(String orderNo,boolean isFromSync) throws Exception {
        logger.info("查询退款结果:{}", orderNo);
        Order order = orderService.selectByNo(orderNo);
        String oldStatus = order.getStatus();
        PayProviderEnum payProviderEnum = PayProviderEnum.valueOf(order.getPayProvider());
        BizNotifyResult resultVO = payServiceProvider.getPayService(payProviderEnum).getRefundResult(order);
        //如果是同步任务调用的，且状态有变更，则通知业务系统
        if (isFromSync && !oldStatus.equals(resultVO.getStatus())) {
            notifyBizService.payNotify(order);
        }
        logger.info("返回响应:{}", JSONUtil.toJsonStr(resultVO));
        return resultVO;
    }


    /**
     * 获取支付结果
     * @param orderNo
     * @param isFromSync 是否来自同步任务
     * @return
     * @throws Exception
     */
    public BizNotifyResult getPayResult(String orderNo,boolean isFromSync) throws Exception {
        logger.info("查询支付结果:{}", orderNo);
        Order order = orderService.selectByNo(orderNo);
        String oldStatus = order.getStatus();
        PayProviderEnum payProviderEnum = PayProviderEnum.valueOf(order.getPayProvider());
        BizNotifyResult resultVO = payServiceProvider.getPayService(payProviderEnum).getPayResult(order);
        //如果是同步任务调用的，且状态有变更，则通知业务系统
        if (isFromSync && !oldStatus.equals(resultVO.getStatus())) {
            notifyBizService.refundNotify(order);
        }
        logger.info("返回响应:{}", JSONUtil.toJsonStr(resultVO));
        return resultVO;
    }

    /**
     * 关闭支付订单
     * @param orderNo
     * @throws Exception
     */
    public void close(String orderNo) throws Exception {
        Order order = orderService.selectByNo(orderNo);
        PayProviderEnum payProviderEnum = PayProviderEnum.valueOf(order.getPayProvider());
        payServiceProvider.getPayService(payProviderEnum).close(order);
        orderService.updateOrder(order);
    }

    @Transactional
    public TransferResultVO syncHbOrderStatus(HbOrder order) throws Exception {
        logger.info("查询红包领取结果:{}", order.getOrderNo());
        String payProvider = PayProviderEnum.WECHAT.name();
        PayProviderEnum payProviderEnum = PayProviderEnum.valueOf(payProvider);
        String oldStatus = order.getStatus();
        TransferResultVO resultVO = payServiceProvider.getPayService(payProviderEnum).syncHbOrderStatus(order);
        String newStatus = order.getStatus();
        if (!oldStatus.equals(newStatus)) {
            //状态发生变化，则更新
            order.setUpdateDate(new Date());
            hbOrderService.update(order);
            if (OrderStatusEnum.REFUND.name().equals(newStatus)) {
                //如果新状态是已退款，则回退金额
                logger.info("发激励状态为已退款，重新给账户退回相应金额【重新充值】。");
                accountService.deposit(accountService.selectById(order.getAccountId()),order.getAmount());
                //增加流水
                transactionService.addTransaction(order,true);
            }
        }
        return resultVO;
    }
}
