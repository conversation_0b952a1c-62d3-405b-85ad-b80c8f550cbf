package com.huafagroup.core.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huafagroup.core.dto.BizNotifyResult;
import com.huafagroup.core.dto.PayOrderResultVO;
import com.huafagroup.core.dto.RefundsResultVO;
import com.huafagroup.core.dto.TransferResultVO;
import com.huafagroup.core.entity.trans.HbOrder;
import com.huafagroup.core.entity.trans.Order;
import com.huafagroup.core.enums.ApiTypeEnum;
import org.springframework.http.RequestEntity;

public interface IPayService {

    /**
     * 统一支付接口
     * @param order
     * @param apiTypeEnum
     * @return
     * @throws Exception
     */
    default PayOrderResultVO pay(Order order, ApiTypeEnum apiTypeEnum) throws Exception {return null;}

    /**
     * 统一退款接口
     * @param order
     * @return
     * @throws Exception
     */
    default RefundsResultVO refund(Order order) throws Exception {return null;}

    /**
     * 统一支付通知接口
     * @param requestEntity
     * @param order
     * @return true 如果需要通知业务系统
     * @throws Exception
     */
    default boolean payNotify(RequestEntity<ObjectNode> requestEntity, Order order) throws Exception {return false;}

    /**
     * 统一退款通知
     * @param requestEntity
     * @param order
     * @return true 如果需要通知业务系统
     * @throws Exception
     */
    default boolean refundNotify(RequestEntity<ObjectNode> requestEntity, Order order) throws Exception {return false;}

    default TransferResultVO transfer(HbOrder order, ApiTypeEnum apiTypeEnum) throws Exception {return null;}

    default BizNotifyResult getRefundResult(Order order) throws Exception {return null;}

    default BizNotifyResult getPayResult(Order order) throws Exception {return null;}

    default void close(Order order) throws Exception{};

    default TransferResultVO syncHbOrderStatus(HbOrder order) throws Exception {return null;}
}
