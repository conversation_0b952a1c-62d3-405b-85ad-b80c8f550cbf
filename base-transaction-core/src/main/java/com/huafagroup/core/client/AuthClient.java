package com.huafagroup.core.client;


import com.fasterxml.jackson.core.type.TypeReference;
import com.huafagroup.core.dto.AuthApplicationDetail;
import com.huafagroup.core.dto.AuthResourceDTO;
import com.huafagroup.core.dto.RoleVO;
import com.huafagroup.core.dto.ShowMenu;
import com.huafagroup.core.util.JsonUtil;
import com.huafagroup.core.util.ResultBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调用授权中心接口
 */
@Service
public class AuthClient {

    private final Logger logger = LoggerFactory.getLogger(AuthClient.class);

    @Value("${auth.check.url}")
    private String checkUrl;

    @Value("${api.auth.appNo}")
    private String appNo;

    @Value("${auth.menu.url}")
    private String menuUrl;

    @Value("${auth.user.role.url}")
    private String userRoleUrl;

    @Value("${api.application.url}")
    private String authApplicationUrl;

    @Value("${auth.user.resource.url}")
    private String authResourceUrl;

    @Autowired
    private RestTemplate balanceRestTemplate;

    /**
     * 向授权中心请求是否有该url权限
     *
     * @param account
     * @param url
     * @return
     */
    public boolean hasUrlPrivilege(String account, String url) {
        Map<String, String> map = new HashMap<>();
        map.put("appNo", appNo);
        map.put("oprCode", "read");
        map.put("account", account);
        map.put("res", url);
        try {
            ResponseEntity<ResultBean> resultBean = balanceRestTemplate.postForEntity(checkUrl, map, ResultBean.class);
            return null != resultBean && null != resultBean.getBody() && "200".equalsIgnoreCase(resultBean.getBody().getCode());
        } catch (Exception e) {
            logger.warn("获取url权限异常:{}", url, e);
            return false;
        }
    }

    public List<ShowMenu> getUserMenu(String userId) {
        StringBuilder buf = new StringBuilder();
        buf.append(menuUrl).append("?appNo=").append(appNo);
        buf.append("&userId=").append(userId);
        try {
            ResultBean<List<ShowMenu>> result = balanceRestTemplate.getForObject(buf.toString(), ResultBean.class);
            logger.info("获取用户菜单列表信息:{}", JsonUtil.bean2Json(result));
            return JsonUtil.json2GenericBean(JsonUtil.bean2Json(result.getData()), new TypeReference<List<ShowMenu>>() {
            });
        } catch (Exception e) {
            logger.warn("获取用户菜单列表信息异常:{}", userId, e);
        }
        return null;

    }

    /**
     * 获取角色
     *
     * @param userId
     * @return
     */
    public List<RoleVO> getUserRoleList(String userId) {
        StringBuilder buf = new StringBuilder();
        buf.append(userRoleUrl).append("?appNo=").append(appNo).append("&userId=").append(userId);
        try {
            ResultBean<List<RoleVO>> result = balanceRestTemplate.getForObject(buf.toString(), ResultBean.class);
            logger.info("获取用户角色列表响应参数:{}", JsonUtil.bean2Json(result));
            return JsonUtil.json2GenericBean(JsonUtil.bean2Json(result.getData()), new TypeReference<List<RoleVO>>() {
            });
        } catch (Exception e) {
            logger.info("获取用户角色列表异常,url:{}", buf.toString(), e);
        }
        return null;
    }

    /**
     * 获取应用列表信息
     *
     * @return
     */
    public List<AuthApplicationDetail> getAllAuthApplicationList() {
        try {
            ResultBean<List<AuthApplicationDetail>> result = balanceRestTemplate.getForObject(authApplicationUrl, ResultBean.class);
            logger.info("获取应用列表结果:{}", JsonUtil.bean2Json(result));
            return JsonUtil.json2GenericBean(JsonUtil.bean2Json(result.getData()), new TypeReference<List<AuthApplicationDetail>>() {
            });
        } catch (Exception e) {
            logger.warn("获取应用列表结果异常", e);
        }
        return null;
    }

    /**
     * 获取数据资源
     *
     * @param userId
     * @return
     */
    public List<AuthResourceDTO> getAuthResource(String userId) {
        StringBuilder buf = new StringBuilder();
        buf.append(authResourceUrl).append("?appNo=").append(appNo).append("&userId=").append(userId).append("&type=3");
        try {
            ResultBean<List<AuthResourceDTO>> result = balanceRestTemplate.getForObject(buf.toString(), ResultBean.class);
            logger.info("获取用户授权资源结果:{}", JsonUtil.bean2Json(result));
            return JsonUtil.json2GenericBean(JsonUtil.bean2Json(result.getData()), new TypeReference<List<AuthResourceDTO>>() {
            });
        } catch (Exception e) {
            logger.warn("获取用户授权资源异常,url:{}", buf.toString(), e);
        }
        return null;
    }

}
