package com.huafagroup.core.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.huafagroup.core.dto.UserBasic;
import com.huafagroup.core.util.JsonUtil;
import com.huafagroup.core.util.ResultBean;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 调用用户中心接口
 **/
@Component
public class UserCenterClient {
    private final Logger logger = LoggerFactory.getLogger(UserCenterClient.class);
    private static final String SUCCESS = "200";

    @Value("${user.search.account.url}")
    private String searchAccountUrl;

    @Value("${user.org.account.url}")
    private String orgUserAccountUrl;

    @Value("${user.org.path.url}")
    private String orgPathUrl;

    @Autowired
    private RestTemplate balanceRestTemplate;

    public UserBasic getUserDetailsByAccount(String account) {
        if (StringUtils.isBlank(account)) {
            return null;
        }

        StringBuilder buf = new StringBuilder();
        buf.append(searchAccountUrl).append("?useraccount=").append(account);
        try {
            ResultBean<List<UserBasic>> resultBean = balanceRestTemplate.getForObject(buf.toString(), ResultBean.class);
            logger.debug("通过account获取用户信息 account:{},响应数据:{}", account, JsonUtil.bean2Json(resultBean));
            if (SUCCESS.equals(resultBean.getCode()) && !CollectionUtils.isEmpty(resultBean.getData())) {
                String json = JsonUtil.bean2Json(resultBean.getData().get(0));
                return JsonUtil.json2GenericBean(json, new TypeReference<UserBasic>() {
                });
            }
        } catch (Exception e) {
            logger.warn("通过account获取用户信息异常 account:{}", account, e);
        }
        return null;
    }

    /**
     * 通过组织架构ID获取用户信息
     *
     * @param orgId
     * @return
     */
    public List<UserBasic> getOrgUserBasicInfo(String orgId) {
        StringBuilder buf = new StringBuilder();
        buf.append(orgUserAccountUrl).append("?orgId=").append(orgId);
        try {
            ResultBean<List<UserBasic>> result = balanceRestTemplate.getForObject(buf.toString(), ResultBean.class);
            return JsonUtil.json2GenericBean(JsonUtil.bean2Json(result.getData()), new TypeReference<List<UserBasic>>() {
            });
        } catch (Exception e) {
            logger.info("获取组织架构下的用户异常:{}", buf.toString(), e);
        }
        return null;
    }

//    public List<PathOrgVO> getOrgByPath(List<String> orgPaths) {
//        Map<String, List<String>> reqData = new HashMap<>();
//        reqData.put("orgPathList", orgPaths);
//        try {
//            ResponseEntity<ResultBean> responseEntity = balanceRestTemplate.postForEntity(orgPathUrl, reqData, ResultBean.class);
//            logger.debug("获取用户角色组织架构响应参数:{}", JsonUtil.bean2Json(responseEntity));
//            if (null != responseEntity && null != responseEntity.getBody() && null != responseEntity.getBody().getData()) {
//                return JsonUtil.json2GenericBean(JsonUtil.bean2Json(responseEntity.getBody().getData()), new TypeReference<List<PathOrgVO>>() {
//                });
//            }
//        } catch (Exception e) {
//            logger.info("获取用户角色组织架构异常,url:{},data:{}", orgPathUrl, JsonUtil.bean2Json(reqData), e);
//        }
//        return null;
//    }
}
