package com.huafagroup.core.dao.trans;

import com.huafagroup.core.dto.ProjectMerchantVO;
import com.huafagroup.core.dto.QueryProjectDTO;
import com.huafagroup.core.entity.trans.Project;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.common.special.InsertListMapper;

import java.util.List;

/**
 * <p>
 * </p>
 * 
 * <AUTHOR>
 * @date 2021-04-16 16:50:39
 * @version
 */
public interface ProjectMapper extends Mapper<Project>,InsertListMapper<Project> {


    List<ProjectMerchantVO> getList(QueryProjectDTO dto);
}