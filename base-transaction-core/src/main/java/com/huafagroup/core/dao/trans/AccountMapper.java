package com.huafagroup.core.dao.trans;

import com.huafagroup.core.entity.trans.Account;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.common.special.InsertListMapper;

/**
 * <p>
 * </p>
 * 
 * <AUTHOR>
 * @date 2021-04-19 11:33:17
 * @version
 */
public interface AccountMapper extends Mapper<Account>,InsertListMapper<Account> {

    @Update("UPDATE trans_account SET balance = IFNULL(balance,0) + #{amount} WHERE id = #{id}")
    int updateBalance(Integer id, int amount);

}