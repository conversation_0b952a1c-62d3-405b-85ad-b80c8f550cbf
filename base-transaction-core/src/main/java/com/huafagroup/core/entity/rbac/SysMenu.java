package com.huafagroup.core.entity.rbac;
import javax.persistence.*;
import java.io.Serializable;

import org.hibernate.validator.constraints.Length;
import com.huafagroup.core.entity.BaseEntity;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-02-03 09:45:24
 * @version
 */
@Table(name = "sys_menu")
public class SysMenu extends BaseEntity implements Serializable {

	     /**
     * 菜单层级
     */
    @Column(name = "level")
	private Integer level;
	     /**
     * 菜单父ID
     */
    @Length(max=40,message="菜单父ID 长度不能超过40")
    @Column(name = "parent_id")
	private String parentId;
	     /**
     * 菜单名称
     */
    @Length(max=40,message="菜单名称 长度不能超过40")
    @Column(name = "name")
	private String name;
				     /**
     * 菜单URL
     */
    @Length(max=500,message="菜单URL 长度不能超过500")
    @Column(name = "url")
	private String url;
		     /**
     * 打开方式
     */
    @Column(name = "target")
	private Integer target;

	/**
	 * 排序
	 */
	@Column(name = "sort")
	private Integer sort;

	/**
	 * 菜单图标
	 */
	@Length(max=100,message="icon 长度不能超过100")
	@Column(name = "icon")
	private String icon;

	/**
	 * 权限标识
     */
	@Length(max=200,message="权限标识 长度不能超过200")
	@Column(name = "permission")
	private String permission;


	/**
	 * 菜单类型
	 */
	@Column(name = "type")
	private Integer type;

	public String getPermission() {
		return permission;
	}

	public void setPermission(String permission) {
		this.permission = permission;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public Integer getLevel() {
        return level;
    }

	public void setLevel(Integer level) {
    	 this.level = level;
	}


	public String getParentId() {
        return parentId;
    }

	public void setParentId(String parentId) {
    	 this.parentId = parentId;
	}


	public String getName() {
        return name;
    }

	public void setName(String name) {
    	 this.name = name;
	}


	public String getUrl() {
        return url;
    }

	public void setUrl(String url) {
    	 this.url = url;
	}

        	        	        
	public Integer getTarget() {
        return target;
    }

	public void setTarget(Integer target) {
    	 this.target = target;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
}