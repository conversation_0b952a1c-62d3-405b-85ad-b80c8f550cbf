package com.huafagroup.core.entity.uc;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-24 08:43:16
 */
public class UserThirdPartyDTO implements Serializable {

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    @NotNull(message = "userId not allow null")
    private String userId;
    /**
     * 绑定应用编码
     */
    @ApiModelProperty("绑定应用编码")
    @NotNull(message = "appCode not allow null")
    private String appCode;
    /**
     * union_id
     */
    @ApiModelProperty("union_id")
    @NotNull(message = "unionId not allow null")
    private String unionId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }
}