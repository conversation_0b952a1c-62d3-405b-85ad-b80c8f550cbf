package com.huafagroup.core.entity.rbac;
import javax.persistence.*;
import java.io.Serializable;

import org.hibernate.validator.constraints.Length;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-02-14 16:06:18
 * @version
 */
@Table(name = "sys_role_menu")
public class SysRoleMenu implements Serializable {

    @Length(max=32,message=" 长度不能超过32")
    @Column(name = "menu_id")
	private String menuId;
	    @Length(max=32,message=" 长度不能超过32")
    @Column(name = "role_id")
	private String roleId;
	
	        
	public String getMenuId() {
        return menuId;
    }

	public void setMenuId(String menuId) {
    	 this.menuId = menuId;
	}

        	        
	public String getRoleId() {
        return roleId;
    }

	public void setRoleId(String roleId) {
    	 this.roleId = roleId;
	}

        	}