package com.huafagroup.core.entity;

import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <p>
 * <p>
 * ${remark}
 * </p>
 *
 * <AUTHOR>
 * @date 2018-12-20 15:17:21
 */
@Table(name = "dictionary_item")
public class DictionaryItem extends IntegerBaseEntity implements Serializable {
    /**
     * 类型ID
     */
    @Length(max = 255, message = "TYPE_ID 长度不能超过255")
    @Column(name = "TYPE_ID")
    private String  typeId;
    /**
     * 序号
     */
    @Column(name = "SORT")
    private Integer sort;
    /**
     * 字典内容
     */
    @Length(max = 255, message = "TEXT 长度不能超过255")
    @Column(name = "TEXT")
    private String  text;
    /**
     * 值
     */
    @Length(max = 255, message = "VALUE 长度不能超过255")
    @Column(name = "VALUE")
    private String  value;
    @Length(max = 255, message = "parent_id 长度不能超过255")
    @Column(name = "parent_value")
    private String  parentValue;

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getParentValue() {
        return parentValue;
    }

    public void setParentValue(String parentValue) {
        this.parentValue = parentValue;
    }
}