package com.huafagroup.core.entity.auth;

import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-09-10 14:21:05
 * @version
 */
@Table(name = "auth_org_role")
public class OrgRole extends BaseEntity implements Serializable {

	     /**
     * 角色Id
     */
    @Length(max=32,message="角色Id 长度不能超过32")
    @NotNull(message = "role_id not allow null")
    @Column(name = "role_id")
	private String roleId;
	     /**
     * 组织Id
     */
    @Length(max=32,message="组织Id 长度不能超过32")
    @NotNull(message = "org_id not allow null")
    @Column(name = "org_id")
	private String orgId;
					
	        	        
	public String getRoleId() {
        return roleId;
    }

	public void setRoleId(String roleId) {
    	 this.roleId = roleId;
	}

        	        
	public String getOrgId() {
        return orgId;
    }

	public void setOrgId(String orgId) {
    	 this.orgId = orgId;
	}

        	        	        	        	        	}