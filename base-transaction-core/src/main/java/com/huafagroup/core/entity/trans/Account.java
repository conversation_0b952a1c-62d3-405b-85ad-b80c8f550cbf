package com.huafagroup.core.entity.trans;

import com.huafagroup.core.entity.IntegerBaseEntity;
import com.huafagroup.core.enums.ChannelEnum;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-19 11:33:16
 */
@Table(name = "trans_account")
public class Account extends IntegerBaseEntity implements Serializable {

    /**
     * 0-默认，1-删除
     */
    @NotNull(message = "del_flag not allow null")
    @Column(name = "del_flag")
    private Integer delFlag = 0;
    /**
     * 余额，单位分
     */
    @Column(name = "balance")
    private Long balance;
    /**
     * 营销系统同步过来的项目id
     */
    @Length(max = 32, message = "营销系统同步过来的项目id 长度不能超过32")
    @NotBlank(message = "project_id not allow null")
    @Column(name = "project_id")
    private String projectId;
    /**
     * 账户名称
     */
    @Length(max = 50, message = "账户名称 长度不能超过50")
    @Column(name = "name")
    private String name;
    /**
     * 商户表对应id
     */
    @Column(name = "merchant_id")
    private Integer merchantId;
    /**
     * 用于激励的付款商户商户表对应id
     */
    @Column(name = "pay_merchant_id")
    private Integer payMerchantId;
    /**
     * 渠道（如：置业通、社群）
     */
    @Column(name = "channel")
    private ChannelEnum channel;


    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }


    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }


    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public Integer getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Integer merchantId) {
        this.merchantId = merchantId;
    }

    public Integer getPayMerchantId() {
        return payMerchantId;
    }

    public void setPayMerchantId(Integer payMerchantId) {
        this.payMerchantId = payMerchantId;
    }

    public ChannelEnum getChannel() {
        return channel;
    }

    public void setChannel(ChannelEnum channel) {
        this.channel = channel;
    }
}