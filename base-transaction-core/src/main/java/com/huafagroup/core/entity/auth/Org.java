package com.huafagroup.core.entity.auth;

import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-09-06 10:29:14
 * @version
 */
@Table(name = "auth_org")
public class Org extends BaseEntity implements Serializable {

	/**
	 * 父组织编码
	 */
	@Length(max = 32, message = "父组织编码 长度不能超过32")
	@NotNull(message = "parent_org_id not allow null")
	@Column(name = "parent_org_id")
	private String parentOrgId;
	/**
	 * 组织类型
	 * 1: 集团
	 * 2：公司
	 * 3：部门
	 */
	@NotNull(message = "org_category not allow null")
	@Column(name = "org_category")
	private Integer orgCategory;
	/**
	 * 组织状态
	 * 0: 禁用
	 * 1: 启用
	 */
	@NotNull(message = "enabled not allow null")
	@Column(name = "enabled")
	private Boolean enabled;
	/**
	 * 组织板块
	 */
	@Length(max = 32, message = "组织板块 长度不能超过32")
	@NotNull(message = "org_section not allow null")
	@Column(name = "org_section")
	private String orgSection;
	/**
	 * 同级组织内部排序
	 */
	@NotNull(message = "org_order not allow null")
	@Column(name = "org_order")
	private Integer orgOrder;
	/**
	 * 组织全名
	 */
	@Length(max = 50, message = "组织全名 长度不能超过50")
	@NotNull(message = "full_name not allow null")
	@Column(name = "full_name")
	private String fullName;
	/**
	 * 组织层级
	 */
	@NotNull(message = "org_level not allow null")
	@Column(name = "org_level")
	private Integer orgLevel;
	/**
	 * 组织路径
	 */
	@Length(max = 2000, message = "组织路径 长度不能超过2000")
	@NotNull(message = "org_path not allow null")
	@Column(name = "org_path")
	private String orgPath;
	/**
	 * 组织编码
	 */
	@Length(max = 30, message = "组织编码 长度不能超过30")
	@NotNull(message = "org_no not allow null")
	@Column(name = "org_no")
	private String orgNo;
	/**
	 * 组织名称
	 */
	@Length(max = 50, message = "组织名称 长度不能超过50")
	@NotNull(message = "org_name not allow null")
	@Column(name = "org_name")
	private String orgName;

	public String getParentOrgId() {
		return parentOrgId;
	}

	public void setParentOrgId(String parentOrgId) {
		this.parentOrgId = parentOrgId;
	}


	public Integer getOrgCategory() {
		return orgCategory;
	}

	public void setOrgCategory(Integer orgCategory) {
		this.orgCategory = orgCategory;
	}


	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}


	public String getOrgSection() {
		return orgSection;
	}

	public void setOrgSection(String orgSection) {
		this.orgSection = orgSection;
	}


	public Integer getOrgOrder() {
		return orgOrder;
	}

	public void setOrgOrder(Integer orgOrder) {
		this.orgOrder = orgOrder;
	}


	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}


	public Integer getOrgLevel() {
		return orgLevel;
	}

	public void setOrgLevel(Integer orgLevel) {
		this.orgLevel = orgLevel;
	}


	public String getOrgPath() {
		return orgPath;
	}

	public void setOrgPath(String orgPath) {
		this.orgPath = orgPath;
	}


	public String getOrgNo() {
		return orgNo;
	}

	public void setOrgNo(String orgNo) {
		this.orgNo = orgNo;
	}


	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
}