package com.huafagroup.core.entity.auth;

import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-19 14:15:40
 */
@Table(name = "auth_application")
public class Application extends BaseEntity implements Serializable {

    @Length(max = 50, message = " 长度不能超过50")
    @NotNull(message = "owner not allow null")
    @Column(name = "owner")
    private String owner;
    @Length(max = 50, message = " 长度不能超过50")
    @NotNull(message = "name not allow null")
    @Column(name = "name")
    private String name;
    @Length(max = 200, message = " 长度不能超过200")
    @Column(name = "description")
    private String description;
    @Length(max = 50, message = " 长度不能超过50")
    @Column(name = "section")
    private String section;
    /**
     * 是否可用
     * 0：不可用
     * 1：可用
     */
    @Column(name = "enabled")
    private Boolean enabled;


    @Column(name = "appNo")
    private String appNo;


    @Transient
    private String serviceId;


    @Transient
    private String userId;

    public Boolean getSuperAdmin() {
        return isSuperAdmin;
    }

    public void setSuperAdmin(Boolean superAdmin) {
        isSuperAdmin = superAdmin;
    }

    @Transient
    private Boolean isSuperAdmin;

    @Transient
    private String systemUserId;

    @Column(name = "appsecret")
    private String appsecret;

    @Column(name = "auth_type")
    private String authType;



    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSystemUserId() {
        return systemUserId;
    }

    public void setSystemUserId(String systemUserId) {
        this.systemUserId = systemUserId;
    }


    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getAppNo() {
        return appNo;
    }

    public void setAppNo(String appNo) {
        this.appNo = appNo;
    }

    public String getAppsecret() {
        return appsecret;
    }

    public void setAppsecret(String appsecret) {
        this.appsecret = appsecret;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }


    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }


    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

}