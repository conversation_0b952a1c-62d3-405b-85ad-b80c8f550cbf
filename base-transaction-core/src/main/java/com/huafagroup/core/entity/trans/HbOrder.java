package com.huafagroup.core.entity.trans;

import com.huafagroup.core.entity.IntegerBaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-22 16:33:50
 */
@Table(name = "trans_hb_order")
public class HbOrder extends IntegerBaseEntity implements Serializable {

    /**
     * 订单号，UUID生成
     */
    @Length(max = 64, message = "订单号，UUID生成 长度不能超过64")
    @NotBlank(message = "order_no not allow null")
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 随机字符串
     */
    @Length(max = 45, message = "随机字符串 长度不能超过45")
    @NotBlank(message = "nonce_str not allow null")
    @Column(name = "nonce_str")
    private String nonceStr;
    /**
     * 商户号
     */
    @Length(max = 45, message = "商户号 长度不能超过45")
    @Column(name = "mch_id")
    private String mchId;
    /**
     * 项目id
     */
    @Length(max = 45, message = "项目id 长度不能超过45")
    @NotBlank(message = "project_id not allow null")
    @Column(name = "project_id")
    private String projectId;
    /**
     * 公众账号appid
     */
    @Length(max = 64, message = "公众账号appid 长度不能超过64")
    @Column(name = "app_id")
    private String appId;
    /**
     * 微信付款单号
     */
    @Length(max = 64, message = "微信付款单号  长度不能超过64")
    @Column(name = "transaction_id")
    private String transactionId;
    /**
     * 红包金额，单位分
     */
    @NotNull(message = "amount not allow null")
    @Column(name = "amount")
    private Integer amount;
    /**
     * 收款用户姓名，如果check_name设置为FORCE_CHECK，则必填用户真实姓名
     */
    @Length(max = 45, message = "收款用户姓名，如果check_name设置为FORCE_CHECK，则必填用户真实姓名 长度不能超过45")
    @Column(name = "re_user_name")
    private String reUserName;
    /**
     * 活动名称
     */
    @Length(max = 45, message = "活动名称 长度不能超过45")
    @NotBlank(message = "act_name not allow null")
    @Column(name = "act_name")
    private String actName;
    /**
     * 用户openid
     */
    @Length(max = 45, message = "用户openid 长度不能超过45")
    @Column(name = "openid")
    private String openid;
    /**
     * 发放红包使用场景
     */
    @Length(max = 45, message = "发放红包使用场景 长度不能超过45")
    @NotBlank(message = "scene_id not allow null")
    @Column(name = "scene_id")
    private String sceneId;
    /**
     * 付款成功时间
     */
    @Column(name = "success_time")
    private Date successTime;
    /**
     * 红包祝福语
     */
    @Length(max = 45, message = "红包祝福语 长度不能超过45")
    @NotBlank(message = "wishing not allow null")
    @Column(name = "wishing")
    private String wishing;
    /**
     * 账号ID
     */
    @Column(name = "account_id")
    private Integer accountId;
    /**
     * 商户名称
     */
    @Length(max = 64, message = "商户名称 长度不能超过64")
    @NotBlank(message = "send_name not allow null")
    @Column(name = "send_name")
    private String sendName;
    /**
     * 校验用户姓名选项 NO_CHECK：不校验真实姓名，FORCE_CHECK：强校验真实姓名
     */
    @Length(max = 45, message = "校验用户姓名选项 NO_CHECK：不校验真实姓名，FORCE_CHECK：强校验真实姓名 长度不能超过45")
    @Column(name = "check_name")
    private String checkName;
    /**
     * 业务订单号
     */
    @Length(max = 45, message = "业务订单号 长度不能超过45")
    @Column(name = "biz_order_no")
    private String bizOrderNo;
    /**
     * 企业付款备注
     */
    @Length(max = 128, message = "企业付款备注 长度不能超过128")
    @Column(name = "`desc`")
    private String desc;
    /**
     * NOTPAY(初始状态)/SUCCESS（成功）/FAIL（失败）
     */
    @Length(max = 45, message = "NOTPAY(初始状态)/SUCCESS（成功）/FAIL（失败） 长度不能超过45")
    @NotBlank(message = "status not allow null")
    @Column(name = "`status`")
    private String status;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }


    public String getNonceStr() {
        return nonceStr;
    }

    public void setNonceStr(String nonceStr) {
        this.nonceStr = nonceStr;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }


    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }


    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }


    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }


    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }


    public String getReUserName() {
        return reUserName;
    }

    public void setReUserName(String reUserName) {
        this.reUserName = reUserName;
    }


    public String getActName() {
        return actName;
    }

    public void setActName(String actName) {
        this.actName = actName;
    }


    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }


    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }


    public Date getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(Date successTime) {
        this.successTime = successTime;
    }


    public String getWishing() {
        return wishing;
    }

    public void setWishing(String wishing) {
        this.wishing = wishing;
    }


    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }


    public String getSendName() {
        return sendName;
    }

    public void setSendName(String sendName) {
        this.sendName = sendName;
    }


    public String getCheckName() {
        return checkName;
    }

    public void setCheckName(String checkName) {
        this.checkName = checkName;
    }


    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

}