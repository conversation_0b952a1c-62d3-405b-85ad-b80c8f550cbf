package com.huafagroup.core.entity.auth;

import com.huafagroup.core.annotation.SysDictItem;
import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-19 10:34:11
 */
@Table(name = "auth_resource")
public class Resource extends BaseEntity implements Serializable {


    /**
     * 菜单层级
     */
    @Column(name = "level")
    private Integer level;


    /**
     * 菜单层级(不等于的)
     */
    @Transient
    private Integer levelNot;


    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;


    /**
     * 是否是功能性资源
     * 0：数据资源
     * 1：功能性资源
     */
    @SysDictItem(value = "isFuncResource")
    @Column(name = "is_func_resource")
    private String isFuncResource;
    /**
     * 父资源Id
     */
    @Length(max = 32, message = "父资源Id 长度不能超过32")
    @Column(name = "parent_resource_id")
    private String parentResourceId;
    /**
     * 所属版块
     */
    @Length(max = 50, message = "所属版块 长度不能超过50")
    @Column(name = "section")
    private String section;
    /**
     * 资源类型
     * 1：菜单
     * 2：url
     * 3：文件
     * 4:  表字段
     */
    @Column(name = "type")
    private String type;
    /**
     * 应用
     */
    @Length(max = 50, message = "应用 长度不能超过50")
    @Column(name = "app_id")
    private String appId;

  /*  @Column(name = "appNo")
    private String appNo;*/
    /**
     * 资源标识
     */
    @Length(max = 200, message = "资源标识 长度不能超过200")
    @NotNull(message = "url not allow null")
    @Column(name = "url")
    private String url;
    @Column(name = "enabled")
    private Boolean enabled;

    @Column(name = "name")
    private String name;

    @Transient
    private String userId;



/*    public String getAppNo() {
        return appNo;
    }

    public void setAppNo(String appNo) {
        this.appNo = appNo;
    }*/



    public Integer getLevelNot() {
        return levelNot;
    }

    public void setLevelNot(Integer levelNot) {
        this.levelNot = levelNot;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIsFuncResource() {
        return isFuncResource;
    }

    public void setIsFuncResource(String isFuncResource) {
        this.isFuncResource = isFuncResource;
    }


    public String getParentResourceId() {
        return parentResourceId;
    }

    public void setParentResourceId(String parentResourceId) {
        this.parentResourceId = parentResourceId;
    }


    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }


    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }


    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }


    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getFuncResource() {
        return isFuncResource;
    }

    public void setFuncResource(String funcResource) {
        isFuncResource = funcResource;
    }

}