package com.huafagroup.core.entity.auth;

import com.huafagroup.core.annotation.SysDictItem;
import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-07-19 11:54:37
 * @version
 */
@Table(name = "auth_resource_role")
public class ResourceRole extends BaseEntity implements Serializable {

	     /**
     * 角色Id
     */
    @Length(max=32,message="角色Id 长度不能超过32")
    @NotNull(message = "role_id not allow null")
    @Column(name = "role_id")
	private String roleId;
	     /**
     * 资源操作权限
     */
    @Length(max=10,message="资源操作权限 长度不能超过10")
    @Column(name = "opr_code")
	private String oprCode;
	     /**
     * 资源Id
     */
    @Length(max=32,message="资源Id 长度不能超过32")
    @NotNull(message = "resource_id not allow null")
    @Column(name = "resource_id")
	private String resourceId;



	/**
	 * 是否是功能性资源
	 * 0：数据资源
	 * 1：功能性资源
	 */
	@SysDictItem(value = "isFuncResource")
	@Column(name = "is_func_resource")
	private String isFuncResource;


	public String getIsFuncResource() {
		return isFuncResource;
	}

	public void setIsFuncResource(String isFuncResource) {
		this.isFuncResource = isFuncResource;
	}


	public String getRoleId() {
        return roleId;
    }

	public void setRoleId(String roleId) {
    	 this.roleId = roleId;
	}

        	        
	public String getOprCode() {
        return oprCode;
    }

	public void setOprCode(String oprCode) {
    	 this.oprCode = oprCode;
	}

        	        
	public String getResourceId() {
        return resourceId;
    }

	public void setResourceId(String resourceId) {
    	 this.resourceId = resourceId;
	}

        	        	        	        	        	}