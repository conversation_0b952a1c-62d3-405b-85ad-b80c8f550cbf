package com.huafagroup.core.entity.sys;
import javax.persistence.*;
import java.io.Serializable;

import org.hibernate.validator.constraints.Length;
import com.huafagroup.core.entity.BaseEntity;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-06-06 14:05:43
 * @version
 */
@Table(name = "sys_area")
public class Area extends BaseEntity implements Serializable {

	     /**
     * 编码
     */
    @Length(max=20,message="编码 长度不能超过20")
    @Column(name = "code")
	private String code;
	     /**
     * 父ID
     */
    @Column(name = "parent_id")
	private Integer parentId;
	     /**
     * 名称
     */
    @Length(max=20,message="名称 长度不能超过20")
    @Column(name = "name")
	private String name;
		     /**
     * 类型（1：省份；2：城市）
     */
    @Column(name = "type")
	private Integer type;
				
	        	        
	public String getCode() {
        return code;
    }

	public void setCode(String code) {
    	 this.code = code;
	}

        	        
	public Integer getParentId() {
        return parentId;
    }

	public void setParentId(Integer parentId) {
    	 this.parentId = parentId;
	}

        	        
	public String getName() {
        return name;
    }

	public void setName(String name) {
    	 this.name = name;
	}

        	        	        
	public Integer getType() {
        return type;
    }

	public void setType(Integer type) {
    	 this.type = type;
	}

        	        	        	        	}