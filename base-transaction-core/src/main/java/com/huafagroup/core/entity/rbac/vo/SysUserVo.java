package com.huafagroup.core.entity.rbac.vo;

import com.huafagroup.core.entity.rbac.SysMenu;
import com.huafagroup.core.entity.rbac.SysRole;
import com.huafagroup.core.entity.rbac.SysUser;

import java.util.Set;

/**
 * 前台展示用
 * Huangguoh<PERSON> 添加组织人员、物业项目信息
 *
 * <AUTHOR>
 */
public class SysUserVo extends SysUser {

    private Set<SysRole> sysRoleSet;

    private Set<SysMenu> menuSet;

    private String erpUserId;

    private String erpUserName;

    private String itemId;

    private String itemName;

    private String communityId;

    private String communityName;

    public Set<SysRole> getSysRoleSet() {
        return sysRoleSet;
    }

    public void setSysRoleSet(Set<SysRole> sysRoleSet) {
        this.sysRoleSet = sysRoleSet;
    }

    public Set<SysMenu> getMenuSet() {
        return menuSet;
    }

    public void setMenuSet(Set<SysMenu> menuSet) {
        this.menuSet = menuSet;
    }

    public String getErpUserId() {
        return erpUserId;
    }

    public void setErpUserId(String erpUserId) {
        this.erpUserId = erpUserId;
    }

    public String getErpUserName() {
        return erpUserName;
    }

    public void setErpUserName(String erpUserName) {
        this.erpUserName = erpUserName;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }
}
