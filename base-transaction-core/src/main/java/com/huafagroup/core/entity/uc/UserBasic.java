package com.huafagroup.core.entity.uc;
import javax.persistence.*;
import java.io.Serializable;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.huafagroup.core.annotation.SysDictItem;
import com.huafagroup.core.entity.auth.Role;
import org.hibernate.validator.constraints.Length;
import com.huafagroup.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-07-17 11:53:16
 * @version
 */
@Table(name = "uc_user_basic")
public class UserBasic extends BaseEntity implements Serializable {

    @Length(max=40,message=" 长度不能超过40")
    @Column(name = "salt")
	@JsonIgnore
	private String salt;
	     /**
     * 性别 1: 男  2: 女
     */
    @ApiModelProperty("性别 1: 男  2: 女")
    @Length(max=1,message="性别 1: 男  2: 女 长度不能超过1")
    @Column(name = "sex")
	@SysDictItem("sex")
	private String sex;
	     /**
     * 身份证
     */
    @ApiModelProperty("身份证")
    @Length(max=30,message="身份证 长度不能超过30")
    @NotNull(message = "id_card not allow null")
    @Column(name = "id_card")
	private String idCard;
			     /**
     * 密码
     */
    @ApiModelProperty("密码")
    @Length(max=40,message="密码 长度不能超过40")
    @NotNull(message = "password not allow null")
    @Column(name = "password")
	@JsonIgnore
	private String password;
	     /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @Length(max=50,message="姓名 长度不能超过50")
    @NotNull(message = "name not allow null")
    @Column(name = "name")
	private String name;
	     /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    @Length(max=11,message="手机号码 长度不能超过11")
    @NotNull(message = "tel not allow null")
    @Column(name = "tel")
	private String tel;
				     /**
     * 账号
     */
    @ApiModelProperty("账号")
    @Length(max=50,message="账号 长度不能超过50")
    @NotNull(message = "account not allow null")
    @Column(name = "account")
	private String account;
	     /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    @Length(max=50,message="邮箱 长度不能超过50")
    @NotNull(message = "email not allow null")
    @Column(name = "email")
	private String email;
	     /**
     * 年龄
     */
    @ApiModelProperty("年龄")
    @Column(name = "age")
	private Integer age;
	     /**
     * 用户状态 0: 禁用 1: 启用
     */
    @ApiModelProperty("用户状态 0: 禁用 1: 启用")
    @Column(name = "status")
	private Boolean status;


	@Transient
	private String userId;

	@Transient
	private Boolean isSuperAdmin;

	private List<Role> roles;


	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public Boolean getSuperAdmin() {
		return isSuperAdmin;
	}

	public void setSuperAdmin(Boolean superAdmin) {
		isSuperAdmin = superAdmin;
	}

	public List<Role> getRoles() {
		return roles;
	}

	public void setRoles(List<Role> roles) {
		this.roles = roles;
	}

	public String getSalt() {
        return salt;
    }

	public void setSalt(String salt) {
    	 this.salt = salt;
	}

        	        
	public String getSex() {
        return sex;
    }

	public void setSex(String sex) {
    	 this.sex = sex;
	}

        	        
	public String getIdCard() {
        return idCard;
    }

	public void setIdCard(String idCard) {
    	 this.idCard = idCard;
	}

        	        	        	        
	public String getPassword() {
        return password;
    }

	public void setPassword(String password) {
    	 this.password = password;
	}

        	        
	public String getName() {
        return name;
    }

	public void setName(String name) {
    	 this.name = name;
	}

        	        
	public String getTel() {
        return tel;
    }

	public void setTel(String tel) {
    	 this.tel = tel;
	}

        	        	        	        	        
	public String getAccount() {
        return account;
    }

	public void setAccount(String account) {
    	 this.account = account;
	}

        	        
	public String getEmail() {
        return email;
    }

	public void setEmail(String email) {
    	 this.email = email;
	}

        	        
	public Integer getAge() {
        return age;
    }

	public void setAge(Integer age) {
    	 this.age = age;
	}

        	        
	public Boolean getStatus() {
        return status;
    }

	public void setStatus(Boolean status) {
    	 this.status = status;
	}

   }