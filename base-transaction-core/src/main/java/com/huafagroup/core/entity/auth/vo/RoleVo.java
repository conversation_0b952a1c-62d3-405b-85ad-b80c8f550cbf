package com.huafagroup.core.entity.auth.vo;

import com.huafagroup.core.annotation.SysDictItem;
import com.huafagroup.core.entity.auth.Role;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Set;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-19 11:35:53
 */
public class RoleVo extends Role implements Serializable {

    /**
     * 所属版块(中文)
     */
    @SysDictItem(value = "section")
    @Column(name = "sectionText")
    private String sectionText;

    private String orgId;

    private String appNo;


    @Transient
    private String mode;


    @Transient
    private String userId;


    @Transient
    private Boolean pageable;

    @Transient
    private Integer pageIndex;

    @Transient
    private Integer pageSize;


    @Transient
    private Boolean isSuperAdmin;

    @Transient
    private Set<String> idSet;

    @Column(name = "parentDefineId")
    private String parentDefineId;


    public Boolean getSuperAdmin() {
        return isSuperAdmin;
    }

    public void setSuperAdmin(Boolean superAdmin) {
        isSuperAdmin = superAdmin;
    }

    public String getParentDefineId() {
        return parentDefineId;
    }

    public void setParentDefineId(String parentDefineId) {
        this.parentDefineId = parentDefineId;
    }

    public Set<String> getIdSet() {
        return idSet;
    }

    public void setIdSet(Set<String> idSet) {
        this.idSet = idSet;
    }


    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


    public Boolean getPageable() {
        return pageable;
    }

    public void setPageable(Boolean pageable) {
        this.pageable = pageable;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }


    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }


    public String getSectionText() {
        return sectionText;
    }

    public void setSectionText(String sectionText) {
        this.sectionText = sectionText;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getAppNo() {
        return appNo;
    }

    public void setAppNo(String appNo) {
        this.appNo = appNo;
    }
}