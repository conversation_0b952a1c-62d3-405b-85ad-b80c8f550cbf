package com.huafagroup.core.entity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 * 
 *${remark}
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2018-12-08 11:02:49
 * @version
 */
@Table(name = "sys_log")
public class SysTLog implements Serializable {

     /**
     * 地区
     */


    @Length(max=255,message="area 长度不能超过255")
    @Column(name = "area")
	private String area;
	
     /**
     * 异常信息
     */


    @Length(max=65535,message="exception 长度不能超过65535")
    @Column(name = "exception")
	private String exception;
	
     /**
     * 操作IP地址
     */


    @Length(max=255,message="remote_addr 长度不能超过255")
    @Column(name = "remote_addr")
	private String remoteAddr;
	


    @Column(name = "del_flag")
	private Boolean delFlag;
	
     /**
     * 方法执行时间
     */


    @Column(name = "use_time")
	private Long useTime;
	
     /**
     * 市
     */


    @Length(max=255,message="city 长度不能超过255")
    @Column(name = "city")
	private String city;
	
     /**
     * 网络服务提供商
     */


    @Length(max=255,message="isp 长度不能超过255")
    @Column(name = "isp")
	private String isp;
	
     /**
     * sessionId
     */


    @Length(max=255,message="session_id 长度不能超过255")
    @Column(name = "session_id")
	private String sessionId;
	
     /**
     * 请求类型
     */


    @Length(max=20,message="type 长度不能超过20")
    @Column(name = "type")
	private String type;
	
     /**
     * 日志标题
     */


    @Length(max=255,message="title 长度不能超过255")
    @Column(name = "title")
	private String title;
	
     /**
     * 操作提交的数据
     */


    @Length(max=65535,message="params 长度不能超过65535")
    @Column(name = "params")
	private String params;
	
     /**
     * 请求URI
     */


    @Length(max=255,message="request_uri 长度不能超过255")
    @Column(name = "request_uri")
	private String requestUri;
	


    @Column(name = "update_date")
	private Date updateDate;
	
     /**
     * 创建者
     */


    @Length(max=64,message="create_by 长度不能超过64")
    @Column(name = "create_by")
	private String createBy;
	
     /**
     * 操作方式
     */


    @Length(max=10,message="http_method 长度不能超过10")
    @Column(name = "http_method")
	private String httpMethod;
	
     /**
     * 省
     */


    @Length(max=255,message="province 长度不能超过255")
    @Column(name = "province")
	private String province;
	
     /**
     * 请求类型.方法
     */


    @Length(max=255,message="class_method 长度不能超过255")
    @Column(name = "class_method")
	private String classMethod;
	
     /**
     * 返回内容
     */


    @Length(max=2147483647,message="response 长度不能超过2147483647")
    @Column(name = "response")
	private String response;
	
     /**
     * 浏览器信息
     */


    @Length(max=255,message="browser 长度不能超过255")
    @Column(name = "browser")
	private String browser;
	
     /**
     * 编号
     */


    @Length(max=40,message="id 长度不能超过40")
    @Id
    @Column(name = "id")
	private String id;
	
     /**
     * 创建时间
     */


    @Column(name = "create_date")
	private Date createDate;
	


    @Column(name = "update_by")
	private Long updateBy;
	


    @Length(max=255,message="remarks 长度不能超过255")
    @Column(name = "remarks")
	private String remarks;
	
     /**
     * 操作用户昵称
     */


    @Length(max=255,message="username 长度不能超过255")
    @Column(name = "username")
	private String username;
	
		
	public String getArea() {
        return area;
    }

	public void setArea(String area) {
    	 this.area = area;
	}
		
	public String getException() {
        return exception;
    }

	public void setException(String exception) {
    	 this.exception = exception;
	}
		
	public String getRemoteAddr() {
        return remoteAddr;
    }

	public void setRemoteAddr(String remoteAddr) {
    	 this.remoteAddr = remoteAddr;
	}
		
	public Boolean getDelFlag() {
        return delFlag;
    }

	public void setDelFlag(Boolean delFlag) {
    	 this.delFlag = delFlag;
	}
		
	public Long getUseTime() {
        return useTime;
    }

	public void setUseTime(Long useTime) {
    	 this.useTime = useTime;
	}
		
	public String getCity() {
        return city;
    }

	public void setCity(String city) {
    	 this.city = city;
	}
		
	public String getIsp() {
        return isp;
    }

	public void setIsp(String isp) {
    	 this.isp = isp;
	}
		
	public String getSessionId() {
        return sessionId;
    }

	public void setSessionId(String sessionId) {
    	 this.sessionId = sessionId;
	}
		
	public String getType() {
        return type;
    }

	public void setType(String type) {
    	 this.type = type;
	}
		
	public String getTitle() {
        return title;
    }

	public void setTitle(String title) {
    	 this.title = title;
	}
		
	public String getParams() {
        return params;
    }

	public void setParams(String params) {
    	 this.params = params;
	}
		
	public String getRequestUri() {
        return requestUri;
    }

	public void setRequestUri(String requestUri) {
    	 this.requestUri = requestUri;
	}
		
	public Date getUpdateDate() {
        return updateDate;
    }

	public void setUpdateDate(Date updateDate) {
    	 this.updateDate = updateDate;
	}
		
	public String getCreateBy() {
        return createBy;
    }

	public void setCreateBy(String createBy) {
    	 this.createBy = createBy;
	}
		
	public String getHttpMethod() {
        return httpMethod;
    }

	public void setHttpMethod(String httpMethod) {
    	 this.httpMethod = httpMethod;
	}
		
	public String getProvince() {
        return province;
    }

	public void setProvince(String province) {
    	 this.province = province;
	}
		
	public String getClassMethod() {
        return classMethod;
    }

	public void setClassMethod(String classMethod) {
    	 this.classMethod = classMethod;
	}
		
	public String getResponse() {
        return response;
    }

	public void setResponse(String response) {
    	 this.response = response;
	}
		
	public String getBrowser() {
        return browser;
    }

	public void setBrowser(String browser) {
    	 this.browser = browser;
	}
		
	public String getId() {
        return id;
    }

	public void setId(String id) {
    	 this.id = id;
	}
		
	public Date getCreateDate() {
        return createDate;
    }

	public void setCreateDate(Date createDate) {
    	 this.createDate = createDate;
	}
		
	public Long getUpdateBy() {
        return updateBy;
    }

	public void setUpdateBy(Long updateBy) {
    	 this.updateBy = updateBy;
	}
		
	public String getRemarks() {
        return remarks;
    }

	public void setRemarks(String remarks) {
    	 this.remarks = remarks;
	}
		
	public String getUsername() {
        return username;
    }

	public void setUsername(String username) {
    	 this.username = username;
	}
	}