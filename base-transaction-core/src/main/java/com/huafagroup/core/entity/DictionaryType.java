package com.huafagroup.core.entity;

import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * ${remark}
 * </p>
 *
 * <AUTHOR>
 * @date 2018-12-20 15:28:37
 */
@Table(name = "dictionary_type")
public class DictionaryType extends IntegerBaseEntity implements Serializable {
    /**
     * 编码
     */
    @Length(max = 255, message = "CODE 长度不能超过255")
    @Column(name = "code")
    private String               code;
    /**
     * 状态
     */
    @Column(name = "status")
    private Integer              status;
    /**
     * 字典类型名称
     */
    @Length(max = 255, message = "text 长度不能超过255")
    @Column(name = "text")
    private String               text;
    private List<DictionaryItem> dictitemList;

    public List<DictionaryItem> getDictitemList() {
        return dictitemList;
    }

    public void setDictitemList(List<DictionaryItem> dictitemList) {
        this.dictitemList = dictitemList;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}