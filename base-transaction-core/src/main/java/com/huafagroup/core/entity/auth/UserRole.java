package com.huafagroup.core.entity.auth;

import com.huafagroup.core.entity.BaseEntity;
import com.huafagroup.core.entity.uc.UserBasic;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-07-19 14:06:11
 * @version
 */
@Table(name = "auth_user_role")
public class UserRole extends BaseEntity implements Serializable {

	     /**
     * 用户Id
     */
    @Length(max=32,message="用户Id 长度不能超过32")
    @NotNull(message = "user_id not allow null")
    @Column(name = "user_id")
	private String userId;
	     /**
     * 角色Id
     */
    @Length(max=32,message="角色Id 长度不能超过32")
    @NotNull(message = "role_id not allow null")
    @Column(name = "role_id")
	private String roleId;


	@Transient
	private String pageType;

	@Transient
	private ArrayList<UserBasic> userList;

	@Transient
	private ArrayList<Role> roleList;

	public String getPageType() {
		return pageType;
	}

	public void setPageType(String pageType) {
		this.pageType = pageType;
	}


	public ArrayList<UserBasic> getUserList() {
		return userList;
	}

	public void setUserList(ArrayList<UserBasic> userList) {
		this.userList = userList;
	}

	public ArrayList<Role> getRoleList() {
		return roleList;
	}

	public void setRoleList(ArrayList<Role> roleList) {
		this.roleList = roleList;
	}
					
	        	        
	public String getUserId() {
        return userId;
    }

	public void setUserId(String userId) {
    	 this.userId = userId;
	}

        	        
	public String getRoleId() {
        return roleId;
    }

	public void setRoleId(String roleId) {
    	 this.roleId = roleId;
	}

        	        	        	        	        	}