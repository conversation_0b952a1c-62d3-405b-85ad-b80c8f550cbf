package com.huafagroup.core.entity.rbac;
import javax.persistence.*;
import java.io.Serializable;

import org.hibernate.validator.constraints.Length;
import com.huafagroup.core.entity.BaseEntity;

import java.util.List;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-06-03 11:42:28
 * @version
 */
@Table(name = "sys_user")
public class SysUser extends BaseEntity implements Serializable {

     /**
     * 盐值
     */
    @Length(max=40,message="盐值 长度不能超过40")
    @Column(name = "salt")
	private String salt;
	     /**
     * 1：男；2：女
     */
    @Column(name = "gender")
	private Integer gender;
	     /**
     * 公司ID
     */
    @Length(max=50,message="公司ID 长度不能超过50")
    @Column(name = "company_id")
	private String companyId;
	     /**
     * 岗位名称
     */
    @Length(max=50,message="岗位名称 长度不能超过50")
    @Column(name = "position_name")
	private String positionName;
	     /**
     * 是否启用
     */
    @Column(name = "enabled")
	private Integer enabled;
		     /**
     * 部门编号
     */
    @Length(max=50,message="部门编号 长度不能超过50")
    @Column(name = "department_number")
	private String departmentNumber;
		     /**
     * 用户密码
     */
    @Length(max=40,message="用户密码 长度不能超过40")
    @Column(name = "password")
	private String password;
	     /**
     * 登陆账号
     */
    @Length(max=30,message="登陆账号 长度不能超过30")
    @Column(name = "login_name")
	private String loginName;
	     /**
     * 职务名称
     */
    @Length(max=50,message="职务名称 长度不能超过50")
    @Column(name = "duty_name")
	private String dutyName;
	     /**
     * 手机号码
     */
    @Length(max=11,message="手机号码 长度不能超过11")
    @Column(name = "phone")
	private String phone;
	     /**
     * 职务ID
     */
    @Length(max=50,message="职务ID 长度不能超过50")
    @Column(name = "duty_id")
	private String dutyId;
	     /**
     * 公司名称
     */
    @Length(max=50,message="公司名称 长度不能超过50")
    @Column(name = "company_name")
	private String companyName;
	     /**
     * 用户名称
     */
    @Length(max=30,message="用户名称 长度不能超过30")
    @Column(name = "name")
	private String name;
	     /**
     * HR编号
     */
    @Length(max=50,message="HR编号 长度不能超过50")
    @Column(name = "employee_number")
	private String employeeNumber;
		     /**
     * 是否被锁
     */
    @Column(name = "locked")
	private Integer locked;
			     /**
     * 电子邮箱
     */
    @Length(max=30,message="电子邮箱 长度不能超过30")
    @Column(name = "email")
	private String email;
	     /**
     * 岗位ID
     */
    @Length(max=50,message="岗位ID 长度不能超过50")
    @Column(name = "position_id")
	private String positionId;

	/**
	 * 岗位ID
	 */
	@Length(max=20,message="身份证号 长度不能超过20")
	@Column(name = "id_number")
	private String idNumber;


	public String getIdNumber() {
		return idNumber;
	}

	public void setIdNumber(String idNumber) {
		this.idNumber = idNumber;
	}

	public String getSalt() {
        return salt;
    }

	private List<SysRole> roles;

	private List<SysMenu> menus;

        	        
	public Integer getGender() {
        return gender;
    }

	public void setGender(Integer gender) {
    	 this.gender = gender;
	}

        	        
	public String getCompanyId() {
        return companyId;
    }

	public void setCompanyId(String companyId) {
    	 this.companyId = companyId;
	}

        	        
	public String getPositionName() {
        return positionName;
    }

	public void setPositionName(String positionName) {
    	 this.positionName = positionName;
	}

        	        
	public Integer getEnabled() {
        return enabled;
    }

	public void setEnabled(Integer enabled) {
    	 this.enabled = enabled;
	}

        	        	        
	public String getDepartmentNumber() {
        return departmentNumber;
    }

	public void setDepartmentNumber(String departmentNumber) {
    	 this.departmentNumber = departmentNumber;
	}

        	        	        
	public String getPassword() {
        return password;
    }

	public void setPassword(String password) {
    	 this.password = password;
	}

        	        
	public String getLoginName() {
        return loginName;
    }

	public void setLoginName(String loginName) {
    	 this.loginName = loginName;
	}

        	        
	public String getDutyName() {
        return dutyName;
    }

	public void setDutyName(String dutyName) {
    	 this.dutyName = dutyName;
	}

        	        
	public String getPhone() {
        return phone;
    }

	public void setPhone(String phone) {
    	 this.phone = phone;
	}

        	        
	public String getDutyId() {
        return dutyId;
    }

	public void setDutyId(String dutyId) {
    	 this.dutyId = dutyId;
	}

        	        
	public String getCompanyName() {
        return companyName;
    }

	public void setCompanyName(String companyName) {
    	 this.companyName = companyName;
	}

        	        
	public String getName() {
        return name;
    }

	public void setName(String name) {
    	 this.name = name;
	}

        	        
	public String getEmployeeNumber() {
        return employeeNumber;
    }

	public void setEmployeeNumber(String employeeNumber) {
    	 this.employeeNumber = employeeNumber;
	}

        	        	        
	public Integer getLocked() {
        return locked;
    }

	public void setLocked(Integer locked) {
    	 this.locked = locked;
	}

        	        	        	        
	public String getEmail() {
        return email;
    }

	public void setEmail(String email) {
    	 this.email = email;
	}

        	        
	public String getPositionId() {
        return positionId;
    }

	public void setPositionId(String positionId) {
    	 this.positionId = positionId;
	}

	public List<SysMenu> getMenus() {
		return menus;
	}

	public void setMenus(List<SysMenu> menus) {
		this.menus = menus;
	}

	public List<SysRole> getRoles() {
		return roles;
	}

	public void setRoles(List<SysRole> roles) {
		this.roles = roles;
	}

	public void setSalt(String salt) {
		this.salt = salt;
	}
}