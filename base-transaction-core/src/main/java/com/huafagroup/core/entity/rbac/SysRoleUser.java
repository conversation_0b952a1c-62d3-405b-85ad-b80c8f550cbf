package com.huafagroup.core.entity.rbac;

import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019-02-14 09:18:07
 * @version
 */
@Table(name = "sys_role_user")
public class SysRoleUser implements Serializable {

    @Length(max=32,message=" 长度不能超过32")
    @Column(name = "user_id")
	private String userId;

	    @Length(max=32,message=" 长度不能超过32")
    @Column(name = "role_id")
	private String roleId;


	public String getUserId() {
        return userId;
    }

	public void setUserId(String userId) {
    	 this.userId = userId;
	}

	public String getRoleId() {
        return roleId;
    }

	public void setRoleId(String roleId) {
    	 this.roleId = roleId;
	}

        	}