package com.huafagroup.core.entity.auth;

import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-07-19 14:18:35
 * @version
 */
@Table(name = "auth_role_department")
public class RoleDepartment extends BaseEntity implements Serializable {

	     /**
     * 角色Id
     */
    @Length(max=32,message="角色Id 长度不能超过32")
    @NotNull(message = "role_id not allow null")
    @Column(name = "role_id")
	private String roleId;
	     /**
     * 部门Id
     */
    @Length(max=32,message="部门Id 长度不能超过32")
    @NotNull(message = "dept_id not allow null")
    @Column(name = "dept_id")
	private String deptId;
				
	        	        
	public String getRoleId() {
        return roleId;
    }

	public void setRoleId(String roleId) {
    	 this.roleId = roleId;
	}

        	        
	public String getDeptId() {
        return deptId;
    }

	public void setDeptId(String deptId) {
    	 this.deptId = deptId;
	}

        	        	        	        	}