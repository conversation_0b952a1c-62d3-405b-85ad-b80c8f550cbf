package com.huafagroup.core.entity.rbac;
import javax.persistence.*;
import java.io.Serializable;

import org.hibernate.validator.constraints.Length;

import java.util.Date;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-04-10 14:29:35
 * @version
 */
@Table(name = "access_log")
public class AccessLog implements Serializable {

     /**
     * 访问时间
     */
    @Column(name = "access_date")
	private Date accessDate;
	     /**
     * 项目id
     */
    @Length(max=32,message="项目id 长度不能超过32")
    @Column(name = "program_id")
	private String programId;
		     /**
     * 供应商id
     */
    @Length(max=32,message="供应商id 长度不能超过32")
    @Column(name = "supplier_id")
	private String supplierId;

	@Length(max=32,message="关联ID 长度不能超过32")
	@Column(name = "id")
	private String id;

	public Date getAccessDate() {
        return accessDate;
    }

	public void setAccessDate(Date accessDate) {
    	 this.accessDate = accessDate;
	}

        	        
	public String getProgramId() {
        return programId;
    }

	public void setProgramId(String programId) {
    	 this.programId = programId;
	}

        	        	        
	public String getSupplierId() {
        return supplierId;
    }

	public void setSupplierId(String supplierId) {
    	 this.supplierId = supplierId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}
}