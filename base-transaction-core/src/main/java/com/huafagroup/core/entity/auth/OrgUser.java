package com.huafagroup.core.entity.auth;

import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-09-09 09:29:05
 * @version
 */
@Table(name = "auth_org_user")
public class OrgUser extends BaseEntity implements Serializable {

	     /**
     * 用户Id
     */
    @Length(max=36,message="用户Id 长度不能超过36")
    @NotNull(message = "user_id not allow null")
    @Column(name = "user_id")
	private String userId;
	     /**
     * 组织Id
     */
    @Length(max=32,message="组织Id 长度不能超过32")
    @NotNull(message = "org_id not allow null")
    @Column(name = "org_id")
	private String orgId;
					
	        	        
	public String getUserId() {
        return userId;
    }

	public void setUserId(String userId) {
    	 this.userId = userId;
	}

        	        
	public String getOrgId() {
        return orgId;
    }

	public void setOrgId(String orgId) {
    	 this.orgId = orgId;
	}

        	        	        	        	        	}