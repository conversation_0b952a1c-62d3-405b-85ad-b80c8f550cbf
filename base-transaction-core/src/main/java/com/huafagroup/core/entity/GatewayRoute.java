package com.huafagroup.core.entity;

import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 * 
 *${remark}
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2018-10-09 10:13:41
 * @version
 */
@Table(name = "gateway_route")
public class GatewayRoute implements Serializable {



    @Column(name = "retryable")
	private Boolean retryable;
	


    @Length(max=255,message="path 长度不能超过255")
    @NotNull(message = "path not allow null")
    @Column(name = "path")
	private String path;
	


    @Column(name = "strip_prefix")
	private Integer stripPrefix;
	


    @Length(max=255,message="api_name 长度不能超过255")
    @Column(name = "api_name")
	private String apiName;
	


    @Length(max=50,message="service_id 长度不能超过50")
    @Column(name = "service_id")
	private String serviceId;
	


    @Length(max=50,message="id 长度不能超过50")
    @NotNull(message = "id not allow null")
    @Id
    @Column(name = "id")
	private String id;
	


    @Length(max=255,message="url 长度不能超过255")
    @Column(name = "url")
	private String url;
	


    @NotNull(message = "enabled not allow null")
    @Column(name = "enabled")
	private Boolean enabled;
	
		
	public Boolean getRetryable() {
        return retryable;
    }

	public void setRetryable(Boolean retryable) {
    	 this.retryable = retryable;
	}
		
	public String getPath() {
        return path;
    }

	public void setPath(String path) {
    	 this.path = path;
	}
		
	public Integer getStripPrefix() {
        return stripPrefix;
    }

	public void setStripPrefix(Integer stripPrefix) {
    	 this.stripPrefix = stripPrefix;
	}
		
	public String getApiName() {
        return apiName;
    }

	public void setApiName(String apiName) {
    	 this.apiName = apiName;
	}
		
	public String getServiceId() {
        return serviceId;
    }

	public void setServiceId(String serviceId) {
    	 this.serviceId = serviceId;
	}
		
	public String getId() {
        return id;
    }

	public void setId(String id) {
    	 this.id = id;
	}
		
	public String getUrl() {
        return url;
    }

	public void setUrl(String url) {
    	 this.url = url;
	}
		
	public Boolean getEnabled() {
        return enabled;
    }

	public void setEnabled(Boolean enabled) {
    	 this.enabled = enabled;
	}
	}