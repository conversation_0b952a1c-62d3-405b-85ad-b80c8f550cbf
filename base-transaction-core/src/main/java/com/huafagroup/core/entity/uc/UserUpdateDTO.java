package com.huafagroup.core.entity.uc;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2019-07-17 08:43:16
 */
public class UserUpdateDTO implements Serializable {

    @ApiModelProperty("ID")
    @NotNull(message = "id not allow null")
    private String id;
    /**
     * 性别 1: 男  2: 女
     */
    @ApiModelProperty("性别 1: 男  2: 女")
    @Length(max=1,message="性别 1: 男  2: 女 长度不能超过1")
    private String sex;
    /**
     * 身份证
     */
    @ApiModelProperty("身份证")
    @Length(max=30,message="身份证 长度不能超过30")
    private String idCard;
    /**
     * 密码
     */
    @ApiModelProperty("密码")
    @Length(max=40,message="密码 长度不能超过40")
    private String password;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @Length(max=50,message="姓名 长度不能超过50")
    private String name;
    /**
     * 邮箱
     */
    @ApiModelProperty("邮箱")
    @Length(max=50,message="邮箱 长度不能超过50")
    private String email;
    /**
     * 年龄
     */
    @ApiModelProperty("年龄")
    private Integer age;
    /**
     * 用户状态 0: 禁用 1: 启用
     */
    @ApiModelProperty("用户状态 0: 禁用 1: 启用")
    private Boolean status;

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }


    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }


    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }


    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }


    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}