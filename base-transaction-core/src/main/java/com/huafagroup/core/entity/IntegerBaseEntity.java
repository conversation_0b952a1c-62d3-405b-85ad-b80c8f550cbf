package com.huafagroup.core.entity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description 数据库实体基类
 * @Date 2018/12/24 11:26
 **/
public class IntegerBaseEntity implements Serializable {
    public static final String  FIELD_ID          = "id";
    public static final String  FIELD_CREATE_DATE = "createDate";
    public static final String  FIELD_UPDATE_DATE = "updateDate";
    public static final String  FIELD_CREATE_BY   = "createBy";
    public static final String  FIELD_UPDATE_BY   = "updateBy";
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private             Integer id;
    @Column(name = "create_date")
    private             Date    createDate;
    @Column(name = "update_date")
    private             Date    updateDate;
    @Length(max = 40, message = "create_by 长度不能超过40")
    @Column(name = "create_by")
    private             String  createBy;
    @Length(max = 40, message = "update_by 长度不能超过40")
    @Column(name = "update_by")
    private             String  updateBy;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
}
