package com.huafagroup.core.entity.trans;

import com.huafagroup.core.entity.IntegerBaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-16 16:49:29
 */
@Table(name = "trans_merchant")
public class Merchant extends IntegerBaseEntity implements Serializable {

    /**
     * Api V3秘钥
     */
    @Length(max = 50, message = "Api V3秘钥 长度不能超过50")
    @Column(name = "apiv3_key")
    private String apiV3Key;
    /**
     * Api 秘钥
     */
    @Length(max = 64, message = "Api秘钥 长度不能超过64")
    @Column(name = "api_key")
    private String apiKey;
    /**
     * 支持付款标识：1：支持，0：不支持
     */
    @NotNull(message = "pay_flag not allow null")
    @Column(name = "pay_flag")
    private Integer payFlag;
    /**
     * 0-默认，1-删除
     */
    @NotNull(message = "del_flag not allow null")
    @Column(name = "del_flag")
    private Integer delFlag = 0;
    /**
     * 支付证书url
     */
    @Length(max = 500, message = "支付证书url 长度不能超过500")
    @Column(name = "certificate_url")
    private String certificateUrl;
    /**
     * 商户证书序列号
     */
    @Length(max = 500, message = "商户证书序列号 长度不能超过500")
    @Column(name = "mch_serial_no")
    private String mchSerialNo;
    /**
     * 商户私钥
     */
    @Length(max = 500, message = "商户私钥 长度不能超过500")
    @Column(name = "private_key")
    private String privateKey;
    /**
     * 微信商户号
     */
    @Length(max = 32, message = "微信商户号 长度不能超过32")
    @Column(name = "mch_id")
    private String mchId;
    //微信支付公钥路径
    private String wechatPayPublicKeyUrl;
    //微信支付公钥
    private String wechatPayPublicKey;
    //微信支付公钥ID
    private String wechatPayPublicKeyId;
    /**
     * 微信商户名称
     */
    @Length(max = 64, message = "微信商户名称 长度不能超过64")
    @Column(name = "mch_name")
    private String mchName;
    private String merchantSubject;
    @NotBlank
    private String managerName;
    @NotBlank
    private String managerPhone;

    public String getApiV3Key() {
        return apiV3Key;
    }

    public void setApiV3Key(String apiV3Key) {
        this.apiV3Key = apiV3Key;
    }


    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }


    public String getCertificateUrl() {
        return certificateUrl;
    }

    public void setCertificateUrl(String certificateUrl) {
        this.certificateUrl = certificateUrl;
    }


    public String getMchSerialNo() {
        return mchSerialNo;
    }

    public void setMchSerialNo(String mchSerialNo) {
        this.mchSerialNo = mchSerialNo;
    }


    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }


    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getMchName() {
        return mchName;
    }

    public void setMchName(String mchName) {
        this.mchName = mchName;
    }

    public String getMerchantSubject() {
        return merchantSubject;
    }

    public void setMerchantSubject(String merchantSubject) {
        this.merchantSubject = merchantSubject;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getManagerPhone() {
        return managerPhone;
    }

    public void setManagerPhone(String managerPhone) {
        this.managerPhone = managerPhone;
    }

    public Integer getPayFlag() {
        return payFlag;
    }

    public void setPayFlag(Integer payFlag) {
        this.payFlag = payFlag;
    }

    public String getWechatPayPublicKey() {
        return wechatPayPublicKey;
    }

    public void setWechatPayPublicKey(String wechatPayPublicKey) {
        this.wechatPayPublicKey = wechatPayPublicKey;
    }

    public String getWechatPayPublicKeyId() {
        return wechatPayPublicKeyId;
    }

    public void setWechatPayPublicKeyId(String wechatPayPublicKeyId) {
        this.wechatPayPublicKeyId = wechatPayPublicKeyId;
    }
    public String getWechatPayPublicKeyUrl() {
        return wechatPayPublicKeyUrl;
    }

    public void setWechatPayPublicKeyUrl(String wechatPayPublicKeyUrl) {
        this.wechatPayPublicKeyUrl = wechatPayPublicKeyUrl;
    }
}