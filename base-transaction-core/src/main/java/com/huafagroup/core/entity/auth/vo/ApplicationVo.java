package com.huafagroup.core.entity.auth.vo;

import com.huafagroup.core.annotation.SysDictItem;
import com.huafagroup.core.entity.auth.Application;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-07-19 10:34:11
 * @version
 */

public class ApplicationVo extends Application implements Serializable {


    @Column(name = "sectionText")
    @SysDictItem("section")
    private String sectionText;

    @Transient
    private String appid;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }


    public String getSectionText() {
        return sectionText;
    }

    public void setSectionText(String sectionText) {
        this.sectionText = sectionText;
    }

}