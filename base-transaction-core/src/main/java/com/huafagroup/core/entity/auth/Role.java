package com.huafagroup.core.entity.auth;

import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-07-19 11:35:53
 * @version
 */
@Table(name = "auth_role")
public class Role extends BaseEntity implements Serializable {

	     /**
     * 角色类型
            1: 授权角色
            2: 业务角色
     */

    @Column(name = "role_type")
	private String roleType;
	     /**
     * 角色名称
     */
    @Length(max=50,message="角色名称 长度不能超过50")
    @NotNull(message = "name not allow null")
    @Column(name = "name")
	private String name;
	     /**
     * 所属版块
     */
    @Length(max=50,message="所属版块 长度不能超过50")
    @Column(name = "section")
	private String section;
	     /**
     * 父角色Id
     */
    @Length(max=32,message="父角色Id 长度不能超过32")
    @Column(name = "parent_role_id")
	private String parentRoleId;
				     /**
     * 所属系统/应用
     */
    @Length(max=32,message="所属系统/应用 长度不能超过32")
    @Column(name = "app_id")
	private String appId;
	    @Column(name = "enabled")
	private Boolean enabled;

	@Length(max=1000,message="角色路径 长度不能超过1000")
	@Column(name = "role_path")
	private String rolePath;

	@Transient
	private String roleId;
	@Transient
	private String subRoleId;
	@Transient
	private String appName;

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}


	public String getRoleType() {
        return roleType;
    }

	public void setRoleType(String roleType) {
    	 this.roleType = roleType;
	}

        	        
	public String getName() {
        return name;
    }

	public void setName(String name) {
    	 this.name = name;
	}

        	        
	public String getSection() {
        return section;
    }

	public void setSection(String section) {
    	 this.section = section;
	}

        	        
	public String getParentRoleId() {
        return parentRoleId;
    }

	public void setParentRoleId(String parentRoleId) {
    	 this.parentRoleId = parentRoleId;
	}

        	        	        	        	        
	public String getAppId() {
        return appId;
    }

	public void setAppId(String appId) {
    	 this.appId = appId;
	}

        	        
	public Boolean getEnabled() {
        return enabled;
    }

	public void setEnabled(Boolean enabled) {
    	 this.enabled = enabled;
	}

	public String getSubRoleId() {
		return subRoleId;
	}

	public void setSubRoleId(String subRoleId) {
		this.subRoleId = subRoleId;
	}

	public String getRolePath() {
		return rolePath;
	}

	public void setRolePath(String rolePath) {
		this.rolePath = rolePath;
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}
}