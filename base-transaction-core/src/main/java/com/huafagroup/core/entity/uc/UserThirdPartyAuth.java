package com.huafagroup.core.entity.uc;
import javax.persistence.*;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;
import com.huafagroup.core.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-07-24 11:36:23
 * @version
 */
@Table(name = "uc_user_third_party_auth")
public class UserThirdPartyAuth extends BaseEntity implements Serializable {

	     /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    @Length(max=32,message="用户Id 长度不能超过32")
    @NotNull(message = "user_id not allow null")
    @Column(name = "user_id")
	private String userId;
	    @Length(max=50,message=" 长度不能超过50")
    @NotNull(message = "open_id not allow null")
    @Column(name = "open_id")
	private String openId;
	     /**
     * 第三方认证类型  1: 微信认证
     */
    @ApiModelProperty("第三方认证类型  1: 微信认证")
    @NotNull(message = "third_party_type not allow null")
    @Column(name = "third_party_type")
	private Integer thirdPartyType;
	    @Length(max=50,message=" 长度不能超过50")
    @NotNull(message = "union_id not allow null")
    @Column(name = "union_id")
	private String unionId;
					     /**
     * 绑定应用编号
     */
    @ApiModelProperty("绑定应用编号")
    @Length(max=20,message="绑定应用编号 长度不能超过20")
    @NotNull(message = "app_code not allow null")
    @Column(name = "app_code")
	private String appCode;
	
	        	        
	public String getUserId() {
        return userId;
    }

	public void setUserId(String userId) {
    	 this.userId = userId;
	}

        	        
	public String getOpenId() {
        return openId;
    }

	public void setOpenId(String openId) {
    	 this.openId = openId;
	}

        	        
	public Integer getThirdPartyType() {
        return thirdPartyType;
    }

	public void setThirdPartyType(Integer thirdPartyType) {
    	 this.thirdPartyType = thirdPartyType;
	}

        	        
	public String getUnionId() {
        return unionId;
    }

	public void setUnionId(String unionId) {
    	 this.unionId = unionId;
	}

        	        	        	        	        	        
	public String getAppCode() {
        return appCode;
    }

	public void setAppCode(String appCode) {
    	 this.appCode = appCode;
	}

        	}