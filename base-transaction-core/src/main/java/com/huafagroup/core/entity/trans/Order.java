package com.huafagroup.core.entity.trans;

import com.huafagroup.core.entity.IntegerBaseEntity;
import com.huafagroup.core.enums.OrderStatusEnum;
import com.huafagroup.core.enums.PayProviderEnum;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-12 19:32:58
 */
@Table(name = "trans_order")
public class Order extends IntegerBaseEntity implements Serializable {

    /**
     * 订单号，UUID生成
     */
    @Length(max = 64, message = "订单号，UUID生成 长度不能超过64")
    @NotBlank(message = "order_no not allow null")
    @Column(name = "order_no")
    private String orderNo;
    /**
     * 业务系统通知，状态NOTNOTIFY：未通知SUCCESS：通知成功
     */
    @Length(max = 64, message = "业务系统通知，状态NOTNOTIFY：未通知SUCCESS：通知成功 长度不能超过64")
    @Column(name = "biz_notify_status")
    private String bizNotifyStatus;
    /**
     * 退款成功时间
     */
    @Column(name = "refund_success_time")
    private Date refundSuccessTime;
    /**
     * 订单描述
     */
    @Length(max = 128, message = "订单描述 长度不能超过128")
    @Column(name = "`desc`")
    private String desc;
    /**
     * 直连商户的商户号
     */
    @Length(max = 64, message = "直连商户的商户号 长度不能超过64")
    @Column(name = "mch_id")
    private String mchId;
    /**
     * 通知的唯一ID
     */
    @Length(max = 64, message = "通知的唯一ID 长度不能超过64")
    @Column(name = "notify_id")
    private String notifyId;
    /**
     * 预支付交易会话标识
     */
    @Length(max = 64, message = "预支付交易会话标识 长度不能超过64")
    @Column(name = "prepay_id")
    private String prepayId;
    /**
     * 用户支付金额，单位分
     */
    @Column(name = "payer_total")
    private Integer payerTotal;
    /**
     * 退款通知摘要
     */
    @Length(max = 128, message = "退款通知摘要 长度不能超过128")
    @Column(name = "refund_notify_summary")
    private String refundNotifySummary;
    /**
     * 退款渠道，ORIGINAL：原路退款
     */
    @Length(max = 64, message = "退款渠道，ORIGINAL：原路退款 长度不能超过64")
    @Column(name = "refund_channel")
    private String refundChannel;
    /**
     * 项目ID
     */
    @Length(max = 64, message = "项目ID 长度不能超过64")
    @NotBlank(message = "project_id not allow null")
    @Column(name = "project_id")
    private String projectId;
    /**
     * 退款通知时间
     */
    @Column(name = "refund_notify_time")
    private Date refundNotifyTime;
    /**
     * 退款业务系统通知状态，NOTNOTIFY：未通知SUCCESS：通知成功
     */
    @Length(max = 64, message = "退款业务系统通知状态，NOTNOTIFY：未通知SUCCESS：通知成功 长度不能超过64")
    @Column(name = "refund_biz_notify_status")
    private String refundBizNotifyStatus;
    /**
     * 退款原因
     */
    @Length(max = 64, message = "退款原因  长度不能超过64")
    @Column(name = "refund_reason")
    private String refundReason;
    /**
     * 退款通知的唯一ID
     */
    @Length(max = 64, message = "退款通知的唯一ID 长度不能超过64")
    @Column(name = "refund_notify_id")
    private String refundNotifyId;
    /**
     * 直连商户申请的公众号或移动应用appid
     */
    @Length(max = 64, message = "直连商户申请的公众号或移动应用appid 长度不能超过64")
    @NotBlank(message = "app_id not allow null")
    @Column(name = "app_id")
    private String appId;
    /**
     * 微信支付订单号
     */
    @Length(max = 64, message = "微信支付订单号 长度不能超过64")
    @Column(name = "transaction_id")
    private String transactionId;
    /**
     * 退款业务系统回调通知地址
     */
    @Length(max = 256, message = "退款业务系统回调通知地址 长度不能超过256")
    @Column(name = "refund_biz_notify_url")
    private String refundBizNotifyUrl;
    /**
     * 订单金额，单位分
     */
    @NotNull(message = "amount not allow null")
    @Column(name = "amount")
    private Integer amount;
    /**
     * 账号ID
     */
    @Column(name = "account_id")
    private Integer accountId;

    /**
     * 通知时间
     */
    @Column(name = "notify_time")
    private Date notifyTime;
    /**
     * 业务系统通知成功时间
     */
    @Column(name = "biz_notify_time")
    private Date bizNotifyTime;
    /**
     * 退款状态， SUCCESS：退款成功CLOSED：退款关闭PROCESSING：退款处理中ABNORMAL：退款异常
     */
    @Length(max = 64, message = "退款状态， SUCCESS：退款成功CLOSED：退款关闭PROCESSING：退款处理中ABNORMAL：退款异常 长度不能超过64")
    @Column(name = "refund_status")
    private String refundStatus;
    /**
     * 业务系统回调通知地址
     */
    @Length(max = 256, message = "业务系统回调通知地址 长度不能超过256")
    @NotBlank(message = "biz_notify_url not allow null")
    @Column(name = "biz_notify_url")
    private String bizNotifyUrl;
    /**
     * 用户在直连商户appid下的唯一标识
     */
    @Length(max = 64, message = "用户在直连商户appid下的唯一标识 或者用户的客户端IP")
    @NotBlank(message = "openid not allow null")
    @Column(name = "openid")
    private String openid;
    @NotNull(message = "expire_time not allow null")
    @Column(name = "expire_time")
    private Date expireTime;
    /**
     * 通知摘要
     */
    @Length(max = 128, message = "通知摘要 长度不能超过128")
    @Column(name = "notify_summary")
    private String notifySummary;
    /**
     * 退款入账账户，如：招商银行信用卡0403
     */
    @Length(max = 64, message = "退款入账账户，如：招商银行信用卡0403 长度不能超过64")
    @Column(name = "refund_user_received_account")
    private String refundUserReceivedAccount;
    /**
     * 支付完成时间
     */
    @Column(name = "success_time")
    private Date successTime;
    /**
     * 支付退款号
     */
    @Length(max = 64, message = "支付退款号 长度不能超过64")
    @Column(name = "refund_id")
    private String refundId;
    /**
     * 实际退款金额
     */
    @Column(name = "refund_payer_refund")
    private Integer refundPayerRefund;
    /**
     * 业务系统通知退款成功时间
     */
    @Column(name = "refund_biz_notify_time")
    private Date refundBizNotifyTime;
    /**
     * 业务系统内部订单号
     */
    @Length(max = 64, message = "业务系统内部订单号 长度不能超过64")
    @NotBlank(message = "biz_order_no not allow null")
    @Column(name = "biz_order_no")
    private String bizOrderNo;
    /**
     * 订单状态，NOTPAY：未支付，SUCCESS：支付成功，PAYERROR：支付失败，CLOSED：已关闭
     */
    @Length(max = 64, message = "订单状态，NOTPAY：未支付，SUCCESS：支付成功，PAYERROR：支付失败，CLOSED：已关闭 长度不能超过64")
    @Column(name = "`status`")
    private String status;

    /**
     * 支付平台，WECHAT:微信
     */
    @Length(max = 64)
    @Column(name = "pay_provider")
    private String payProvider = PayProviderEnum.WECHAT.name();

    /**
     * 交易类型
     */
    @Transient
    private String tradeType;

    /**
     * 用户的客户端IP
     */
    @Transient
    private String clientIp;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }


    public String getBizNotifyStatus() {
        return bizNotifyStatus;
    }

    public void setBizNotifyStatus(String bizNotifyStatus) {
        this.bizNotifyStatus = bizNotifyStatus;
    }


    public Date getRefundSuccessTime() {
        return refundSuccessTime;
    }

    public void setRefundSuccessTime(Date refundSuccessTime) {
        this.refundSuccessTime = refundSuccessTime;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }


    public String getNotifyId() {
        return notifyId;
    }

    public void setNotifyId(String notifyId) {
        this.notifyId = notifyId;
    }


    public String getPrepayId() {
        return prepayId;
    }

    public void setPrepayId(String prepayId) {
        this.prepayId = prepayId;
    }


    public Integer getPayerTotal() {
        return payerTotal;
    }

    public void setPayerTotal(Integer payerTotal) {
        this.payerTotal = payerTotal;
    }


    public String getRefundNotifySummary() {
        return refundNotifySummary;
    }

    public void setRefundNotifySummary(String refundNotifySummary) {
        this.refundNotifySummary = refundNotifySummary;
    }


    public String getRefundChannel() {
        return refundChannel;
    }

    public void setRefundChannel(String refundChannel) {
        this.refundChannel = refundChannel;
    }


    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }


    public Date getRefundNotifyTime() {
        return refundNotifyTime;
    }

    public void setRefundNotifyTime(Date refundNotifyTime) {
        this.refundNotifyTime = refundNotifyTime;
    }


    public String getRefundBizNotifyStatus() {
        return refundBizNotifyStatus;
    }

    public void setRefundBizNotifyStatus(String refundBizNotifyStatus) {
        this.refundBizNotifyStatus = refundBizNotifyStatus;
    }


    public String getRefundReason() {
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }


    public String getRefundNotifyId() {
        return refundNotifyId;
    }

    public void setRefundNotifyId(String refundNotifyId) {
        this.refundNotifyId = refundNotifyId;
    }


    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }


    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }


    public String getRefundBizNotifyUrl() {
        return refundBizNotifyUrl;
    }

    public void setRefundBizNotifyUrl(String refundBizNotifyUrl) {
        this.refundBizNotifyUrl = refundBizNotifyUrl;
    }


    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }


    public Date getNotifyTime() {
        return notifyTime;
    }

    public void setNotifyTime(Date notifyTime) {
        this.notifyTime = notifyTime;
    }


    public Date getBizNotifyTime() {
        return bizNotifyTime;
    }

    public void setBizNotifyTime(Date bizNotifyTime) {
        this.bizNotifyTime = bizNotifyTime;
    }


    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }


    public String getBizNotifyUrl() {
        return bizNotifyUrl;
    }

    public void setBizNotifyUrl(String bizNotifyUrl) {
        this.bizNotifyUrl = bizNotifyUrl;
    }


    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }


    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }


    public String getNotifySummary() {
        return notifySummary;
    }

    public void setNotifySummary(String notifySummary) {
        this.notifySummary = notifySummary;
    }


    public String getRefundUserReceivedAccount() {
        return refundUserReceivedAccount;
    }

    public void setRefundUserReceivedAccount(String refundUserReceivedAccount) {
        this.refundUserReceivedAccount = refundUserReceivedAccount;
    }


    public Date getSuccessTime() {
        return successTime;
    }

    public void setSuccessTime(Date successTime) {
        this.successTime = successTime;
    }


    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }


    public Integer getRefundPayerRefund() {
        return refundPayerRefund;
    }

    public void setRefundPayerRefund(Integer refundPayerRefund) {
        this.refundPayerRefund = refundPayerRefund;
    }


    public Date getRefundBizNotifyTime() {
        return refundBizNotifyTime;
    }

    public void setRefundBizNotifyTime(Date refundBizNotifyTime) {
        this.refundBizNotifyTime = refundBizNotifyTime;
    }


    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPayProvider() {
        return payProvider;
    }

    public void setPayProvider(String payProvider) {
        this.payProvider = payProvider;
    }
    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public boolean isSuccess() {
        return OrderStatusEnum.SUCCESS.name().equals(this.status);
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }
}