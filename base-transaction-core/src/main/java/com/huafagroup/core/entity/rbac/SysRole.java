package com.huafagroup.core.entity.rbac;
import javax.persistence.*;
import java.io.Serializable;

import org.hibernate.validator.constraints.Length;
import com.huafagroup.core.entity.BaseEntity;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-02-02 17:24:34
 * @version
 */
@Table(name = "sys_role")
public class SysRole extends BaseEntity implements Serializable {

	     /**
     * 部门ID
     */
    @Length(max=32,message="部门ID 长度不能超过32")
    @Column(name = "department_id")
	private String departmentId;
	     /**
     * 职位名称
     */
    @Length(max=40,message="职位名称 长度不能超过40")
    @Column(name = "name")
	private String name;

	/**
	 * 备注
	 */
	@Length(max=255,message="备注 长度不能超过40")
	@Column(name = "comment")
	private String comment;

	        	        
	public String getDepartmentId() {
        return departmentId;
    }

	public void setDepartmentId(String departmentId) {
    	 this.departmentId = departmentId;
	}

        	        
	public String getName() {
        return name;
    }

	public void setName(String name) {
    	 this.name = name;
	}


	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}
}