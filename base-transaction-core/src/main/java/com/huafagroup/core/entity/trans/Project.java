package com.huafagroup.core.entity.trans;

import com.huafagroup.core.entity.BaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-22 16:07:09
 */
@Table(name = "trans_project")
public class Project extends BaseEntity implements Serializable {

    /**
     * 0-默认，1-删除
     */
    @NotNull(message = "del_flag not allow null")
    @Column(name = "del_flag")
    private Integer delFlag;
    /**
     * 1-同步，2-手动
     */
    @NotNull(message = "create_type not allow null")
    @Column(name = "create_type")
    private Integer createType;
    /**
     * 父级
     */
    @Column(name = "parent_id")
    private String parentId;
    /**
     * 名称
     */
    @Length(max = 64, message = "名称 长度不能超过64")
    @NotBlank(message = "name not allow null")
    @Column(name = "name")
    private String name;
    /**
     * 0-area;1-city;2-org;3-project
     */
    @NotNull(message = "type not allow null")
    @Column(name = "type")
    private Integer type;


    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getCreateType() {
        return createType;
    }

    public void setCreateType(Integer createType) {
        this.createType = createType;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

}