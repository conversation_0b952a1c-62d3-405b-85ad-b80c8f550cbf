package com.huafagroup.core.entity.auth.vo;

import com.huafagroup.core.annotation.SysDictItem;
import com.huafagroup.core.entity.auth.Resource;

import javax.persistence.Column;
import javax.persistence.Transient;


/**
 * <p>
 * 
 *
 *
 * </p>
 * 
 * <AUTHOR>
 * @date 2019-07-19 10:34:11
 * @version
 */

public class ResourceVo extends Resource {


	@Column(name = "opr_code")
	private String oprCode;

	@Column(name = "role_type")
	private String roleType;


    @Column(name = "appNo")
    private String appNo;

    @SysDictItem("section")
    @Column(name = "sectionText")
    private String sectionText;

    @Transient
    private String userId;



    @Transient
    private Integer pageIndex;
    @Transient
    private Integer pageSize;
    @Transient
    private Boolean isSuperAdmin;

    public Boolean getIsSuperAdmin() {
        return isSuperAdmin;
    }

    public void setIsSuperAdmin(Boolean isSuperAdmin) {
        this.isSuperAdmin = isSuperAdmin;
    }

    public String getSectionText() {
        return sectionText;
    }

    public void setSectionText(String sectionText) {
        this.sectionText = sectionText;
    }

    public String getAppNo() {
        return appNo;
    }

    public void setAppNo(String appNo) {
        this.appNo = appNo;
    }


    @Override
    public String getUserId() {
        return userId;
    }

    @Override
    public void setUserId(String userId) {
        this.userId = userId;
    }


    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        this.pageIndex = pageIndex;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


    public String getOprCode() {
        return oprCode;
    }

	public void setOprCode(String oprCode) {
		this.oprCode = oprCode;
	}

	public String getRoleType() {
		return roleType;
	}

	public void setRoleType(String roleType) {
		this.roleType = roleType;
	}
}