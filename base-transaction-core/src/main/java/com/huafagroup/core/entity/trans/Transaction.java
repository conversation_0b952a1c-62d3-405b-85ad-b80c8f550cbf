package com.huafagroup.core.entity.trans;

import com.huafagroup.core.entity.IntegerBaseEntity;
import org.hibernate.validator.constraints.Length;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


/**
 * <p>
 *
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2021-04-19 09:50:25
 */
@Table(name = "trans_transaction")
public class Transaction extends IntegerBaseEntity implements Serializable {

    public Transaction(){}

    public Transaction(Integer amount, Integer accountId, String desc, String type, Integer orderId) {
        this.amount = amount;
        this.accountId = accountId;
        this.desc = desc;
        this.type = type;
        this.orderId = orderId;
        Date now = new Date();
        setCreateDate(now);
        setUpdateDate(now);
    }

    /**
     * 交易金额，单位分
     */
    @NotNull(message = "amount not allow null")
    @Column(name = "amount")
    private Integer amount;
    /**
     * 账号id
     */
    @NotNull(message = "account_id not allow null")
    @Column(name = "account_id")
    private Integer accountId;
    @Length(max = 128, message = " 长度不能超过128")
    @Column(name = "`desc`")
    private String desc;
    /**
     * 交易类型，1：支付订单，2：退款订单，3：激励订单 4：充值订单
     */
    @Length(max = 16, message = "交易类型，PAY：支付订单，REFUND：退款订单，TRANSFER：激励订单 DEPOSIT：充值订单 长度不能超过16")
    @NotBlank(message = "type not allow null")
    @Column(name = "type")
    private String type;
    /**
     * 关联的订单id，可能是支付订单，退款订单，激励订单，充值订单
     */
    @NotNull(message = "order_id not allow null")
    @Column(name = "order_id")
    private Integer orderId;


    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }


    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }


    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

}