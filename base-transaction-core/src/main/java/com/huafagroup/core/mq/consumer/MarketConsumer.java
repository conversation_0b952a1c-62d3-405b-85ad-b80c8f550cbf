package com.huafagroup.core.mq.consumer;

import com.huafagroup.core.service.trans.ProjectService;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragely;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.RPCHook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Order(1)
public class MarketConsumer implements CommandLineRunner {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${apache.rocketmq.group}")
    private String pushConsumer;
    @Value("${apache.rocketmq.namesrvAddr}")
    private String namesrvAddr;
    @Value("${apache.rocketmq.acl.user}")
    private String user;
    @Value("${apache.rocketmq.topic}")
    private String topic;
    @Value("${apache.rocketmq.acl.pass}")
    private String pass;
    @Value("${apache.rocketmq.sync.tables}")
    private String syncTables;
    @Value("${apache.rocketmq.commissionTopicSwitch}")
    private int commissionTopicSwitch;
    @Autowired
    private ProjectService projectService;


    @Override
    public void run(String... args) throws Exception {
        if (commissionTopicSwitch == 0) {
            logger.info("股份基础数据同步MQ开关关闭");
            return;
        }
        this.pushConsumer();
    }

    public void pushConsumer() throws MQClientException {
        //创建线程池，核心线程数为4（CPU 核数 × 2），等待队列20，允许的最大线程数为10（等待队列满之后，才会创建）
        //CallerRunsPolicy策略：如果线程池的线程数量达到上限，该策略会把任务队列中的任务放在主线程当中运行（阻塞主线程）
        ThreadPoolExecutor pool = new ThreadPoolExecutor(4, 10, 1000, TimeUnit.MILLISECONDS, new ArrayBlockingQueue<>(20), new ThreadPoolExecutor.CallerRunsPolicy());
        RPCHook rpcHook = new AclClientRPCHook(new SessionCredentials(user, pass));
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(pushConsumer, rpcHook, new AllocateMessageQueueAveragely());
        consumer.setNamesrvAddr(namesrvAddr);
        List<String> syncTablesList = Arrays.asList(syncTables.split(";"));
        consumer.subscribe(topic, "*");
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
        //可以修改每次消费消息的数量，默认设置是每次消费一条
        consumer.setConsumeMessageBatchMaxSize(10);

        consumer.registerMessageListener((MessageListenerConcurrently) (msgs, context) -> {

            for (Message msg : msgs) {
                pool.execute(() -> {
                    //logger.info("ID：{}，消息：{}，已消费！", msg.getUserProperty("UNIQ_KEY"), json);
                    if (syncTablesList.contains(msg.getTags())) {
                        try {
                            String json = new String(msg.getBody());
                            projectService.dataSync(json, msg.getTags());

                        } catch (Exception e) {
//                            logger.error("消息落库失败，ID：{}，消息：{}", msg.getUserProperty("UNIQ_KEY"), json, e);
                        }
                    }

                });
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        });
        consumer.start();
        logger.info("同步项目数据消费者，启动完成！");
    }
}
