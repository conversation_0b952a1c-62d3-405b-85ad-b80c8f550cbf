package com.huafagroup.core.annotation;

/**
 * <AUTHOR>
 * 2018/12/30
 */

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SysDictItem {

    String value() default "";
}
