package com.huafagroup.core.exception;

/**
 * Created by wd on 2016-7-20.
 */


public class MsgFactory {


    public static MsgContentStruct requestTooOften = new MsgContentStruct("request_too_often", "请求过于频繁，请稍候再试");
    public static MsgContentStruct requestForbidden = new MsgContentStruct("request_forbidden", "禁止访问");
    public static MsgContentStruct relationInputInvalid = new MsgContentStruct("relation_Input_Invalid", "关系人信息输入不合法：%s");
    public static MsgContentStruct relationVerifyError = new MsgContentStruct("relation_Verify_Error", "关系人验证失败：%s");

    public static MsgContentStruct mobileInfoNotExist = new MsgContentStruct("mobile_info_not_exist", "抱歉，您手机号信息不存在，请联系客服补充手机信息后再试");//旧APP：请旅客联系客服先行补充手机信息后再设置密码
    public static MsgContentStruct passwordTooSimple = new MsgContentStruct("password_too_simple", "抱歉，您的密码太简单，为提高账户安全性，请进行密码找回");//旧APP：当前密码过于简单，为提高账户安全性，请进行密码找回
    public static MsgContentStruct getVerifyCodeTooOften = new MsgContentStruct("get_verify_code_too_often", "请求验证码太频繁");
    public static MsgContentStruct smVerifyCodeCheckFail = new MsgContentStruct("sm_verify_code_check_fail", "短信验证码验证失败");

    public static MsgContentStruct addFocusFlightFail = new MsgContentStruct("add_focus_flight_fail", "该航班已添加该关注");
    public static MsgContentStruct queryFocusFlightFail = new MsgContentStruct("query_focus_flight_fail", "未查询到关注航班");

    public static MsgContentStruct pictureNotFound = new MsgContentStruct("picture_not_found", "找不到相应的图片信息");

    //shopping
    public static MsgContentStruct cabinIsSoldOut = new MsgContentStruct("cabin_is_sold_out", "非常抱歉，该舱位已售罄");
    public static MsgContentStruct cabinReturnIsSoldOut = new MsgContentStruct("cabin_return_is_sold_out", "非常抱歉，该舱位的返程已售罄");
    public static MsgContentStruct flightIsSoldOut = new MsgContentStruct("flight_is_sold_out", "非常抱歉，所有航班已售罄");
    public static MsgContentStruct flightReturnIsSoldOut = new MsgContentStruct("flight_return_is_sold_out", "非常抱歉，所有航班的返程已售罄");
    public static MsgContentStruct priceInfoIsNotFount = new MsgContentStruct("price_info_is_not_found", "抱歉，无法查询到相应的航线价格信息");
    public static MsgContentStruct noFlightToday = new MsgContentStruct("no_flight_today", "抱歉，该航线当天没有航班，请换个日期试试");
    public static MsgContentStruct policyIsInvalid = new MsgContentStruct("policy_is_invalid", "非常抱歉，此航线的优惠政策已失效，请重新预定");
    public static MsgContentStruct shoppingCheckFail = new MsgContentStruct("shopping_check_fail", "请求验证失败");

    //PE接口
    public static MsgContentStruct pe_noCheckInTicketById = new MsgContentStruct("pe_no_checkin_ticket", "该证件号没有可办理值机的客票!");
    public static MsgContentStruct pe_noGetCheckInTime = new MsgContentStruct("pe_no_get_checkin_time", "非常抱歉，您乘坐的航班尚未到值机时间。建议您在航班起飞前24小时内进行操作。!");
    public static MsgContentStruct pe_noBoardingPassInfo = new MsgContentStruct("pe_no_boardingpass_info", "未查到该登机牌信息!");
    public static MsgContentStruct pe_noCheckInCode = new MsgContentStruct("pe_no_checkin_code", "未查到该次值机验证码信息!");
    public static MsgContentStruct pe_checkInCodeFail = new MsgContentStruct("pe_checkin_code_fail", "值机验证码输入有误!");
    public static MsgContentStruct pe_checkInFailureIn40mins = new MsgContentStruct("pe_checkIn_failure_in40mis", "值机失败，飞机起飞前%s分钟内不允许值机!");
    public static MsgContentStruct pe_checkInFailure95557= new MsgContentStruct("pe_checkIn_failure_95557", "办理值机失败，请拨打95557进行确认");
    public static MsgContentStruct pe_checkInQrCodeFailure= new MsgContentStruct("pe_checkIn_qrcode", "办理值机成功，生成二维码电子登机牌失败");
    public static MsgContentStruct pe_checkInApplePassBook= new MsgContentStruct("pe_checkIn_passbook", "办理值机成功，生成wallet登机牌失败");
    public static MsgContentStruct pe_cancelCheckInFailure = new MsgContentStruct("pe_cancel_checkIn_failure", "取消值机失败，飞机起飞前60分钟内不允许取消值机!");
    public static MsgContentStruct pe_fltCannotCheckIn = new MsgContentStruct("pe_flt_cannot_check_in", "抱歉，当前航班无法办理在线值机");

    //订单
    public static MsgContentStruct psgTooMuch = new MsgContentStruct("psg_too_much", "乘客数量过多");
    public static MsgContentStruct psgNameError = new MsgContentStruct("psg_name_error", "您的订单中旅客姓名(%s)有误，请重新修改");
    public static MsgContentStruct contentIllegal = new MsgContentStruct("content_illegal", "%s不合法：%s");
    public static MsgContentStruct contentCanNotBeEmpty = new MsgContentStruct("content_can_not_be_empty", "%s不能为空");
    public static MsgContentStruct needSeparateBooking = new MsgContentStruct("need_separate_booking", "%s，需要分开预定");
    public static MsgContentStruct tooManyInsurance = new MsgContentStruct("too_many_insurance", "每个人在每个航程只能购买%d份航空意外险和%d份航空延误险");
    public static MsgContentStruct seatNotAvailable = new MsgContentStruct("seat_not_available", "非常抱歉，座位数已失效，请重新预定");
    public static MsgContentStruct faithlessPeople = new MsgContentStruct("faithless_people", "存在失信人，无法预定");
    public static MsgContentStruct cabinCombinationForbid = new MsgContentStruct("cabin_combination_forbid", "不可用的舱位组合");
    public static MsgContentStruct deptTimeLimit = new MsgContentStruct("dept_time_limit", "此航班离起飞时间小于%d分钟，无法预定");
    public static MsgContentStruct roundTripTimeLimit = new MsgContentStruct("round_trip_time_limit", "往返程航班的时间间隔不能小于%d分钟");
    public static MsgContentStruct orderNotExist = new MsgContentStruct("order_not_exist", "订单不存在");
    public static MsgContentStruct orderInProcessing = new MsgContentStruct("order_in_processing", "订单正在操作处理中，请稍后再试");
    public static MsgContentStruct orderStateCanNotIssue = new MsgContentStruct("order_state_can_not_issue", "当前订单无法出票：%s");
//    public static MsgContentStruct orderCanNotIssueDueToPayRecord = new MsgContentStruct("order_can_not_issue_due_to_pay_record", "当前订单无法出票，因为找不到支付成功的支付记录");
//    public static MsgContentStruct orderCanNotIssueDueToMistakeRefund = new MsgContentStruct("order_can_not_issue_due_to_mistake_refund", "当前订单无法出票，因为支付成功的记录已经提交差错退款");
    public static MsgContentStruct orderStateCanNotRefund = new MsgContentStruct("order_state_can_not_refund", "当前订单状态无法退票");
    public static MsgContentStruct ticketStateCanNotRefund = new MsgContentStruct("ticket_state_can_not_refund", "该票已使用或处于退票状态");
    public static MsgContentStruct refundTicketInfoNotFound = new MsgContentStruct("refund_ticket_info_not_found","非常抱歉，获取退票信息失败,请稍后再试");
    public static MsgContentStruct orderIssueFail = new MsgContentStruct("order_issue_fail", "出票失败");
    public static MsgContentStruct orderRefundFail = new MsgContentStruct("order_refund_fail", "退票失败");
    public static MsgContentStruct refundRateNotFound = new MsgContentStruct("refund_rate_not_found", "找不到相应的退票费率");
    public static MsgContentStruct orderCanNotMistakeRefundDueToPayRecord = new MsgContentStruct("order_can_not_mistake_refund_due_to_pay_record", "当前订单无法提交差错退款申请，因为找不到支付成功的支付记录");
    public static MsgContentStruct mistakeRefundApplyFail = new MsgContentStruct("mistake_refund_apply_fail", "提交差错退款失败");
    public static MsgContentStruct mistakeRefundAuditFail = new MsgContentStruct("mistake_refund_audit_fail", "差错退款审核失败");
    public static MsgContentStruct psgReOrder = new MsgContentStruct("psg_reorder", "旅客%s已经预定了航班%s，不能重复预定");
    public static MsgContentStruct priceFail = new MsgContentStruct("price_fail", "抱歉，价格有变动，请确认后重新提交订单");
    public static MsgContentStruct tooManyChildren = new MsgContentStruct("too_many_children", "一个成人最多带%s个儿童");
    public static MsgContentStruct internationalOnlySupportRefundAll = new MsgContentStruct("international_only_support_refund_all",
        "抱歉，目前APP上国际票仅支持全部退票。如有其他需求请联系客服处理");
    public static MsgContentStruct tooManyOrderWaitForPay = new MsgContentStruct("too_many_order_wait_for_pay", "抱歉，您未支付的订单过多，无法继续预定。未支付的订单已被作废。");
    public static MsgContentStruct tooManyOrderNotyPayCancel = new MsgContentStruct("too_many_order_not_pay_cancel", "抱歉，您未支付作废的订单过多，今天无法继续预定");
    public static MsgContentStruct inOrderBlackList = new MsgContentStruct("in_order_black_list", "抱歉，您暂时无法预定订单，请稍候再试");
    public static MsgContentStruct makeOrderFail = new MsgContentStruct("make_order_fail", "抱歉，生成订单失败：%s");
    public static MsgContentStruct orderCanNotPay = new MsgContentStruct("order_can_not_pay", "订单无法支付：%s");
    public static MsgContentStruct orderCanNotRefundDueToRule = new MsgContentStruct("order_can_not_refund_due_to_rule", "抱歉，由于退票规则限制，此订单无法退票");
    public static MsgContentStruct refundCheckTicketInfoChange = new MsgContentStruct("refund_check_ticket_info_change", "客票信息发生变更，请联系客户服务进行退票");

    //贵宾休息室
    public static MsgContentStruct vipRoomMakeTicketFail = new MsgContentStruct("vipRoomMakeTicketFail", "出票失败！");
    public static MsgContentStruct vipRoomTicketRefundStatusErr = new MsgContentStruct("vip_tickets_status_err", "贵宾休息券只有状态为‘未使用’才可执行此操作！");
    //我的行程接口
    public static MsgContentStruct myRoutInterfaceFail = new MsgContentStruct("myRout_interface_fail", "获取我的行程接口失败：%s");
    public static MsgContentStruct myRoutListIsNull = new MsgContentStruct("myRout_list_is_null", "没有行程记录，世界辣么大，我们去看看吧！");

    //优惠券
    public static MsgContentStruct couponAcceptPlatformErr = new MsgContentStruct("couponAcceptPlatformErr", "接入平台id错误！");
    public static MsgContentStruct couponAcceptPlatformUnuse = new MsgContentStruct("couponAcceptPlatformUnuse", "接入平台未启用！");
    public static MsgContentStruct couponNumZero = new MsgContentStruct("coupon_num_zero", "优惠券领取失败：%s");
    public static MsgContentStruct couponInvalid = new MsgContentStruct("coupon_invalid", "优惠券失效：%s");
    public static MsgContentStruct couponCannotUse = new MsgContentStruct("coupon_can_not_use", "优惠券不能使用：%s");
    public static MsgContentStruct couponCannotAuthentication = new MsgContentStruct("couponCannotAuthentication", "认证失败:%s");
    public static MsgContentStruct couponAlreadyUsed = new MsgContentStruct("coupon_already_used", "优惠券已使用");
    public static MsgContentStruct couponAuthenticateNotFound = new MsgContentStruct("coupon_authenticate_not_found", "未找到相应的认证信息");

    //保险
    public static MsgContentStruct insuranceInterfaceFail = new MsgContentStruct("insurance_interface_fail", "保险接口返回失败：%s");
    public static MsgContentStruct sinsuranceOrderStatusNotMatch = new MsgContentStruct("status_not_match", "保单当前状态 %s 无法进行此操作");
    public static MsgContentStruct refundFailExistNotSuccessInsuranceOrder = new MsgContentStruct("refund_fail_exist_not_success_insurance_order",
        "存在未投保成功的保单，暂时无法退票。请联系客服人员处理后再申请。");
    public static MsgContentStruct onlyDomesticInsuranceSupport = new MsgContentStruct("only_domestic_insurance_support", "抱歉，暂时只支持国内保险");
    public static MsgContentStruct tooOldBuyAviation = new MsgContentStruct("too_old_buy_aviation", "抱歉，超过%s周岁的旅客不能购买航意险");

    public static MsgContentStruct routeNotExist = new MsgContentStruct("route_not_exist", "航线不存在");
    public static MsgContentStruct priceCalendarNotComplete = new MsgContentStruct("price_calendar_not_complete", "价格日历数据不完整");
    //常用乘机人
    public static MsgContentStruct commonUserHasExist = new MsgContentStruct("commonUserHasExist", "该乘机人已存在！");
    public static MsgContentStruct commonUserTooMuch = new MsgContentStruct("commonUserTooMuch", "最多只能添加12个乘机人！");
    public static MsgContentStruct commonUserVerifyFail = new MsgContentStruct("commonUserVerifyFail", "常客信息验证失败！");

    //价格校验
    public static MsgContentStruct priceCheckErr = new MsgContentStruct("price_check_err", "价格校验失败，请重新预定");

    //IBE错误，待整理
    public static MsgContentStruct ibeErrMsg = new MsgContentStruct("ibe_err_msg", "%s");

    //特殊情况
    public static MsgContentStruct thirdPartyWsErr = new MsgContentStruct("third_party_ws_err", "");//第三方接口返回的错误信息，msg为第三方接口返回的错误内容
    //退票成人儿童
    public static MsgContentStruct refundAdtChdRatioErr = new MsgContentStruct("rfd_adt_chd_rate_err", "无法完成退票，儿童必须有成人陪伴，并且每个成人最多带%s个儿童");
    //参数长度校验
    public static MsgContentStruct parameterOutOfLength = new MsgContentStruct("parameter_out_of_length", "参数%s长度不能超过:%s");

    //ffp
    public static MsgContentStruct noExChangeFlt = new MsgContentStruct("no_exchange_flt", "抱歉，无法查询到可兑换的航班");
    public static MsgContentStruct exChangeOnlyDomestic = new MsgContentStruct("ex_change_only_domestic", "抱歉，目前免票兑换仅支持国内航班");
    public static MsgContentStruct realNameVerifyConditionsNeed = new MsgContentStruct("rn_verify_no_conditions", "您的账户中未留中文姓名或身份证号码，请携带本人有效身份证件原件前往厦航直销售票处办理");
    public static MsgContentStruct realNameVerifyMaxFailLimit = new MsgContentStruct("rn_verify_fail_limit", "您已累计认证错误%s次，请于%s小时后再次尝试操作");
    public static MsgContentStruct realNameVerifyMultipleCardNo = new MsgContentStruct("rn_verify_multiple_cardNo", "您的证件号码对应多个白鹭会员账户，请携带本人有效身份证件原件前往厦航直销售票处办理");
    public static MsgContentStruct unableSKTRetroAirCode = new MsgContentStruct("unable_skt_retro_airCode", "抱歉，目前暂时不支持代码为%s的航空公司的积分补登");
    public static MsgContentStruct unableSKTRetroFlightDate = new MsgContentStruct("unable_skt_retro_flightDate", "抱歉，航班时间不在积分补登的有效期内");

    //船套票
    public static MsgContentStruct flightDateError = new MsgContentStruct("flight_date_error", "抱歉，当前日期不可查询");
    public static MsgContentStruct flightFerryCantSearch = new MsgContentStruct("flight_ferry_cant_search", "抱歉，该航线航班时间暂未开放船票预定，请临近出发日期再来查询（船票日期在当前日期15日以内）");
    public static MsgContentStruct orderNoFlyToXmn = new MsgContentStruct("order_no_fly_to_xmn", "抱歉，该订单不是飞往厦门或为共享航班");
    public static MsgContentStruct ticketOrPsgError = new MsgContentStruct("ticket_or_psg_error", "抱歉，您的此订单票面状态不可用或您已购买船套票");
    public static MsgContentStruct orderPsgNumError = new MsgContentStruct("order_psg_num_error", "抱歉，订单人数不一致");
//    public static MsgContentStruct orderHasPaid = new MsgContentStruct("order_has_paid", "抱歉，订单已经支付，无法取消");
    public static MsgContentStruct orderTotalPriceError = new MsgContentStruct("order_total_price_error", "抱歉，订单总价不一致");
    public static MsgContentStruct orderSaveError = new MsgContentStruct("order_save_error", "抱歉，下单失败，请稍后尝试");
    public static MsgContentStruct addContactsError = new MsgContentStruct("add_contacts_error", "抱歉，添加乘客失败");
    public static MsgContentStruct contactsCanAddIsNullError = new MsgContentStruct("contacts_can_add_is_null_error", "抱歉，没有可添加的乘机人");
    public static MsgContentStruct contactsCanRefundIsNullError = new MsgContentStruct("contacts_can_refund_is_null_error", "抱歉，没有可退票的乘客");
    public static MsgContentStruct orderBePaidOrCanceled = new MsgContentStruct("order_be_paid_or_canceled", "抱歉，当前订单已取消或已支付");
    public static MsgContentStruct orderCancelError = new MsgContentStruct("order_cancel_error", "抱歉，订单取消失败，请稍后再试");
    public static MsgContentStruct orderPaidError = new MsgContentStruct("order_paid_error", "抱歉，订单支付成功，修改票面状态失败，请重新下单，等待退款");
    public static MsgContentStruct leftSeatNotEnough = new MsgContentStruct("left_seat_not_enough", "抱歉，剩余座位不足");
    public static MsgContentStruct flightIsInProcessing = new MsgContentStruct("flight_is_in_processing", "抱歉，锁定座位失败，请重试");
    public static MsgContentStruct cantRefundFerryTicketPassTime = new MsgContentStruct("cant_refund_ferry_ticket_pass_time", "抱歉，当前已过退票时间");
    public static MsgContentStruct cantRefundFerryOrderNoPaid = new MsgContentStruct("cant_refund_ferry_order_no_paid", "抱歉，该订单未支付");
    public static MsgContentStruct ferryOrderExistError = new MsgContentStruct("ferry_order_exist_error","如退机票会同时退游鼓浪屿订单，收手续费请参考相关产品退改期说明。您是否确认退票？");

    //电子钱包
    public static MsgContentStruct eWalletFeeVerifyError = new MsgContentStruct("eWallet_fee_verify_error","手续费校验失败，请重试.");
    public static MsgContentStruct eWalletLimitVerifyError = new MsgContentStruct("eWallet_limit_verify_error","抱歉，您的余额超过提现限额.");



}
