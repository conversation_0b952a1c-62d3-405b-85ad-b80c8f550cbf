package com.huafagroup.core.exception;

/*
* 通用异常代码枚举
* success 正常
*
* */
public enum ExceptionEnum {

    SUCCESS("200"),
    RESOURCE_NOT_FOUND("404"),
    ARGUMENTS_INVALID("401"),
    BUSINESS_ERROR("400"),
    SERVER_ERROR("500"),
    PARAMS_NOT_VALID("7001");


    private ExceptionEnum(String code){
        this.code=code;
    }
    // 成员变量
    private String code;
    public   String getCode(){
        return  this.code;
    }
}
