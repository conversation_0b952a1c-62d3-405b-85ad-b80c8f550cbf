package com.huafagroup.core.codegenerator.core;

import tk.mybatis.mapper.util.StringUtil;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtils {


	public static final char UNDERLINE = '_';

	/**
	 * 首字符转大写
	 *
	 * @param val
	 * @return
	 */
	public static String toUpperCaseFirst(String val) {
		StringBuffer sb = new StringBuffer(val);
		sb.setCharAt(0, Character.toUpperCase(sb.charAt(0)));
		val = sb.toString();
		return val;
	}



	public static String camelToUnderline(String param) {
		if (param == null || "".equals(param.trim())) {
			return "";
		}
		int len = param.length();
		StringBuilder sb = new StringBuilder(len);
		for (int i = 0; i < len; i++) {
			char c = param.charAt(i);
			if (Character.isUpperCase(c)) {
				sb.append(UNDERLINE);
				sb.append(Character.toLowerCase(c));
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}

	//银行账号 保存前四位 后四位
	public static String BankAccountDisplay(String param) {
		if (param == null || "".equals(param.trim())) {
			return "";
		}
		int len = param.length();
		return param.substring(0,4)+"****************"+param.substring(len-4,len);

	}

	public static String underLineToCamel(String param) {
		if (param == null || "".equals(param.trim())) {
			return "";
		}
		int len = param.length();
		StringBuilder sb = new StringBuilder(len);
		for (int i = 0; i < len; i++) {
			char c = param.charAt(i);
			if (c == UNDERLINE) {
				if (++i < len) {
					sb.append(Character.toUpperCase(param.charAt(i)));
				}
			} else {
				sb.append(c);
			}
		}
		return sb.toString();
	}

	public static String underLineToCamel2(String param) {
		if (param == null || "".equals(param.trim())) {
			return "";
		}
		StringBuilder sb = new StringBuilder(param);
		Matcher mc = Pattern.compile("_").matcher(param);
		int i = 0;
		while (mc.find()) {
			int position = mc.end() - (i++);
			// String.valueOf(Character.toUpperCase(sb.charAt(position)));
			sb.replace(position - 1, position + 1, sb.substring(position, position + 1).toUpperCase());
		}
		return sb.toString();
	}

	//比较两个字符串是否相等
	public static boolean equalsStr(String str1, String str2){
		if(StringUtil.isEmpty(str1) && StringUtil.isEmpty(str2)){
			return true;
		}
		if(!StringUtil.isEmpty(str1) && str1.equals(str2)){
			return true;
		}
		return false;
	}

	//为null的返回空值
	public static String emptyStrHandle(String str1){
		if(str1==null){
			return "";
		}else{
			return str1;
		}
	}

	public static void main(String[] args) {
	}

}
