package com.huafagroup.core.configuration;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * Created by summer on 2016/11/25.
 */
@Configuration
@MapperScan(basePackages = "com.huafagroup.core.dao", sqlSessionTemplateRef = "framedbSqlSessionTemplate")
public class FramedbConfig {

    @Autowired
    private SQLInterceptor sqlInterceptor;

    @Bean(name = "framedbDataSource")
    @ConfigurationProperties(prefix = "first.spring.datasource")

    public DataSource framedbDataSource() {
        return new DruidDataSource();
    }


    @Bean(name = "framedbSqlSessionFactory")

    public SqlSessionFactory testSqlSessionFactory(@Qualifier("framedbDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setPlugins(new Interceptor[]{ sqlInterceptor});
        //配置configuration
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        //设置下划线转驼峰
        configuration.setMapUnderscoreToCamelCase(true);
        bean.setConfiguration(configuration);
        bean.setDataSource(dataSource);


        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        try {
            //如果没有mapper文件，会报错，try一下，目录没有用到
            Resource[] res = resolver.getResources("classpath:mapper/**");
            for (int i =0;i< res.length;i++) {
                if(res[i].getFilename().indexOf("xml")==-1){
                    res[i] = null;
                }
            }
            bean.setMapperLocations(res);
        } catch (Exception e) {
        }


        return bean.getObject();
    }


    @Bean(name = "framedbTransactionManager")

    public DataSourceTransactionManager testTransactionManager(@Qualifier("framedbDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "framedbSqlSessionTemplate")

    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("framedbSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}