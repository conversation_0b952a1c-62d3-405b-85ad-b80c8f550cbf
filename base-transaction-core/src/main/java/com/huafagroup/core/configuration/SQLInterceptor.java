package com.huafagroup.core.configuration;

import cn.hutool.core.util.IdUtil;
import com.huafagroup.core.entity.BaseEntity;
import com.huafagroup.core.util.JsonUtil;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * @Description 本拦截器会拦截mybatis insert  update  delete 三个操作,
 * 1、自动设置id  updatedate 等5个字段，使用时间序列生成的UUID，jdk默认的uuid有概率重复
 * 2、自动化写日志logger.info
 * 3、20190306 修复批量操作
 * <AUTHOR>
 * @Date 2019/1/4 9:04
 **/
@Component
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {
                MappedStatement.class, Object.class})})
public class SQLInterceptor implements Interceptor {

    private Logger logger = LoggerFactory.getLogger(SQLInterceptor.class);


    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        //获取原始的ms
        MappedStatement ms = (MappedStatement) invocation.getArgs()[0];
        String commandName = ms.getSqlCommandType().name();
        Object parameter = invocation.getArgs()[1];

        if (commandName.startsWith("DELETE")) {//删除操作
            logger.info("删除记录{}，id信息：{}", parameter.getClass().getName(), parameter);
            return invocation.proceed();
        } else if (commandName.startsWith("INSERT")) {//新增时
            if (parameter instanceof BaseEntity) {
                BaseEntity entity = (BaseEntity) parameter;
                setDefaultValue(entity);
                logger.info("新增记录{}：{}", parameter.getClass().getName(), JsonUtil.bean2Json(parameter));
            }
            else if(parameter instanceof HashMap){
                if(((HashMap) parameter).size()==2 &&((HashMap<String,Object>)parameter).containsKey("collection")){
                    //批量插入
                    HashMap<String,Object> temp=(HashMap<String,Object>)parameter;
                    List<Object> list=(List<Object>) temp.get("collection");
                    for (Object obj : list) {
                        if (obj instanceof BaseEntity) {

                            setDefaultValue((BaseEntity) obj);
                            // logger.info("批量新增记录{}：{}", obj.getClass().getName(), JsonUtil.bean2Json(obj));
                        }
                    }
                    logger.info("批量新增记录{}", list.get(0).getClass().getName(), JsonUtil.bean2Json(list));
                }

            }


        } else {//修改
            if (parameter instanceof BaseEntity) {
                BaseEntity entity = (BaseEntity) parameter;
                if(StringUtils.isEmpty(entity.getUpdateBy())) {
                    entity.setUpdateBy(MDC.get("userid"));
                }
                if(StringUtils.isEmpty(entity.getUpdateDate())) {
                    entity.setUpdateDate(new Date());
                }
            }
            logger.info("修改记录{}：{}", parameter.getClass().getName(), JsonUtil.bean2Json(parameter));
        }
        return invocation.proceed();
    }

    private  void  setDefaultValue(BaseEntity entity){
        if (StringUtils.isEmpty(entity.getId())) {
            //entity.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            //  entity.setId(String.valueOf(snowflakeIdWorker.nextId()));
            //生成的是不带-的字符串，类似于：b17f24ff026d40949c85a24f4f375d42
            entity.setId(IdUtil.simpleUUID());
            //entity.setId(UUID.randomUUID().toString().replace("-", "").toLowerCase());
            //  entity.setId(TimeBasedUUIDGenerator.generateId().toString().replace("-", ""));
        }
        if(StringUtils.isEmpty(entity.getCreateBy())) {
            entity.setCreateBy(MDC.get("userid"));//从threadlocal中取出userid
        }
        if(StringUtils.isEmpty(entity.getCreateDate())) {
            entity.setCreateDate(new Date());
        }
        if(StringUtils.isEmpty(entity.getUpdateBy())) {
            entity.setUpdateBy(MDC.get("userid"));
        }
        if(StringUtils.isEmpty(entity.getUpdateDate())) {
            entity.setUpdateDate(new Date());
        }
    }
    /**
     * @param
     * @return
     */
    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}