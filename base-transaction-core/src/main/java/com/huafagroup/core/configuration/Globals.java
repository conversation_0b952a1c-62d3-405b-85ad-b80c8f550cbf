package com.huafagroup.core.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class Globals {
    @Value("${api.auth.appId}")
    private String appId;

    @Value("${sso.auth.url}")
    private String ssoAuthUrl;

    @Value("${sso.auth.redirectUrl}")
    private String ssoAuthRedirectUrl;

    @Value("${sso.auth.currentNote.logoutUrl}")
    private String ssoAuthCurrentNoteLogoutUrl;

    @Value("${sso.auth.logoutUrl}")
    private String ssoAuthLogoutUrl;

    @Value("${sso.auth.requestTokenUrl}")
    private String requestTokenUrl;

    @Value("${sso.auth.loginRegisterUrl}")
    private String loginRegisterUrl;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSsoAuthUrl() {
        return ssoAuthUrl;
    }

    public void setSsoAuthUrl(String ssoAuthUrl) {
        this.ssoAuthUrl = ssoAuthUrl;
    }

    public String getSsoAuthRedirectUrl() {
        return ssoAuthRedirectUrl;
    }

    public void setSsoAuthRedirectUrl(String ssoAuthRedirectUrl) {
        this.ssoAuthRedirectUrl = ssoAuthRedirectUrl;
    }

    public String getSsoAuthCurrentNoteLogoutUrl() {
        return ssoAuthCurrentNoteLogoutUrl;
    }

    public void setSsoAuthCurrentNoteLogoutUrl(String ssoAuthCurrentNoteLogoutUrl) {
        this.ssoAuthCurrentNoteLogoutUrl = ssoAuthCurrentNoteLogoutUrl;
    }

    public String getSsoAuthLogoutUrl() {
        return ssoAuthLogoutUrl;
    }

    public void setSsoAuthLogoutUrl(String ssoAuthLogoutUrl) {
        this.ssoAuthLogoutUrl = ssoAuthLogoutUrl;
    }

    public String getRequestTokenUrl() {
        return requestTokenUrl;
    }

    public void setRequestTokenUrl(String requestTokenUrl) {
        this.requestTokenUrl = requestTokenUrl;
    }

    public String getLoginRegisterUrl() {
        return loginRegisterUrl;
    }

    public void setLoginRegisterUrl(String loginRegisterUrl) {
        this.loginRegisterUrl = loginRegisterUrl;
    }
}
