/*
package com.huafagroup.core.configuration;

import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


*/
/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-02-20 14:46
 **//*

@Configuration
public class RabbitMQConfig {

    @Bean
    public Queue testQueue(){

        return  new Queue("test");
    }
}
*/
