package com.huafagroup.core.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Classname 交易服务配置类
 * @Created by juesu
 */
@Data
@Component
@ConfigurationProperties(prefix = "base.transaction")
public class TransactionConfig {

    /**
     * 支付回调地址
     */
    private String payNotifyUrl;
    /**
     * 退款回调地址
     */
    private String refundsNotifyUrl;

    /**
     * 发红包的客户端地址，微信平台中需要指定，否则无法调用
     */
    private String redPackClientIP;
    /**
     * 代理服务器IP
     */
    private String proxyIP;
    /**
     * 代理服务器端口
     */
    private Integer proxyPort;

    /**
     * 如是服务地址
     */
    private String ruthusHost;

}
