package com.huafagroup.core.configuration;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

import java.net.InetAddress;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019-07-17 16:11
 **/
public class LogIpConfig extends ClassicConverter {
    private   static String ip;
    static  {
        try{
            ip= InetAddress.getLocalHost().getHostAddress();

        }catch (Exception e){//不处理，取不到
            ip="0:0:0:0";
        }
    }
    @Override
    public String convert(ILoggingEvent event) {
        return  ip;
    }
}