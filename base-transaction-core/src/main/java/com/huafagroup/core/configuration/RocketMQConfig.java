package com.huafagroup.core.configuration;


import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.remoting.RPCHook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;


//@Configuration
//@ConfigurationProperties(prefix = "spring.rocketmq.producer")
public class RocketMQConfig {
    /**
     * 日志对象
     */
    private Logger logger = LoggerFactory.getLogger(this.getClass());


    public static RPCHook getAclRPCHook() {
        return new AclClientRPCHook(new SessionCredentials("huafagroup", "huafagroup"));
    }

    @Primary
    @Bean
    public DefaultMQProducer initMQProducer() {

        DefaultMQProducer producer = new DefaultMQProducer(group, getAclRPCHook());
        producer.setNamesrvAddr(nameServer);
        producer.setInstanceName(instanceName);
        producer.setCompressMsgBodyOverHowmuch(compressMsgBodyOverHowmuch);
        producer.setMaxMessageSize(maxMessageSize);
        producer.setRetryAnotherBrokerWhenNotStoreOK(retryAnotherBrokerWhenNotStoreOk);
        producer.setRetryTimesWhenSendAsyncFailed(retryTimesWhenSendAsyncFailed);
        producer.setRetryTimesWhenSendFailed(retryTimesWhenSendFailed);
        // 使用故障延迟机制，会对获取的MQ进行可用性验证
        producer.setSendLatencyFaultEnable(true);
        producer.setSendMsgTimeout(sendMsgTimeout);
        //设置到broker的心跳
        producer.setHeartbeatBrokerInterval(3000);
        //从namesrv获取topic路由
        producer.setPollNameServerInterval(3000);

        try {
            producer.start();
        } catch (MQClientException e) {
            logger.error("RocketMQ初始化异常", e);
            return null;
        }

        return producer;
    }


    @Bean
    public DefaultMQPushConsumer initMQPushConsumer() {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(pushConsumerGroup);
        consumer.setNamesrvAddr(nameServer);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET);
        //从namesrv获取topic路由
        consumer.setPollNameServerInterval(2000);
        consumer.setHeartbeatBrokerInterval(2000);
        consumer.setConsumeMessageBatchMaxSize(10);


        return consumer;
    }

    public String getPushConsumerGroup() {
        return pushConsumerGroup;
    }

    public void setPushConsumerGroup(String pushConsumerGroup) {
        this.pushConsumerGroup = pushConsumerGroup;
    }

    public String getPullConsumerGroup() {
        return pullConsumerGroup;
    }

    public void setPullConsumerGroup(String pullConsumerGroup) {
        this.pullConsumerGroup = pullConsumerGroup;
    }

    private String pushConsumerGroup;
    private String pullConsumerGroup;


    public boolean isRetryAnotherBrokerWhenNotStoreOk() {
        return retryAnotherBrokerWhenNotStoreOk;
    }

    private String nameServer;
    private String group;
    private int retryTimesWhenSendAsyncFailed;
    private int sendMsgTimeout;
    private int compressMsgBodyOverHowmuch;
    private int maxMessageSize;
    private boolean retryAnotherBrokerWhenNotStoreOk;
    private String instanceName;

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }


    public Logger getLogger() {
        return logger;
    }

    public void setLogger(Logger logger) {
        this.logger = logger;
    }

    public String getNameServer() {
        return nameServer;
    }

    public void setNameServer(String nameServer) {
        this.nameServer = nameServer;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public int getRetryTimesWhenSendAsyncFailed() {
        return retryTimesWhenSendAsyncFailed;
    }

    public void setRetryTimesWhenSendAsyncFailed(int retryTimesWhenSendAsyncFailed) {
        this.retryTimesWhenSendAsyncFailed = retryTimesWhenSendAsyncFailed;
    }

    public int getSendMsgTimeout() {
        return sendMsgTimeout;
    }

    public void setSendMsgTimeout(int sendMsgTimeout) {
        this.sendMsgTimeout = sendMsgTimeout;
    }

    public int getCompressMsgBodyOverHowmuch() {
        return compressMsgBodyOverHowmuch;
    }

    public void setCompressMsgBodyOverHowmuch(int compressMsgBodyOverHowmuch) {
        this.compressMsgBodyOverHowmuch = compressMsgBodyOverHowmuch;
    }

    public int getMaxMessageSize() {
        return maxMessageSize;
    }

    public void setMaxMessageSize(int maxMessageSize) {
        this.maxMessageSize = maxMessageSize;
    }

    public boolean getRetryAnotherBrokerWhenNotStoreOk() {
        return retryAnotherBrokerWhenNotStoreOk;
    }

    public void setRetryAnotherBrokerWhenNotStoreOk(boolean retryAnotherBrokerWhenNotStoreOk) {
        this.retryAnotherBrokerWhenNotStoreOk = retryAnotherBrokerWhenNotStoreOk;
    }

    public int getRetryTimesWhenSendFailed() {
        return retryTimesWhenSendFailed;
    }

    public void setRetryTimesWhenSendFailed(int retryTimesWhenSendFailed) {
        this.retryTimesWhenSendFailed = retryTimesWhenSendFailed;
    }

    private int retryTimesWhenSendFailed;


}
