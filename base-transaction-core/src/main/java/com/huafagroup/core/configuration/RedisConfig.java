package com.huafagroup.core.configuration;


import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisConnectionException;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.redisson.spring.session.config.EnableRedissonHttpSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Configuration
@ConfigurationProperties(prefix = "spring.redisson")
@EnableCaching
@EnableRedissonHttpSession
public class RedisConfig extends CachingConfigurerSupport
{
    /**
     * 日志对象
     */
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    /**
     * 生产key的策略,注意：如果方法参数是引用类型，必须自己实现tostring方法，来区分不同的类实例
     *
     * @return
     */

    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return new KeyGenerator() {
            @Override
            public Object generate(Object target, Method method, Object... params) {
                StringBuilder sb = new StringBuilder();
                sb.append(target.getClass().getName());
                sb.append(method.getName());
                for (Object obj : params) {
                    if(obj!=null) {
                        sb.append(obj.toString());
                    }
                }
                return sb.toString();
            }
        };
    }


    @Lazy
    @Autowired
    RedissonClient redissonClient;


    /**
     * 管理缓存
     *
     * @param redissonClient
     * @return
     */

    @SuppressWarnings("rawtypes")
    @Bean
    @Lazy
    //@DependsOn
    public CacheManager CacheManager() {
        Map<String, CacheConfig> config = new HashMap<String, CacheConfig>();
        //RLock
        config.put("testMap", new CacheConfig(24 * 60 * 1000, 12 * 60 * 1000));

        return new RedissonSpringCacheManager(redissonClient, config);
    }


    @Bean
    public RedissonClient initRedissonClient () {
        Config config = new Config();
        //config.setTransportMode(TransportMode.EPOLL);
        config.setLockWatchdogTimeout(20000);

        JsonJacksonCodec codec=JsonJacksonCodec.INSTANCE;
        codec.getObjectMapper().registerModule(new JavaTimeModule());

        config.setCodec(codec);

        ClusterServersConfig serversConfig=config.useClusterServers();
        if(!(getPassword()==null || getPassword().equals(""))) {
            serversConfig.setPassword(password);
        }
        serversConfig.setReadMode(ReadMode.MASTER_SLAVE);
        serversConfig.setKeepAlive(true);
        //断线重连
        serversConfig.setPingConnectionInterval(4000);
        serversConfig.setMasterConnectionMinimumIdleSize(masterConnectionMinimumIdleSize);
        serversConfig.setSlaveConnectionMinimumIdleSize(slaveConnectionMinimumIdleSize);
        serversConfig.setSubscriptionConnectionMinimumIdleSize(1);
        serversConfig.setSubscriptionConnectionPoolSize(2);
        serversConfig.setScanInterval(1000);
        serversConfig.setSlaveConnectionPoolSize(maxConnectionSize);

        serversConfig.setMasterConnectionPoolSize(maxConnectionSize);
        serversConfig.setIdleConnectionTimeout(20000);
        serversConfig.setConnectTimeout(10000);
        serversConfig.setTimeout(3000);//命令超时
        // 使用 lambda 表达式以及函数操作(functional operation)
        nodeAddresses.forEach(serversConfig::addNodeAddress);
        try {
            return Redisson.create(config);
        }catch (RedisConnectionException e)
        {
            logger.error("redis初始化异常",e);
            return  null;
        }
    }

    private  String readMode;

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    private  String password;

    private  String retryAttempts;

    private  String idleConnectionTimeout;

    public int getMaxConnectionSize() {
        return maxConnectionSize;
    }

    public void setMaxConnectionSize(int maxConnectionSize) {
        this.maxConnectionSize = maxConnectionSize;
    }

    private  int maxConnectionSize;


    private  int masterConnectionMinimumIdleSize;

    private  int slaveConnectionMinimumIdleSize;

    public int getMasterConnectionMinimumIdleSize() {
        return masterConnectionMinimumIdleSize;
    }

    public void setMasterConnectionMinimumIdleSize(int masterConnectionMinimumIdleSize) {
        this.masterConnectionMinimumIdleSize = masterConnectionMinimumIdleSize;
    }

    public int getSlaveConnectionMinimumIdleSize() {
        return slaveConnectionMinimumIdleSize;
    }

    public void setSlaveConnectionMinimumIdleSize(int slaveConnectionMinimumIdleSize) {
        this.slaveConnectionMinimumIdleSize = slaveConnectionMinimumIdleSize;
    }

    private List<String> nodeAddresses;

    public String getReadMode() {
        return readMode;
    }

    public void setReadMode(String readMode) {
        this.readMode = readMode;
    }

    public String getRetryAttempts() {
        return retryAttempts;
    }

    public void setRetryAttempts(String retryAttempts) {
        this.retryAttempts = retryAttempts;
    }

    public String getIdleConnectionTimeout() {
        return idleConnectionTimeout;
    }

    public void setIdleConnectionTimeout(String idleConnectionTimeout) {
        this.idleConnectionTimeout = idleConnectionTimeout;
    }

    public List<String> getNodeAddresses() {
        return nodeAddresses;
    }

    public void setNodeAddresses(List<String> nodeAddresses) {
        this.nodeAddresses = nodeAddresses;
    }


}
