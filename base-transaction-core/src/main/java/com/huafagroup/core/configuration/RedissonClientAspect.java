package com.huafagroup.core.configuration;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2019-06-19 10:27
 **/
@Aspect
@Component
public class RedissonClientAspect {


    @Value("${spring.redisson.keyprefix}")
    private String keyprifix;

    @Around("execution(* org.redisson.api.RedissonClient+.get*(..))")
    public  Object addKeyPrifix(ProceedingJoinPoint joinPoint) throws  Throwable{

        Object[] args = joinPoint.getArgs();//redis  key 统一添加前缀
        if(args.length!=0){
            args[0]=keyprifix.concat("-").concat(args[0].toString());
        }
        Object object=joinPoint.proceed(args);
        return  object;
    }
}
