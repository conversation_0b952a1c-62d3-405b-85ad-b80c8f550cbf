package com.huafagroup.core.util.wx;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class APIV3AesUtil {

    static final int KEY_LENGTH_BYTE = 32;
    static final int TAG_LENGTH_BIT = 128;

    public static String decryptToString(String aesKey,String associatedData, String nonce, String ciphertext)
            throws GeneralSecurityException, IOException {
        return decryptToString(aesKey.getBytes(StandardCharsets.UTF_8),
                associatedData.getBytes(StandardCharsets.UTF_8),nonce.getBytes(StandardCharsets.UTF_8),
                ciphertext);
    }

    public static String decryptToString(byte[] aesKey,byte[] associatedData, byte[] nonce, String ciphertext)
            throws GeneralSecurityException, IOException {
        if (aesKey.length != KEY_LENGTH_BYTE) {
            throw new IllegalArgumentException("无效的ApiV3Key，长度必须为32个字节");
        }
        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
 
            SecretKeySpec key = new SecretKeySpec(aesKey, "AES");
            GCMParameterSpec spec = new GCMParameterSpec(TAG_LENGTH_BIT, nonce);
 
            cipher.init(Cipher.DECRYPT_MODE, key, spec);
            cipher.updateAAD(associatedData);
 
            return new String(cipher.doFinal(Base64.getDecoder().decode(ciphertext)), "utf-8");
        } catch (NoSuchAlgorithmException | NoSuchPaddingException e) {
            throw new IllegalStateException(e);
        } catch (InvalidKeyException | InvalidAlgorithmParameterException e) {
            throw new IllegalArgumentException(e);
        }
    }

    public static void main(String[] args) throws IOException, GeneralSecurityException {
        //key为APIv3密钥
        byte[] key = "SystemConst.WX_KEY".getBytes("UTF-8");
        String assc = "";
        String noce = "";
        String cip = "";
        String decryptToString = decryptToString(key,assc.getBytes("UTF-8"),noce.getBytes("UTF-8"),cip);
        System.out.println(decryptToString);
    }
}