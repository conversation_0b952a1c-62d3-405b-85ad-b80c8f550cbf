package com.huafagroup.core.util;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2019/1/11 下午3:36
 **/

public class SerialUtil {
    private static final String NUM_SYMBOLS = "0123456789"; // 数字

    // 字符串
    private static final String SYMBOLS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    private static final Random random = new SecureRandom();

    public static void main(String[] args) {
        System.out.println(getNonceStr(32));
        System.out.println(getNonceStr(32));
    }

    /**
     * 获取指定长度的随机数字
     *
     * @return 随机数字
     * @date 修改日志：由 space 创建于 2018-8-2 下午2:43:51
     */
    public static String getNonceNumStr(int len) {
        StringBuilder sb = new StringBuilder(len);
        for (int index = 0; index < len; ++index) {
            sb.append(NUM_SYMBOLS.charAt(random.nextInt(NUM_SYMBOLS.length())));
        }
        return sb.toString();
    }

    /**
     * 获取指定长度的随机字符（大小写字母+数字）
     * @param len
     * @return
     */
    public static String getNonceStr(int len) {
        StringBuilder sb = new StringBuilder(len);
        for (int index = 0; index < len; ++index) {
            sb.append(SYMBOLS.charAt(random.nextInt(SYMBOLS.length())));
        }
        return sb.toString();
    }

    /**
     * 生成yyyyMMddHHmmss  格式当前日期字符串
     *
     * @return yyyyMMddHHmmss  格式当前日期字符串
     * @date 修改日志：由 huangzhixian 创建
     */
    public static String getCurrentDateTimeString(){
        Date d = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateNowStr = sdf.format(d);
        return dateNowStr;

    }

    /**
     * 生成应收账单单号
     *
     * @return YS加yyyyMMddHHmmss 加四位随机数 格式的 应收账单单号
     * @date 修改日志：由 huangzhixian 创建
     */
    public static String getBillReceivableId(){

        return "YS"+getCurrentDateTimeString()+getNonceNumStr(4);

    }


    /**
     * 生成批量导入应收账单批次号
     *
     * @return PC加yyyyMMddHHmmss 加四位随机数 格式的 应收账单批次号
     * @date 修改日志：由 huangzhixian 创建
     */
    public static String getBillReceivableBatchNumber(){

        return "PC"+getCurrentDateTimeString()+getNonceNumStr(4);

    }


}
