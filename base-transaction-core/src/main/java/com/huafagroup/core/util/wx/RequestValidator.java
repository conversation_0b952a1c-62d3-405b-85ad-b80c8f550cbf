package com.huafagroup.core.util.wx;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.wechat.pay.contrib.apache.httpclient.auth.PublicKeyVerifier;
import com.wechat.pay.contrib.apache.httpclient.notification.Notification;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationHandler;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationRequest;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.RequestEntity;

import java.nio.charset.StandardCharsets;
import java.time.DateTimeException;
import java.time.Duration;
import java.time.Instant;
import java.util.Objects;

/**
 * 对微信的请求做签名校验
 */
public class RequestValidator {

    static Logger logger = LoggerFactory.getLogger(RequestValidator.class);

    static RuntimeException parameterError(String message, Object... args) {
        message = String.format(message, args);
        return new IllegalArgumentException("parameter error: " + message);
    }

    public static Notification validate(RequestEntity<ObjectNode> request, MerchantProperties merchantProperties) throws Exception {
            validateParameters(request);
            ObjectNode body = request.getBody();
            String content = Objects.isNull(body) ? "" : body.toString();
            HttpHeaders headers = request.getHeaders();
            NotificationRequest notificationRequest = new NotificationRequest.Builder()
                    .withSerialNumber(headers.getFirst("Wechatpay-Serial"))
                    .withNonce(headers.getFirst("Wechatpay-Nonce"))
                    .withTimestamp(headers.getFirst("Wechatpay-Timestamp"))
                    .withSignature(headers.getFirst("Wechatpay-Signature"))
                    .withBody(content)
                    .build();
            NotificationHandler handler = new NotificationHandler(new PublicKeyVerifier(merchantProperties.getWechatPayPublicKeyId(),
                    PemUtil.loadPublicKey(merchantProperties.getWechatPayPublicKey())),
                    merchantProperties.getApiV3Key().getBytes(StandardCharsets.UTF_8));
            // 验签和解析请求体
        return handler.parse(notificationRequest);
    }

    protected static void validateParameters(RequestEntity request) {
        HttpHeaders headers = request.getHeaders();

        if (!headers.containsKey("Wechatpay-Serial")) {
            throw parameterError("empty Wechatpay-Serial");
        } else if (!headers.containsKey("Wechatpay-Signature")) {
            throw parameterError("empty Wechatpay-Signature");
        } else if (!headers.containsKey("Wechatpay-Timestamp")) {
            throw parameterError("empty Wechatpay-Timestamp");
        } else if (!headers.containsKey("Wechatpay-Nonce")) {
            throw parameterError("empty Wechatpay-Nonce");
        } else {
            String timestamp = headers.getFirst("Wechatpay-Timestamp");

            try {
                Instant instant = Instant.ofEpochSecond(Long.parseLong(timestamp));
                if (Duration.between(instant, Instant.now()).abs().toMinutes() >= 5L) {
                    throw parameterError("timestamp=[%s] expires", timestamp);
                }
            } catch (NumberFormatException | DateTimeException var5) {
                throw parameterError("invalid timestamp=[%s]", timestamp);
            }
        }
    }
}
