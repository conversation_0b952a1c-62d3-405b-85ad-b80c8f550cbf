package com.huafagroup.core.util;


import com.huafagroup.core.exception.BusinessException;
import com.wuwenze.poi.ExcelKit;
import com.wuwenze.poi.handler.ExcelReadHandler;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


public class ExcelUtil {
    public static void exportExcel(List<?> list, Class<?> pojoClass, int maxSheetRecords, HttpServletResponse response, Boolean isTemplate) {
        ExcelKit.$Export(pojoClass, response).setMaxSheetRecords(maxSheetRecords).downXlsx(list, isTemplate); //500000 false

      /*  ExcelKit.$Export(pojoClass, response).downXlsx(list, true);*/
    }
    public static  <T> void importExcel(MultipartFile file, Class<T> pojoClass,ExcelReadHandler<T> excelReadHandler) {
        try {
            ExcelKit.$Import(pojoClass)
                    .readXlsx(file.getInputStream(),excelReadHandler
                   );
        } catch (Exception e) {
            throw new BusinessException("5102", e.getMessage());
        }

    }


}
