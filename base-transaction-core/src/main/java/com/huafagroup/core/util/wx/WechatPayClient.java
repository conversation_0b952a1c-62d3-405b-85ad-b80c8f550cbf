package com.huafagroup.core.util.wx;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.huafagroup.core.dto.wx.MerchantProperties;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.huafagroup.core.service.assist.HttpBaseCommand;
import com.huafagroup.core.service.assist.ProxyHost;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.IOException;

import static okhttp3.RequestBody.create;

/**
 * 微信支付客户端
 */
public class WechatPayClient {

    static Logger logger = LoggerFactory.getLogger(WechatPayClient.class);

    public static <R> R doPost(HttpBaseCommand command) throws Exception {
        //请求URL
        HttpPost httpPost = new HttpPost(command.getDomain().concat(command.getApi()));
        httpPost.addHeader("Content-type", "application/json;charset=utf-8");

        String reqStr = command.prepareParams();

        logger.info("发送请求参数为：{}",reqStr);

        StringEntity entity = new StringEntity(reqStr, ContentType.create("application/json", "utf-8"));

        httpPost.setEntity(entity);

        return doRequest(command, httpPost);
    }


    public static <R> R doGet(HttpBaseCommand command) throws Exception {
        //请求URL
        HttpGet httpGet = new HttpGet (command.getDomain().concat(command.getApi()));

        String reqStr = command.prepareParams();

        logger.info("发送请求参数为【实际在url中传入，此地方仅为打印输出】：{}",reqStr);

        return doRequest(command, httpGet);
    }

    public static String unifiedOrder(OkHttpClient okHttpClient, String url, String xmlParam){
        logger.info("请求地址:{}", url);
        logger.info("请求参数:{}", xmlParam);
        try {
            //设置请求body
            RequestBody requestBody = create(MediaType.get("application/xml"), xmlParam);
            //构建请求
            Request request = new Request.Builder().url(url).addHeader("Content-Type", "application/json;charset=utf-8").post(requestBody).build();
            Response response = okHttpClient.newCall(request).execute();
            if (response.code() != 200) {
                logger.error("调用微信支付接口返回code{}，异常:{}", response.code(), response.message());
                new BusinessException(StringUtils.EMPTY + response.code(), "创建订单失败：{}", response.message());
            }
            String jsonStr = response.body().string();
            logger.info("调用微信支付接口返回:{}", jsonStr);
            return jsonStr;
        } catch (Exception e) {
            logger.error("调用微信支付接口报错:{}", e.getMessage());
            new BusinessException(ExceptionEnum.SERVER_ERROR.getCode(), "调用微信统一下单接口报错:" + e.getMessage());
        }
        return null;
    }

    private static <R> R doRequest(HttpBaseCommand command, HttpRequestBase httpRequest) throws IOException {
        httpRequest.addHeader("Accept", "application/json");
        CloseableHttpClient httpClient = getHttpClient(command);
        //完成签名并执行请求
        CloseableHttpResponse response = httpClient.execute(httpRequest);

        int statusCode = response.getStatusLine().getStatusCode();
        JSONObject resultJson = new JSONObject();
        if (statusCode != HttpStatus.NO_CONTENT.value()) {
            String result = EntityUtils.toString(response.getEntity());
            logger.info("响应结果为：{}",result);
            resultJson = JSONUtil.parseObj(result);
        }

        response.close();
        httpClient.close();

        //todo test
        /**
        statusCode = 200;
        resultJson = JSONUtil.parseObj("{" +
                "\"amount\": {" +
                "\"currency\": \"CNY\"," +
                "\"discount_refund\": 0," +
                "\"payer_refund\": 1," +
                "\"payer_total\": 1," +
                "\"refund\": 1," +
                "\"settlement_refund\": 1," +
                "\"settlement_total\": 1," +
                "\"total\": 1" +
                "}," +
                "\"channel\": \"ORIGINAL\"," +
                "\"create_time\": \"2021-03-26T14:47:45+08:00\"," +
                "\"funds_account\": \"UNSETTLED\"," +
                "\"out_refund_no\": \"sdk12345678920210326144648\"," +
                "\"out_trade_no\": \"sdk12345678920210326144648\"," +
                "\"promotion_detail\": []," +
                "\"refund_id\": \"50000807732021032607482875770\"," +
                "\"status\": \"PROCESSING\"," +
                "\"transaction_id\": \"4200000890202103265892063419\"," +
                "\"user_received_account\": \"支付用户零钱\"" +
                "}");
        */
        ResponseEntity<JSONObject> responseEntity = new ResponseEntity<>(resultJson, HttpStatus.resolve(statusCode));
        Object r = command.processResult(responseEntity);
        return (R)r;
    }

    private static CloseableHttpClient getHttpClient(HttpBaseCommand command) {
        MerchantProperties merchantProperties = command.getMerchantProperties();

        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
                .withMerchant(merchantProperties.getMchId(), merchantProperties.getMchSerialNo(), PemUtil.loadPrivateKey(merchantProperties.getPrivateKey()))
                .withWechatPay(merchantProperties.getWechatPayPublicKeyId(), PemUtil.loadPublicKey(merchantProperties.getWechatPayPublicKey()));
        //看是否使用代理
        if (command instanceof ProxyHost && ((ProxyHost)command).getProxyHost() != null) {
            builder.setProxy(((ProxyHost)command).getProxyHost());
        }
        return builder.build();
    }

    /**
     * 以xml方式提交请求，主要是微信v2版本的
     * @param command
     * @param <R>
     * @return
     * @throws Exception
     */
    public static <R> R doPostV2(HttpBaseCommand command) throws Exception {
        String xmlStr = command.prepareParams();
        String url = command.getDomain().concat(command.getApi());
        HttpPost httpPost = new HttpPost(url);
        StringEntity postEntity = new StringEntity(xmlStr, "UTF-8");
        httpPost.addHeader("Content-Type", "text/xml");
        httpPost.setEntity(postEntity);
        logger.info("发送请求参数为：{}",xmlStr);
        HttpResponse httpResponse = getHttpClientV2(command).execute(httpPost);
        HttpEntity httpEntity = httpResponse.getEntity();

        String result = EntityUtils.toString(httpEntity, "UTF-8");

        logger.info("响应结果为：{}",result);

        JSONObject resultJson = JSONUtil.xmlToJson(result);
        if (resultJson.containsKey("xml")) {
            resultJson = resultJson.getJSONObject("xml");
        }
        int statusCode = 200;//表明调用过程成功，业务有异常，会通过参数返回
        ResponseEntity<JSONObject> responseEntity = new ResponseEntity<>(resultJson, HttpStatus.resolve(statusCode));
        Object r = command.processResult(responseEntity);
        return (R)r;

    }

    private static HttpClient getHttpClientV2(HttpBaseCommand command){
        return getHttpClient(command);
    }
}
