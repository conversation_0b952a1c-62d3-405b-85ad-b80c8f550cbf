package com.huafagroup.core.util;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PageBean<T> implements Serializable {
    private  int pageNum;
    private  int pageSize;
    private long total;
    private List<T> records;

    public PageBean(int pageNum, int pageSize, long total, List<T> records) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.records = records;
    }
}
