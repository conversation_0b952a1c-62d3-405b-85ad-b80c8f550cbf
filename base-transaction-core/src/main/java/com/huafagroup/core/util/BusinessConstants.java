package com.huafagroup.core.util;

/**
 * @Auther: chenyanwu
 * @Date: 2019/2/25 12:46
 * @Description:
 * @Version 1.0
 */
public class BusinessConstants {
    // 用户分类类型：供应商 correlator_category(type)
    public final static Integer CATEGORYS_TYPE_SUPPLIER = 0;

    // 用户分类类型：客户
    public final static Integer CATEGORYS_TYPE_CUSTOM = 1;

    // 用户类型：讲师 correlator_tag(type)
    public final static Integer USER_TYPE_TEACHER= 1;

    // 用户类型：方案
    public final static Integer USER_TYPE_SCHEME = 2;

    // 用户类型：培训项目
    public final static Integer USER_TYPE_TRAINING_PROJECT = 3;

    // 用户类型：咨询项目
    public final static Integer USER_TYPE_CONSULT_PROJECT = 4;

    // 用户类型：咨询其他项目
    public final static Integer USER_TYPE_CONSULT_OTHER_PROJECT = 5;

    // 文件类型：证书文件 attach_file(type)
    public final static Integer FILE_TYPE_CERTIFICATE = 1;

    // 文件类型：供应商附件
    public final static Integer FILE_TYPE_SUPPLIER = 2;

    // 文件类型：筹备阶段文件模板(培训)
    public final static Integer FILE_TYPE_TRAINING_BEFORE = 3;

    // 文件类型：实施过程文件(培训)
    public final static Integer FILE_TYPE_TRAINING_DONE = 4;

    // 文件类型：效果评估报告(培训)
    public final static Integer FILE_TYPE_TRAINING_RESULT = 5;

    // 文件类型：试题上传(咨询)
    public final static Integer FILE_TYPE_CONSULTING_EXAM = 6;

    // 文件类型：筹备阶段文件模板(咨询)
    public final static Integer FILE_TYPE_CONSULTING_BEFORE = 7;

    // 文件类型：实施过程文件(咨询)
    public final static Integer FILE_TYPE_CONSULTING_DONE = 8;

    // 文件类型：项目方案(咨询)
    public final static Integer FILE_TYPE_CONSULTING_OTHER_SCHEME = 9;

    // 文件类型：成果文件(咨询)
    public final static Integer FILE_TYPE_CONSULTING_OTHER_FILE = 10;

    // 文件类型：效果评估报告(咨询)
    public final static Integer FILE_TYPE_CONSULTING_OTHER_RESULT = 11;

    // 文件类型：呈文(内部流程)
    public final static Integer FILE_TYPE_INTERNAL_FACTA = 12;

    // 文件类型：合同(内部流程)
    public final static Integer FILE_TYPE_INTERNAL_CONTRACT = 13;

    // 文件类型：方案
    public final static Integer FILE_TYPE_SCHEME = 14;

    // 项目类型：年度项目
    public final static Integer PROGRAM_TYPE_YEAR = 1;

    // 项目类型：单个项目
    public final static Integer PROGRAM_TYPE_SINGLE = 2;

    // 项目类型：系列项目
    public final static Integer PROGRAM_TYPE_SERIES = 3;

    // 项目所属：集团内业务
    public final static Integer PROGRAM_BELONG_INNER = 1;

    // 项目所属：集团外业务
    public final static Integer PROGRAM_BELONG_OUTER = 2;

    // 项目状态：正常开展
    public final static Integer PROGRAM_STATE_DONE = 1;

    // 项目状态：延期
    public final static Integer PROGRAM_STATE_DELAY = 2;

    // 项目状态：终止
    public final static Integer PROGRAM_STATE_TERMINATION = 3;

    // 项目状态：其他
    public final static Integer PROGRAM_STATE_OTHER = 4;

}
