package com.huafagroup.core.util;

import com.huafagroup.core.exception.ExceptionEnum;

import java.io.Serializable;

/*
* 定义统一标准的接口规范
* */
public class ResultBean<T> implements Serializable{

    private String code= ExceptionEnum.SUCCESS.getCode();

    /**
     * 消息内容
     */
    private String msg="success";

    /**
     数据内容
     */
    private  T data;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }





    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    @Deprecated
    public ResultBean(ExceptionEnum exceptionEnum, String errStr, String message, T data) {

        this.code=exceptionEnum.getCode();
        this.msg=message;
        this.data=data;
    }
    public ResultBean(ExceptionEnum exceptionEnum, String message, T data) {

        this.code=exceptionEnum.getCode();
        this.msg=message;
        this.data=data;
    }



    public ResultBean(){}
    public ResultBean(T data){
        super();

        this.data=data;
    }
    public ResultBean(Throwable e){
        this.code= ExceptionEnum.SERVER_ERROR.getCode();

        this.msg=e.getMessage();
    }
}
