package com.huafagroup.core.util;

import cn.hutool.http.HttpUtil;
import com.google.common.collect.Maps;
import com.huafagroup.core.entity.uc.UserBasic;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.Map;

public class ToolUtil {

    public static final Logger LOGGER = LoggerFactory.getLogger(ToolUtil.class);

    /**
     * 设定安全的密码，生成随机的salt并经过1024次 sha-1 hash
     */
    public static void entryptPassword(UserBasic user) {
        byte[] salt = Digests.generateSalt(Constants.SALT_SIZE);
        user.setSalt(Encodes.encodeHex(salt));
        byte[] hashPassword = Digests.sha1(user.getPassword().getBytes(), salt, Constants.HASH_INTERATIONS);
        user.setPassword(Encodes.encodeHex(hashPassword));
    }

    /**
     * @param paramStr 输入需要加密的字符串
     * @return
     */
    public static String entryptPassword(String paramStr, String salt) {
        if (StringUtils.isNotEmpty(paramStr)) {
            byte[] saltStr = Encodes.decodeHex(salt);
            byte[] hashPassword = Digests.sha1(paramStr.getBytes(), saltStr, Constants.HASH_INTERATIONS);
            String password = Encodes.encodeHex(hashPassword);
            return password;
        } else {
            return null;
        }

    }

    /**
     * 获取客户端的ip信息
     *
     * @param request
     * @return
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        LOGGER.info("ipadd : " + ip);
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Forwarded-For");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknow".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        LOGGER.info(" ip --> " + ip);
        return ip;
    }

    /**
     * 将bean转换成map
     *
     * @param condition
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> convertBeanToMap(Object condition) {
        if (condition == null) {
            return null;
        }
        if (condition instanceof Map) {
            return (Map<String, Object>) condition;
        }
        Map<String, Object> objectAsMap = new HashMap<String, Object>();
        BeanInfo info = null;
        try {
            info = Introspector.getBeanInfo(condition.getClass());
        } catch (IntrospectionException e) {
            LOGGER.error(e.getMessage());
            e.printStackTrace();
        }

        if (info == null) {
            return null;
        }
        for (PropertyDescriptor pd : info.getPropertyDescriptors()) {
            Method reader = pd.getReadMethod();
            if (reader != null && !"class".equals(pd.getName())) {
                try {
                    objectAsMap.put(pd.getName(), reader.invoke(condition));
                } catch (Exception e) {
                    e.printStackTrace();
                    LOGGER.error(e.getMessage());
                }
            }

        }
        return objectAsMap;
    }

    /**
     * 通过文件名判断并获取OSS服务文件上传时文件的contentType
     *
     * @param fileName 文件名
     * @return 文件的contentType
     */
    public static String getContentType(String fileName) {
        int d = fileName.lastIndexOf(".");
        if (d == -1) {
            return "text/html";
        }
        String fileExtension = fileName.substring(fileName.lastIndexOf("."));
        switch (fileExtension) {
            case ".bmp":
                return "image/bmp";
            case ".gif":
                return "image/gif";
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".html":
                return "text/html";
            case ".txt":
                return "text/txt";
            case ".vsd":
                return "application/vnd.visio";
            case ".ppt":
            case ".pptx":
                return "application/vnd.ms-powerpoint";
            case ".docx":
            case ".doc":
                return "application/msword";
            case ".xml":
                return "text/xml";
            default:
                return "text/html";
        }
    }

    /***
     * 腾讯WebService API
     * http://lbs.qq.com/webservice_v1/guide-ip.html
     * @param ip
     * @return
     */
    public static HashMap<String, String> getAddressByIP(String ip) {
        if ("0:0:0:0:0:0:0:1".equals(ip)) {
            ip = "0.0.0.0";
        }
        HashMap<String, String> map = Maps.newHashMap();
        StringBuilder sb = new StringBuilder("https://apis.map.qq.com/ws/location/v1/ip?key=N7XBZ-NX764-OFOUH-D5LJY-KZ3QK-6WFNX&ip=");
        sb.append(ip);
        String result = HttpUtil.get(sb.toString(), Charset.forName("UTF-8"));
        HashMap resultMap = JsonUtil.json2Bean(result, HashMap.class);
        Integer status = (Integer) resultMap.get("status");
        HashMap finalMap = Maps.newHashMap();
        if (status == 0) {
            HashMap m = (HashMap) resultMap.get("result");
            HashMap<String, String> detail = (HashMap<String, String>) m.get("ad_info");
            String area = detail.get("nation");
            String isp = "";
            String province = detail.get("province");
            String city = detail.get("city");
            finalMap.put("isp", isp);
            if (StringUtils.isNotBlank(area)) {
                finalMap.put("area", area);
            } else {
                finalMap.put("area", "");
            }
            if (StringUtils.isNotBlank(province)) {
                finalMap.put("province", province);
            } else {
                finalMap.put("province", "");
            }
            if (StringUtils.isNotBlank(city)) {
                finalMap.put("city", city);
            } else {
                finalMap.put("city", "");
            }
        } else {
            finalMap.put("area", "未知");
            finalMap.put("isp", "未知");
            finalMap.put("province", "未知");
            finalMap.put("city", "未知");
        }
        return finalMap;
    }

    /**
     * 判断请求是否是ajax请求
     *
     * @param request
     * @return
     */
    public static boolean isAjax(HttpServletRequest request) {
        String accept = request.getHeader("accept");
        return accept != null && accept.contains("application/json") || (request.getHeader("X-Requested-With") != null && request.getHeader("X-Requested-With").contains("XMLHttpRequest"));
    }

    /**
     * 获取操作系统,浏览器及浏览器版本信息
     *
     * @param request
     * @return
     */
    public static HashMap<String, String> getOsAndBrowserInfo(HttpServletRequest request) {
        HashMap<String, String> map = Maps.newHashMap();
        String browserDetails = request.getHeader("User-Agent");
        String userAgent = browserDetails;
        String user = userAgent.toLowerCase();

        String os = "";
        String browser = "";

        //=================OS Info=======================
        if (userAgent.toLowerCase().contains("windows")) {
            os = "Windows";
        } else if (userAgent.toLowerCase().contains("mac")) {
            os = "Mac";
        } else if (userAgent.toLowerCase().contains("x11")) {
            os = "Unix";
        } else if (userAgent.toLowerCase().contains("android")) {
            os = "Android";
        } else if (userAgent.toLowerCase().contains("iphone")) {
            os = "IPhone";
        } else {
            os = "UnKnown, More-Info: " + userAgent;
        }
        //===============Browser===========================
        if (user.contains("edge")) {
            browser = (userAgent.substring(userAgent.indexOf("Edge")).split(" ")[0]).replace("/", "-");
        } else if (user.contains("msie")) {
            String substring = userAgent.substring(userAgent.indexOf("MSIE")).split(";")[0];
            browser = substring.split(" ")[0].replace("MSIE", "IE") + "-" + substring.split(" ")[1];
        } else if (user.contains("safari") && user.contains("version")) {
            browser = (userAgent.substring(userAgent.indexOf("Safari")).split(" ")[0]).split("/")[0]
                    + "-" + (userAgent.substring(userAgent.indexOf("Version")).split(" ")[0]).split("/")[1];
        } else if (user.contains("opr") || user.contains("opera")) {
            if (user.contains("opera")) {
                browser = (userAgent.substring(userAgent.indexOf("Opera")).split(" ")[0]).split("/")[0]
                        + "-" + (userAgent.substring(userAgent.indexOf("Version")).split(" ")[0]).split("/")[1];
            } else if (user.contains("opr")) {
                browser = ((userAgent.substring(userAgent.indexOf("OPR")).split(" ")[0]).replace("/", "-"))
                        .replace("OPR", "Opera");
            }

        } else if (user.contains("chrome")) {
            browser = (userAgent.substring(userAgent.indexOf("Chrome")).split(" ")[0]).replace("/", "-");
        } else if ((user.contains("mozilla/7.0")) || (user.contains("netscape6")) ||
                (user.contains("mozilla/4.7")) || (user.contains("mozilla/4.78")) ||
                (user.contains("mozilla/4.08")) || (user.contains("mozilla/3"))) {
            browser = "Netscape-?";

        } else if (user.contains("firefox")) {
            browser = (userAgent.substring(userAgent.indexOf("Firefox")).split(" ")[0]).replace("/", "-");
        } else if (user.contains("rv")) {
            String IEVersion = (userAgent.substring(userAgent.indexOf("rv")).split(" ")[0]).replace("rv:", "-");
            browser = "IE" + IEVersion.substring(0, IEVersion.length() - 1);
        } else {
            browser = "UnKnown, More-Info: " + userAgent;
        }
        map.put("os", os);
        map.put("browser", browser);
        return map;
    }

}
