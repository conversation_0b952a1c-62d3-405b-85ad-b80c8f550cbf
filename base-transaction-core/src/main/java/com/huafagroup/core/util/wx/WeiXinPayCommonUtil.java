package com.huafagroup.core.util.wx;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;

public class WeiXinPayCommonUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(WeiXinPayCommonUtil.class);

    public static String createSign(String key, String characterEncoding, SortedMap<String, Object> parameters) {
        StringBuffer sb = new StringBuffer();
        Set es = parameters.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            Object v = entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k) && !"key".equals(k)) {
                sb.append(k + "=" + v + "&");
            }
        }
        // 最后加密时添加商户密钥，由于key值放在最后，所以不用添加到SortMap里面去，单独处理，编码方式采用UTF-8
        sb.append("key=" + key);
        LOGGER.info("签名字符串：{}", sb.toString());
        String sign = WeiXinMD5Util.MD5Encode(sb.toString(), characterEncoding).toUpperCase();
        return sign;
    }

    public static String getRequestXml(Map<String, Object> parameters) {

        StringBuffer sb = new StringBuffer();

        sb.append("<xml>");

        Set es = parameters.entrySet();

        Iterator it = es.iterator();

        while (it.hasNext()) {

            Map.Entry entry = (Map.Entry) it.next();

            String k = (String) entry.getKey();

            Object v = entry.getValue();

            if ("sign".equalsIgnoreCase(k)) {


            } else if ("attach".equalsIgnoreCase(k) || "body".equalsIgnoreCase(k)) {

                sb.append("<" + k + ">" + "<![CDATA[" + v + "]]></" + k + ">");

            } else {

                sb.append("<" + k + ">" + v + "</" + k + ">");

            }

        }

        sb.append("<" + "sign" + ">" + parameters.get("sign") + "</" + "sign" + ">");

        sb.append("</xml>");

        return sb.toString();

    }
}
