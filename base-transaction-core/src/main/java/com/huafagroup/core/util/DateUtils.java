package com.huafagroup.core.util;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日期工具类
 */
public class DateUtils {

    //rfc3339标准格式
    static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");

    public static String toRfc3339Str(Date date){
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(date.toInstant(),
                ZoneId.of("Asia/Shanghai"));
        return zonedDateTime.format(formatter);
    }

    public static Date fromRfc3339Str(String dateStr){
        ZonedDateTime dateTime = ZonedDateTime.parse(dateStr, formatter);
        Date date = Date.from(dateTime.toInstant());
        return date;
    }
}
