package com.huafagroup.core.util.wx;

import cn.hutool.core.io.IoUtil;
import com.huafagroup.core.entity.trans.Merchant;
import com.huafagroup.core.exception.BusinessException;
import com.huafagroup.core.exception.ExceptionEnum;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import sun.security.util.Debug;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.X509Certificate;
import java.util.Base64;
import java.util.Enumeration;

/**
 * 使用商户私钥进行签名
 */
public class PKSigner {

    static Logger logger = LoggerFactory.getLogger(PKSigner.class);
    /**
     * 使用商户私钥对待签名串进行SHA256 with RSA签名，并对签名结果进行Base64编码得到签名值
     * @param message 待签名串
     * @param privateKeyStr 商户私钥
     * @return
     */
    public static String sign(String message,String privateKeyStr) {
        try {
            PrivateKey privateKey = PemUtil
                    .loadPrivateKey(new ByteArrayInputStream(privateKeyStr.getBytes("utf-8")));
            Signature sign = Signature.getInstance("SHA256withRSA");
            sign.initSign(privateKey);
            sign.update(message.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(sign.sign());
        } catch (NoSuchAlgorithmException var3) {
            throw new RuntimeException("当前Java环境不支持SHA256withRSA", var3);
        } catch (SignatureException var4) {
            throw new RuntimeException("签名计算失败", var4);
        } catch (InvalidKeyException var5) {
            throw new RuntimeException("无效的私钥", var5);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("不支持的编码格式", e);
        }
    }

    public static void genPrivateKeyInfo(Merchant merchant) {
        try {
            InputStream inputStream = getCertInputStream(merchant.getCertificateUrl());
            char[] pem = merchant.getMchId().toCharArray();
            KeyStore ks = KeyStore.getInstance("PKCS12");
            ks.load(inputStream, pem);
            Enumeration enums = ks.aliases();
            String keyAlias = null;
            if (enums.hasMoreElements()) // we are readin just one certificate.
            {
                keyAlias = (String) enums.nextElement();
            }
            PrivateKey priKey = (PrivateKey) ks.getKey(keyAlias, pem);
            X509Certificate cert = (X509Certificate) ks.getCertificate(keyAlias);
            String serialNum = Debug.toHexString(cert.getSerialNumber()).toUpperCase().replaceAll(" ", "");
            String priKeyStr = Base64.getEncoder().encodeToString(priKey.getEncoded());
            merchant.setMchSerialNo(serialNum);
            merchant.setPrivateKey(priKeyStr);
        } catch (Exception e) {
            logger.error("PKSigner.genPrivateKeyInfo error, ",e);
            throw new BusinessException(ExceptionEnum.PARAMS_NOT_VALID.getCode(), "商户证书有误，请重新上传");
        }
    }

    private static InputStream getCertInputStream(String certificateUrl) throws IOException {
        InputStream inputStream = null;
        //如果是http链接
        if (certificateUrl.startsWith("http")) {
            URLConnection connection = new URL(certificateUrl).openConnection();
            connection.connect();
            inputStream = connection.getInputStream();
        } else {
            ClassPathResource resource = new ClassPathResource(certificateUrl);
            inputStream = resource.getInputStream();
        }
        return inputStream;
    }

    public static void genWechatPayPublicKeyInfo(Merchant merchant) {
        try{
            String wechatPayPublicKey = IoUtil.read(getCertInputStream(merchant.getWechatPayPublicKeyUrl()),"UTF-8");
            merchant.setWechatPayPublicKey(wechatPayPublicKey);
        } catch (Exception e) {
            logger.error("PKSigner.genWechatPayPublicKeyInfo error, ",e);
            throw new BusinessException(ExceptionEnum.PARAMS_NOT_VALID.getCode(), "微信支付公钥有误，请重新上传");
        }
    }
}
