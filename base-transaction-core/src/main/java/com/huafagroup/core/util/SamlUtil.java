package com.huafagroup.core.util;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.RandomStringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import javax.xml.crypto.dom.DOMStructure;
import javax.xml.crypto.dsig.Reference;
import javax.xml.crypto.dsig.XMLSignature;
import javax.xml.crypto.dsig.XMLSignatureFactory;
import javax.xml.crypto.dsig.dom.DOMValidateContext;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.InputStream;
import java.io.StringReader;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.Deflater;

public class SamlUtil {

    public static PublicKey init(ClassLoader classLoader) {
        CertificateFactory cf;
        InputStream in2;
        java.security.cert.Certificate c2 = null;
        try {
            cf = CertificateFactory.getInstance("X.509");
            in2 = classLoader.getResourceAsStream("siam.pem");
            c2 = cf.generateCertificate(in2);
        } catch (CertificateException e) {
            e.printStackTrace();
        }
        return c2.getPublicKey();
    }

    public static String validate(String responseString, PublicKey pubKey) throws Exception {
        StringReader sr = new StringReader(responseString);
        String loginName = "";
        InputSource is = new InputSource(sr);
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
        Document doc = dbf.newDocumentBuilder().parse(is);
        // Search the Signature element
        NodeList nl = doc.getElementsByTagNameNS(XMLSignature.XMLNS, "Signature");
        if (nl.getLength() == 0) {
            throw new Exception("Cannot find Signature element");
        }
        Node signatureNode = nl.item(0);
        XMLSignatureFactory fac = XMLSignatureFactory.getInstance("DOM");
        XMLSignature signature = fac.unmarshalXMLSignature(new DOMStructure(signatureNode));
        // Create ValidateContext
        DOMValidateContext valCtx = new DOMValidateContext(pubKey, signatureNode);
        // Validate the XMLSignature
        boolean coreValidity = signature.validate(valCtx);
        // Check core validation status
        if (coreValidity == false) {
            // Check the signature validation status
            boolean sv = signature.getSignatureValue().validate(valCtx);
            // check the validation status of each Reference
            List refs = signature.getSignedInfo().getReferences();
            for (int i = 0; i < refs.size(); i++) {
                Reference ref = (Reference) refs.get(i);
                boolean refValid = ref.validate(valCtx);
            }
        } else {
            // 获取登录账号节点信息
            NodeList node = doc.getElementsByTagName("NameID");
            Element e = (Element) node.item(0);
            loginName = e.getTextContent().trim();
            System.out.println("loginName:" + loginName);
        }
        return loginName;
    }

    public static String samlParams(String clientRedirectUrl) {
        Map<String, String> params = new HashMap<>();
        String samlRequest = "";
        // 设置当前访问时间
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss'Z'");
        Date now = new Date();
        // 设置samlRequest信息
        String samlRequestString = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><samlp:AuthnRequest xmlns:samlp=\"urn:oasis:names:tc:SAML:2.0:protocol\" ID=\"" + RandomStringUtils.random(40, true, false) + "\" Version=\"2.0\" IssueInstant=\"" + fmt.format(now) + "\" ProtocolBinding=\"urn:oasis:names.tc:SAML:2.0:bindings:HTTP-Redirect\" ProviderName=\"samldemo\" AssertionConsumerServiceURL=\"" + clientRedirectUrl + "\"/>";
        // 将samlRequest信息转码
        samlRequest = encoding(samlRequestString);
        // 传递参数至页面，接入应用可将此步骤直接Post认证页面
        params.put("SAMLRequest", samlRequest);
        params.put("RelayState", clientRedirectUrl);
        return "?" + getUrlParamsFromMap(params);
    }

    public static String getUrlParamsFromMap(Map<String, String> map) {
        try {
            if (null != map) {
                StringBuilder stringBuilder = new StringBuilder();
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    stringBuilder.append(URLEncoder.encode(entry.getKey(), "UTF-8")).append("=").append(URLEncoder.encode(entry.getValue(), "UTF-8")).append("&");
                }
                String content = stringBuilder.toString();
                if (content.endsWith("&")) {
                    content = org.apache.commons.lang.StringUtils.substringBeforeLast(content, "&");
                }
                return content;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String encoding(String originalString) {
        String samlRequest = originalString;
        String outputString = null;
        // 将原始包变成bin byte[]
        byte[] binByte = samlRequest.getBytes(StandardCharsets.UTF_8);
        // 将bin byte[]压缩
        byte[] output = new byte[1000];
        Deflater compresser = new Deflater(Deflater.BEST_COMPRESSION, true);
        // 要压缩的数据包
        compresser.setInput(binByte);
        // 完成
        compresser.finish();
        // 压缩，返回的是数据包经过缩缩后的大小
        compresser.deflate(output);
        // 将压缩后的 bin byte[]变为 base64 byte[]
        byte[] base64Input = Base64.encodeBase64(output);
        // 将 base64 byte[]变为base64 String
        String base64String = new String(base64Input, StandardCharsets.UTF_8);
        outputString = base64String;
        return outputString;
    }
}
