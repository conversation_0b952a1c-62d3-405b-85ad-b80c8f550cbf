package com.huafagroup.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 聚合数据手机充值接口错误码
 */

@Getter
@AllArgsConstructor
public enum JuHeErrorCodeEnum {

    ERROR_10001(10001, "错误的请求KEY"),
    ERROR_10002(10002, "该KEY无请求权限"),
    ERROR_10003(10003, "KEY过期"),
    ERROR_10004(10004, "错误的OPENID"),
    ERROR_10005(10005, "应用未审核超时，请提交认证"),
    ERROR_10007(10007, "未知的请求源"),
    ERROR_10008(10008, "被禁止的IP"),
    ERROR_10009(10009, "被禁止的KEY"),
    ERROR_10011(10011, "当前IP请求超过限制"),
    ERROR_10012(10012, "请求超过次数限制"),
    ERROR_10013(10013, "测试KEY超过请求限制"),
    ERROR_10014(10014, "系统内部异常(调用充值类业务时，请务必联系客服或通过订单查询接口检测订单，避免造成损失)"),
    ERROR_10020(10020, "接口维护"),
    ERROR_10021(10021, "接口停用"),

    // 话费
    ERROR_208501(208501, "不允许充值的手机号码及金额"),
    ERROR_208502(208502, "请求手机号和面值查询商品信息失败，请重试"),
    ERROR_208503(208503, "运营商地区维护，暂不能充值"),
    ERROR_208504(208504, "请求手机号和面值查询商品信息错误，具体请参考reason"),
    ERROR_208505(208505, "错误的手机号码"),
    ERROR_208506(208506, "错误的充值金额"),
    ERROR_208507(208507, "充值失败，具体请参考reason"),
    ERROR_208508(208508, "请求充值失败，请重试"),
    ERROR_208509(208509, "错误的订单号"),
    ERROR_208510(208510, "请求订单状态失败，请重试"),
    ERROR_208513(208513, "查询订单失败，具体参照reason说明"),
    ERROR_208514(208514, "不合规范的订单号（8-32位）"),
    ERROR_208515(208515, "校验值sign错误"),
    ERROR_208516(208516, "重复的订单号（近60天订单周期内单号不能重复）（需要自行进行二次确认，是否进行失败处理）"),
    ERROR_208517(208517, "当前账户可用余额不足"),

    // 流量
    ERROR_210501(210501, "错误的手机号码"),
    ERROR_210502(210502, "错误的面值"),
    ERROR_210503(210503, "检索不到符合该手机号码的流量套餐"),
    ERROR_210504(210504, "套餐ID不符合当前手机号"),
    ERROR_210505(210505, "余额不足"),
    ERROR_210506(210506, "校验值sign错误"),
    ERROR_210507(210507, "渠道暂时不可用"),
    ERROR_210508(210508, "重复的订单号（需要自行进行二次确认，是否进行失败处理）"),
    ERROR_210509(210509, "订单生成失败"),
    ERROR_210510(210510, "受理充值失败"),
    ERROR_210511(210511, "订单号错误/不存在"),
    ;


    private final Integer code;
    private final String msg;

    public static String getMsg(Integer code) {

        for (JuHeErrorCodeEnum value : JuHeErrorCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }

    public static Boolean isError(Integer code) {
        for (JuHeErrorCodeEnum value : JuHeErrorCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

}
