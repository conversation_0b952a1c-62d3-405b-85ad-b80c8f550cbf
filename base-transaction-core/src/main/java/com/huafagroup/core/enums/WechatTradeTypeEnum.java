package com.huafagroup.core.enums;

/**
 * 微信支付接口枚举
 */
public enum WechatTradeTypeEnum {
    /**
     * H5支付
     */
    MWEB("MWEB"),
    /**
     * NATIVE
     */
    NATIVE("NATIVE"),
    /**
     * JSAPI
     */
    JSAPI("JSAPI");

    /**
     * 类型
     */
    private final String type;

    WechatTradeTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    @Override
    public String toString() {
        return type;
    }
}
