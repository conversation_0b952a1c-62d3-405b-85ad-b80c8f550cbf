package com.huafagroup.core.enums;

public enum ProjectTypeEnum {

    AREA(0,"area"),
    CITY(1,"city"),
    ORG(2,"org"),
    PROJECT(3,"project"),
    ;
    private Integer code;
    private String tag;

    ProjectTypeEnum(Integer code, String tag) {
        this.code = code;
        this.tag = tag;
    }

    public Integer getCode() {
        return code;
    }

    public String getTag() {
        return tag;
    }

    public static Integer getCodeByTag(String tag) {
        for (ProjectTypeEnum value : values()) {
            if (value.getTag().equals(tag)) {
                return value.getCode();
            }
        }
        return -1;
    }

    public static String getTagByCode(Integer code) {
        for (ProjectTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getTag();
            }
        }
        return null;
    }
}
