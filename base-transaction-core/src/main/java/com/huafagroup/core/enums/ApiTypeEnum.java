package com.huafagroup.core.enums;

/**
 * 支付接口枚举，不止微信，包括其它
 */
public enum ApiTypeEnum {
    /**
     * JS API 下单 API
     */
    JS_API_PAY("/v3/pay/transactions/jsapi"),
    /**
     * H5下单API
     */
    H5_API_PAY("/v3/pay/transactions/h5"),
    /***
     * 统一下单
     */
    PAY_UNIFIEDORDER("/pay/unifiedorder"),
    /**
     * App下单API
     */
    APP_API_PAY("/v3/pay/transactions/app"),
    /**
     * 微信业务订单号查询
     */
    ORDER_QUERY_BY_ID("/v3/pay/transactions/out-trade-no/{}?mchid={}"),
    /**
     * 申请退款
     */
    DOMESTIC_REFUNDS("/v3/refund/domestic/refunds"),
    /**
     * 关闭订单
     */
    CLOSE_ORDER("/v3/pay/transactions/out-trade-no/{}/close"),
    /**
     * 查询单笔退款
     */
    DOMESTIC_REFUNDS_QUERY("/v3/refund/domestic/refunds/{}"),

    /**
     *企业付款
     */
    PROMOTION_TRANSFERS_V2("/mmpaymkttransfers/promotion/transfers"),

    /**
     *现金红包（公众号）
     */
    PROMOTION_SENDREDPACK_V2("/mmpaymkttransfers/sendredpack"),

    /**
     *小程序红包
     */
    PROMOTION_SENDMINIPROGRAMHB_V2("/mmpaymkttransfers/sendminiprogramhb"),

    /**
     *查询现金红包（公众号）领取状态
     */
    PROMOTION_GETHBINFO_V2("/mmpaymkttransfers/gethbinfo"),

    /**
     *园圈付款到零钱
     */
    PROMOTION_TRANSFERS_RUTHUS("/rs-mini-program/pay/sendMoney");

    /**
     * 类型
     */
    private final String type;

    ApiTypeEnum(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    @Override
    public String toString() {
        return type;
    }
}
