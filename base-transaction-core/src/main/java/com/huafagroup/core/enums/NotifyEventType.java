package com.huafagroup.core.enums;

public enum NotifyEventType {

    TRANSACTION_SUCCESS("TRANSACTION.SUCCESS"),

    REFUND_SUCCESS("REFUND.SUCCESS");
    /**
     * 类型
     */
    private final String type;

    NotifyEventType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    @Override
    public String toString() {
        return type;
    }
}
