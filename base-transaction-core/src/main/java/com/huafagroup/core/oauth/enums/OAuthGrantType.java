package com.huafagroup.core.oauth.enums;

public enum OAuthGrantType {
        AUTHORIZATION_CODE("authorization_code"),
        IMPLICIT("implicit"),
        PASSWORD("password"),
        REFRESH_TOKEN("refresh_token"),
        CLIENT_CREDENTIALS("client_credentials"),
        JWT_BEARER("urn:ietf:params:oauth:grant-type:jwt-bearer");

        private String grantType;

        private OAuthGrantType(String grantType) {
            this.grantType = grantType;
        }

        public String toString() {
            return this.grantType;
        }
}
