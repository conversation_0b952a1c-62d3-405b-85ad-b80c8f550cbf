package com.huafagroup.core.oauth.request;

import javax.validation.constraints.NotEmpty;
import java.util.Set;

public class OAuthCodeReq {
    @NotEmpty(message="appId不能为空")
    private String appId;

    @NotEmpty(message="redirectUri不能为空")
    private String redirectUri;

    private Set<String> scope;

    @NotEmpty(message="state不能为空")
    private String state;

    @NotEmpty(message="grantType不能为空")
    private String grantType;

    private String authCode;
    private String userId;
    private String userName;
    private String userAccount;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public Set<String> getScope() {
        return scope;
    }

    public void setScope(Set<String> scope) {
        this.scope = scope;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    @Override
    public String toString() {
        return "OAuthCodeReq{" +
                "appId='" + appId + '\'' +
                ", redirectUri='" + redirectUri + '\'' +
                ", scope=" + scope +
                ", state='" + state + '\'' +
                ", grantType='" + grantType + '\'' +
                ", authCode='" + authCode + '\'' +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", userAccount='" + userAccount + '\'' +
                '}';
    }
}
