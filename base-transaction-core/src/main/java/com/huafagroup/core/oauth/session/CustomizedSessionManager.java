package com.huafagroup.core.oauth.session;

import org.springframework.util.StringUtils;

import java.util.concurrent.ConcurrentHashMap;
import javax.servlet.http.HttpSession;

public class CustomizedSessionManager {
    private final static ConcurrentHashMap<String, HttpSession> sessionMap = new ConcurrentHashMap<String, HttpSession>();

    public static void  set(String key, HttpSession session){
        sessionMap.put(key,session);
    }

    public static void  remove(String key){
        sessionMap.remove(key);
    }

    public static HttpSession get(String key){
        if (StringUtils.isEmpty(key))
            return null;

        return sessionMap.get(key);
    }
}
