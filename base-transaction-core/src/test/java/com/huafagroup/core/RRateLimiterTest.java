package com.huafagroup.core;


import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.TimeUnit;
@Ignore
@RunWith(SpringRunner.class)
@SpringBootTest
public class RRateLimiterTest {


    @Autowired
    RedissonClient client;

    @Test
    public void test() throws Exception {
        RRateLimiter rr = client.getRateLimiter("test1123");
        Assert.assertTrue(rr.trySetRate(RateType.OVERALL, 10, 1, RateIntervalUnit.SECONDS));
        Assert.assertFalse(rr.trySetRate(RateType.OVERALL, 20, 1, RateIntervalUnit.SECONDS));

        for (int j = 0; j < 3; j++) {
            for (int i = 0; i < 10; i++) {
                Assert.assertTrue(rr.tryAcquire());
            }
            for (int i = 0; i < 10; i++) {
                Assert.assertFalse(rr.tryAcquire());
            }
            Thread.sleep(1050);
        }
    }

    /*限流测试*/
    @Test
    public void testTryAcquire() {
        RRateLimiter rr = client.getRateLimiter("acquire1131");
        Assert.assertTrue(rr.isExists());
        rr.delete();

        Assert.assertTrue(rr.trySetRate(RateType.OVERALL, 2, 30, RateIntervalUnit.SECONDS));

        Assert.assertTrue(rr.tryAcquire(1, 1, TimeUnit.SECONDS));
        // Assert.assertFalse(rr.tryAcquire(1, 1, TimeUnit.SECONDS));
        Assert.assertTrue(rr.tryAcquire());
        Assert.assertFalse(rr.tryAcquire());
    }
}
