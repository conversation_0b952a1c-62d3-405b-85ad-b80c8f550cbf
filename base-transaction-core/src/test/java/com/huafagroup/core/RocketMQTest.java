package com.huafagroup.core;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2021-02-20 15:36
 **/

import com.huafagroup.core.configuration.RocketMQConfig;
import org.apache.rocketmq.common.message.Message;

import java.util.ArrayList;
import java.util.List;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.consumer.rebalance.AllocateMessageQueueAveragely;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.consumer.ConsumeFromWhere;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
@Ignore
@RunWith(SpringRunner.class)
@SpringBootTest
@ConfigurationProperties(prefix = "spring.rocketmq.producer")
public class RocketMQTest {

    @Autowired
    DefaultMQProducer producer;

    @Test
    public  void  testOneway() throws  Exception{
        for (int i = 0; i < 1000; i++) {
            //Create a message instance, specifying topic, tag and message body.
            Message msg = new Message("paas-test" /* Topic */,
                    "TagA" /* Tag */,"test"+i,
                    ("Hello RocketMQ " +
                            i).getBytes(RemotingHelper.DEFAULT_CHARSET) /* Message body */
            );
            //Call send message to deliver message to one of brokers.
            producer.sendOneway(msg);

        }
    }


    @Test
    public void testProduct() throws  Exception{

        List<Message> messageList=new ArrayList<Message>();
        for(int i=0;i<5;i++){
            Message msg = new Message("paas-test",// topic
                    "TagA",// tag
                    "OrderID001"+i,// key
                    ("Hello MetaQ").getBytes());// body
            SendResult  sendResult= producer.send(msg);
            System.out.println("i:"+i+",sendResult:"+sendResult);
            messageList.add(msg);
            // Thread.sleep(200);
        }


/*
        Message msg = new Message("TopicTest",// topic
                "TagA",// tag
                "OrderID001",// key
                ("Hello MetaQ").getBytes());// body
        int orderid=12312;
        SendResult  sendResult = producer.send(msg, new MessageQueueSelector() {
            //发送顺序消息，确保queue都是一样的
            @Override
            public MessageQueue select(List<MessageQueue> list, Message message, Object o) {
                Integer id=(Integer) o;
                int index=id % list.size();
                return  list.get(index);
            }
        },orderid);
        //务必打印key、sendResult
        System.out.println("key:"+"OrderID001"+",sendResult:"+sendResult);
        System.out.println();*/
    }

    @Test
    public  void testConsumer() throws  Exception{
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer("consumer-group1", RocketMQConfig.getAclRPCHook(),new AllocateMessageQueueAveragely());
        consumer.setNamesrvAddr("172.16.57.153:9876;172.16.57.154:9876");

        consumer.setInstanceName("0664511");

        consumer.setHeartbeatBrokerInterval(2000);
        consumer.setConsumeFromWhere(ConsumeFromWhere.CONSUME_FROM_LAST_OFFSET);
        consumer.setConsumeMessageBatchMaxSize(3);

        consumer.subscribe("paas-test","*");
        consumer.setMessageModel(MessageModel.CLUSTERING);

        consumer.registerMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {



                for(MessageExt ext : list){
                    try{
                        System.out.println(new String(ext.getBody(),"UTF-8"));
                        System.out.println(ext.getStoreHost().toString());
                        System.out.println(ext.toString());

                    }catch (UnsupportedEncodingException e){
                        e.printStackTrace();
                    }
                }
                return  ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
        consumer.start();
        while (true){}
    }




}
