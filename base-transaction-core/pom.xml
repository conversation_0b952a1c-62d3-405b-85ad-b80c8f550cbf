<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.huafagroup</groupId>
        <artifactId>base-transaction</artifactId>
        <version>1.0.0</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>4.6.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-acl</artifactId>
            <version>4.6.0</version>
        </dependency>


        <!-- 微信支付SDK -->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-apache-httpclient</artifactId>
            <version>0.6.0</version>
        </dependency>

        <!-- 重试机制 -->
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>2.2.0</version>
        </dependency>
        <!-- 原圈交易系统SDK -->
        <dependency>
            <groupId>com.circle</groupId>
            <artifactId>circle-sdk-base</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.circle</groupId>
            <artifactId>circle-sdk-trading</artifactId>
            <version>1.0</version>
        </dependency>

    </dependencies>

    <description>基础核心类库</description>
    <artifactId>base-transaction-core</artifactId>

    <!-- 原圈maven仓库 -->
    <repositories>
        <repository>
            <id>circledevops-yq-archi-circle-api-sdk-java</id>
            <name>circle-api-sdk-java</name>
            <url>https://circledevops-maven.pkg.coding.net/repository/yq-archi/circle-api-sdk-java/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>circledevops-yq-archi-circle-api-sdk-java</id>
            <name>circle-api-sdk-java</name>
            <url>https://circledevops-maven.pkg.coding.net/repository/yq-archi/circle-api-sdk-java/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>